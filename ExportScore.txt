ExportScore.py - CallMiner Score Export Utility
===============================================

OVERVIEW:
---------
ExportScore.py is a specialized utility for exporting CallMiner scores and their configurations from the FEAPI system. This script provides comprehensive score export capabilities with filtering, dependency analysis, and multiple output formats to support backup, migration, and analysis workflows.

PRIMARY PURPOSE:
----------------
- Export score configurations from CallMiner FEAPI
- Analyze score dependencies and relationships
- Generate detailed reports with filtering capabilities
- Create backup copies of score configurations
- Support migration and configuration management workflows
- Provide score usage analysis and documentation

KEY FEATURES:
-------------

1. COMPREHENSIVE SCORE EXPORT:
   - Export all scores or filter by specific criteria
   - Include complete score configurations and metadata
   - Capture scoring algorithms and weightings
   - Export folder organization and hierarchy
   - Include version and generation information

2. FLEXIBLE FILTERING OPTIONS:
   - Filter by score name patterns
   - Filter by folder organization
   - Filter by score type or category
   - Custom filter expressions
   - Date-based filtering for recent changes

3. DEPENDENCY TRACKING:
   - Identifies categories used in score calculations
   - Tracks alert dependencies on scores
   - Maps relationships between scores
   - Analyzes impact of score changes

4. MULTIPLE OUTPUT FORMATS:
   - Excel workbooks with detailed worksheets
   - JSON files for programmatic processing
   - CSV exports for data analysis
   - Human-readable documentation reports

COMMAND LINE INTERFACE:
-----------------------
The script supports extensive command-line configuration:

Basic Usage:
python ExportScore.py

Common Parameters:
--ScoreFilter: Filter scores by name pattern
--FolderFilter: Filter by folder name
--OutputPrefix: Customize output file naming
--LogRoot: Specify log file directory
--ExportDependencies: Include dependency analysis
--ExportJSON: Generate JSON output files
--IncludeInactive: Include inactive/disabled scores

Example Commands:
python ExportScore.py --ScoreFilter "Quality.*" --ExportDependencies True
python ExportScore.py --FolderFilter "Customer Service" --OutputPrefix "CS_Scores"
python ExportScore.py --IncludeInactive False --ExportJSON True

CONFIGURATION:
--------------
Configuration is managed through INI files and command-line arguments:

INI File Structure (ExportScore.py.INI):
[FEAPI]
API = feapi
Tenant = YourTenant
LogRoot = logs
DEBUG = False

[ExportScore]
ScoreFilter = 
FolderFilter = 
OutputPrefix = Scores
ExportDependencies = True
ExportJSON = False
IncludeInactive = True

MAIN FUNCTIONS:
---------------

1. GetScores(helper, config):
   - Retrieves all scores from FEAPI
   - Applies filtering based on configuration
   - Returns filtered score dataset
   - Handles folder organization and hierarchy

2. GetScore(helper, config, score_data):
   - Retrieves detailed data for specific scores
   - Fetches complete score JSON including algorithms
   - Processes scoring criteria and weightings
   - Handles error cases for missing scores

3. ParseScoreFilters(helper, config, df_scores):
   - Analyzes score filter configurations
   - Identifies dependencies on categories and other entities
   - Generates dependency tracking data
   - Creates relationship matrices

4. ExportToExcel(helper, config, data_dict):
   - Creates comprehensive Excel workbooks
   - Multiple worksheets for different data types
   - Professional formatting and styling
   - Score analysis and dependency visualizations

OUTPUT FILES:
-------------

1. Excel Workbook (Scores_[timestamp].xlsx):
   - Scores worksheet: Main score data and configurations
   - Dependencies worksheet: Relationship analysis
   - Algorithms worksheet: Scoring algorithm details
   - Folders worksheet: Folder organization structure
   - Summary worksheet: Export statistics and metrics

2. JSON Files (if enabled):
   - Individual score JSON files with complete configurations
   - Bulk export JSON with all scores
   - Dependency mapping JSON
   - Algorithm analysis JSON

3. CSV Files (if enabled):
   - Score summary data for analysis
   - Dependency relationships
   - Usage statistics

4. Log Files:
   - Main log: Detailed operation log
   - Error log: Error messages and stack traces
   - Warning log: Warning messages and issues

WORKFLOW PROCESS:
-----------------

1. INITIALIZATION:
   - Load configuration from INI file and command line
   - Initialize logging system with multiple handlers
   - Create FEAPI helper instance
   - Validate tenant connection and permissions

2. DATA RETRIEVAL:
   - Fetch all scores from FEAPI with folder information
   - Apply filtering criteria based on configuration
   - Retrieve detailed score configurations
   - Process scoring algorithms and criteria

3. DEPENDENCY ANALYSIS:
   - Analyze score configurations for category references
   - Identify relationships with alerts and other scores
   - Build dependency matrices and impact analysis
   - Calculate usage statistics and metrics

4. EXPORT GENERATION:
   - Create Excel workbook with multiple formatted worksheets
   - Generate JSON files if requested
   - Apply professional formatting and styling
   - Save files with timestamp-based naming

5. CLEANUP AND REPORTING:
   - Display comprehensive export statistics
   - Show any errors or warnings encountered
   - Provide file location information
   - Clean up temporary resources and connections

SCORE DATA ELEMENTS:
--------------------
The export includes comprehensive score information:

- Basic Properties: ID, Name, Description, Folder
- Configuration: Active status, Version, Generation
- Algorithms: Scoring methods, Weightings, Thresholds
- Categories: Referenced categories and their weights
- Filters: Applied filters and conditions
- Metadata: Creation date, Last modified, Owner
- Dependencies: Related scores, alerts, and categories

ERROR HANDLING:
---------------
- Comprehensive error logging with detailed stack traces
- Graceful handling of API timeouts and connection failures
- Validation of score data integrity and completeness
- Recovery from partial export failures
- Clear error messages for troubleshooting and resolution

PERFORMANCE CONSIDERATIONS:
---------------------------
- Efficient batch processing of large score collections
- Optimized API calls to minimize network requests
- Memory-efficient processing of complex score configurations
- Progress indicators for long-running export operations
- Configurable timeout and retry settings for reliability

SECURITY FEATURES:
------------------
- Secure credential management using environment variables
- No sensitive data exposure in log files (configurable)
- Proper authentication token handling and refresh
- Audit trail of export operations for compliance

USE CASES:
----------

1. BACKUP AND RECOVERY:
   - Regular backups of score configurations
   - Disaster recovery preparation and testing
   - Configuration versioning and change tracking

2. MIGRATION SUPPORT:
   - Export from source environment for migration
   - Data preparation for cross-tenant migrations
   - Environment synchronization and updates

3. ANALYSIS AND REPORTING:
   - Score usage analysis and optimization
   - Dependency impact assessment
   - Configuration auditing and compliance

4. DEVELOPMENT AND TESTING:
   - Development environment setup and configuration
   - Test data preparation and validation
   - Configuration comparison and validation

INTEGRATION:
------------
- Works seamlessly with ImportScore.py for round-trip operations
- Integrates with other FEAPI utilities in the suite
- Supports automation and scripting workflows
- Compatible with CI/CD pipelines and DevOps processes

TROUBLESHOOTING:
----------------
Common issues and solutions:
- API connection failures: Verify credentials and network connectivity
- Large dataset timeouts: Adjust timeout settings and use filtering
- Memory issues with large scores: Process in smaller batches
- Permission errors: Verify API user has appropriate read access
- Missing dependencies: Ensure all referenced entities exist

This utility is essential for CallMiner administrators who need to manage, backup, analyze, or migrate score configurations across different environments and tenants.
