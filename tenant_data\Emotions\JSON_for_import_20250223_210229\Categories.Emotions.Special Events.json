{"BucketId": null, "BucketFullname": "", "BucketDescription": "Happy Birthday or Anniversary", "UserEditable": false, "BucketName": "Special Events", "SectionName": "", "SectionNameDisplay": "", "SectionId": 352, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T21:02:13", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T21:02:13", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": "2024-10-01T00:00:00", "EndDate": "2024-12-11T23:59:59", "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Happy Birthday", "Description": null, "ActionString": "[i+see|you+have+birthday:1.5][happy|enjoy|celebrate+birthday:1.5]$OR$", "UserEntry": "(\"happy|enjoy|celebrate birthday\":1.5) OR (\"i see|you have birthday\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Happy Anniversary", "Description": null, "ActionString": "[happy|enjoy|celebrat%+anniversary:2]", "UserEntry": "(\"happy|enjoy|celebrat* anniversary\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T21:02:13", "ComponentCount": 2}