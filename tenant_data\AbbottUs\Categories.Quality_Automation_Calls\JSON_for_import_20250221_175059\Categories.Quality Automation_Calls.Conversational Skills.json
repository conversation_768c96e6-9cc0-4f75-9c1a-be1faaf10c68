{"BucketId": null, "BucketFullname": "", "BucketDescription": "", "UserEditable": false, "BucketName": "Conversational Skills", "SectionName": "", "SectionNameDisplay": "", "SectionId": 58, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 2.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T17:50:56", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T17:50:56", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": "2024-01-15T00:00:00", "EndDate": "2024-01-20T23:59:59", "LastNDays": 0, "TimeFrame": null, "Threshold": 2.0, "SearchComponents": [{"Id": null, "Name": "Contextual Understandability", "Description": null, "ActionString": "(quality automation_calls.contextual understandability)$NOT$", "UserEntry": "-(CAT:[Quality Automation_Calls.Contextual Understandability])", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Politeness", "Description": null, "ActionString": "(quality automation_calls.politeness)", "UserEntry": "CAT:[Quality Automation_Calls.Politeness] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T17:50:56", "ComponentCount": 2}