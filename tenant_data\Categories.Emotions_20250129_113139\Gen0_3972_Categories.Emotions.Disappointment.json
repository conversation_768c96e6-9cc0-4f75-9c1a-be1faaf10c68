{"BucketId": "3972", "BucketFullname": "Categories.Emotions.Disappointment", "BucketDescription": "Detects when disappointment is present on a call.", "UserEditable": false, "BucketName": "Disappointment", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Disappointment", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2023-05-04T11:02:16.023-04:00", "Creator": "<EMAIL>", "Version": 3, "LanguageTag": "en", "EffectiveDate": "2023-05-04T11:02:22.423", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -24030, "Name": "Disappointment Al<PERSON>", "Description": "", "ActionString": "[very|so|absolutely|extra|especially|quite|really|super|extremely|highly+cheated|disappointment|disappointed|remorse|bummer|stinks|bummed|sad|cranky|whiny|mushy|grievance|sadness|depressed|devastated|distraught|emotional|mistake:0.5]", "UserEntry": "\"very|so|absolutely|extra|especially|quite|really|super|extremely|highly cheated|disappointment|disappointed|remorse|bummer|stinks|bummed|sad|cranky|whiny|mushy|grievance|sadness|depressed|devastated|distraught|emotional|mistake\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24029, "Name": "negative_sentiment-disappointed", "Description": "", "ActionString": "[not+good|pleased:1][super+bombed:1][incredibly|huge|extremely+disappoint%:1.5]<rubbish><bummed><stinks><disappointed><bummer><disappoint%><remorse><cheated>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((cheated|remorse|disappoint*|bummer|disappointed|stinks|bummed|rubbish) OR (\"incredibly|huge|extremely disappoint*\":1.5) OR (\"super bombed\":1) OR (\"not good|pleased\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24028, "Name": "negative_sentiment-negative", "Description": "", "ActionString": "[only|consider%+negative:1][possible+impact:1]<negative%>$OR$$OR$", "UserEntry": "((negative*) OR (\"possible impact\":1) OR (\"only|consider* negative\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24027, "Name": "negative_sentiment-problem", "Description": "", "ActionString": "[no|not|isn`t|didn`t+help%:0.8][biggest+thing:1]<issue><concern%><hectic><stuck%><stupid><upset><hurt><trouble><dead><pain><problem%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((problem*|pain|dead|trouble|hurt|upset|stupid|stuck*|hectic|concern*|issue) OR (\"biggest thing\":1) OR (\"no|not|isn't|didn't help*\":0.8))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24026, "Name": "negative_sentiment-sad", "Description": "", "ActionString": "[not+happy:1]<miserable><emotional><distraught><sad><devasta%><depressed><sadness><grievance><mushy><whiny><cranky>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((cranky|whiny|mushy|grievance|sadness|depressed|devasta*|sad|distraught|emotional|miserable) OR (\"not happy\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24025, "Name": "negative_sentiment-sorry", "Description": "", "ActionString": "[mean+to+interrupt:1.2][being+patient|honest:1][cut+off:1]<disturb%><removing><interrupt%><apology><apologies><goodness><bother%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((bother*|goodness|apologies|apology|interrupt*|removing|disturb*) OR (\"cut off\":1) OR (\"being patient|honest\":1) OR (\"mean to interrupt\":1.2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24024, "Name": "negative_sentiment-regret", "Description": "", "ActionString": "[nobody+knew|told|explain%:1.5][wish+would%|knew|known|noun|told:1][never+knew:1][made+mistake:1][asked+him|her|them:1][didn`t+even|realize:1][don’t+recall:1][wish+you|i|we|they:1][thought|remember+ask%:1]<regret%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((regret*) OR (\"thought|remember ask*\":1) OR (\"wish you|i|we|they\":1) OR (\"don’t recall\":1) OR (\"didn`t even|realize\":1) OR (\"asked him|her|them\":1) OR (\"made mistake\":1) OR (\"never knew\":1) OR (\"wish would*|knew|known|noun|told\":1) OR (\"nobody knew|told|explain*\":1.5))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-05-04T11:02:22.423", "ComponentCount": 7}