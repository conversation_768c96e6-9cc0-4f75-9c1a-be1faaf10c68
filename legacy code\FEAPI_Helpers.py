import os
import re
import requests
import getpass
import json
from openpyxl import load_workbook
import pandas as pd

class FEAPI_Helper:

    token = None

    def __init__(self, name,logger, API):
        self.name = name
        self.logger = logger
        self.API = API
        self.GetTokenFromFile()

    @staticmethod
    def get_windows_user_id():
        #Gets the current Windows user ID (username).
        try:
            return getpass.getuser()
        except Exception as e:
            print(f"Error getting username: {e}")
            return None

    def cleanfilename(self,dirtyfilename: str, replacement:str="_"):
        """
        Cleans a string to be a valid Windows file path by replacing characters
        not allowed in file or folder names with a specified replacement character.

        Args:
            path_string: The string to clean.
            replacement: The character to replace invalid characters with.  Defaults to "_".

        Returns:
            The cleaned string.  Returns an empty string if the input is None or empty.
        """

        if not dirtyfilename:  # Check for None or empty string
            return ""

        invalid_chars = r'[<>:"/\\|?*]'  # Regular expression for invalid characters
        cleaned_string = re.sub(invalid_chars, replacement, dirtyfilename)

        # Check for reserved names (case-insensitive) and append an underscore if found.
        reserved_names = ["CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
                          "LPT1", "LPT2", "LPT3", "LPT4"]

        if cleaned_string.upper() in reserved_names:
            cleaned_string += "_"  # Append underscore to avoid conflicts

        # Check if the name is just a space or period
        if cleaned_string.strip() == "." or cleaned_string.strip() == "":
            cleaned_string = replacement  # Replace with replacement char

        return cleaned_string

    def load_json_from_file(self, filename):
        try:
            with open(filename, 'r') as file:
                data = json.load(file)
                return data
        except FileNotFoundError:
            self.logger.error(f"Error: The file '{filename}' was not found.")
        except json.JSONDecodeError:
            self.logger.error(f"Error: The file '{filename}' is not a valid JSON file.")
        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {e}")

    def move_last_sheet_to_front(self,file_path):
        # Load the Excel workbook
        workbook = load_workbook(file_path)

        # Get the names of all sheets in the workbook
        sheet_names = workbook.sheetnames

        # Get the last sheet and its index
        last_sheet = workbook[sheet_names[-1]]
        last_sheet_index = workbook.sheetnames.index(sheet_names[-1])

        # Move the last sheet to the front
        workbook.move_sheet(last_sheet, offset=-last_sheet_index)

        # Save the modified workbook
        workbook.save(file_path)


    def GetNextSheetName(self, xls_filename:str, sheet_name):
        # Load the Excel file
        excel_file = load_workbook(xls_filename)

        # Get the list of sheet names
        sheet_names = excel_file.sheetnames
        i=0
        while sheet_name in sheet_names:
            sheet_name = f"{sheet_name[:28]}{i}"
            i+=1
            if i>=100:
                self.logger.error(f"Unable to get unique sheetname for Category ({sheet_name})")
                exit(1)

        return sheet_name


    def AppenddataframeAsSheetToExcel(self,xls_filename:str, sheet_name, df):
        try:
            sheet_name = sheet_name[:31]
            #Make sure sheet doesn't already exist
            sheet_name = self.GetNextSheetName(xls_filename,sheet_name)

            # Try to open the Excel file in append mode
            with pd.ExcelWriter(xls_filename, mode='a', engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                self.logger.info(
                    f"Appended DataFrame as sheet '{sheet_name}' to existing Excel file '{xls_filename}'.")
        except FileNotFoundError:
            # If the Excel file does not exist, create a new file and write the DataFrame as the first sheet
            df.to_excel(xls_filename, sheet_name=sheet_name, index=False)
            self.logger.info(f"Created Excel file '{xls_filename}' with DataFrame as sheet '{sheet_name}'.")

    def append_filterrow_to_excel(self,bucket_fullname:str, search_component_name, filter_string, filename='filters.xlsx', filterstringcolumnname = "FilterString"):
        """Appends a row to an Excel file with specified columns."""
        # Define the column names
        columns = ['BucketFullname', 'SearchComponentName', filterstringcolumnname]

        # Create a DataFrame with the new data
        new_data = pd.DataFrame([[bucket_fullname, search_component_name, filter_string]], columns=columns)

        # Check if the file exists and read the existing data
        if os.path.exists(filename):
            existing_data = pd.read_excel(filename)
            # Concatenate the existing data with the new data
            updated_data = pd.concat([existing_data, new_data], ignore_index=True)
        else:
            # If the file does not exist, use the new data as the DataFrame
            updated_data = new_data

            # Write the updated DataFrame back to the Excel file, including headers
        updated_data.to_excel(filename, index=False, header=True)

    def LogFilter(self, bucketfullname:str, searchcomponent, filterstring, xls_filename:str):
        # add filter to output
        self.append_filterrow_to_excel(bucketfullname, searchcomponent, filterstring, xls_filename)

    def LogEQLDependency(self,bucketfullname:str,searchcomponent,depliststring,xlsfilename:str):
        # add filter to output
        self.append_filterrow_to_excel(bucketfullname,searchcomponent,depliststring, xlsfilename, "DependsOn")

    def GetCategoryFullname(self,BucketID:int,df_categories):
        #Get BucketFullname from df
        try:
            result = df_categories.loc[df_categories['BucketId'] == BucketID, 'BucketFullname'].iloc[0]
            return result
        except IndexError:  # No matching row found.
            return f"BucketId_NOT_FOUND"
        except KeyError as e:  # bucketID or BucketFullname column doesn't exist.
            return f"BucketId or BucketFullname Column missing {e}"

    def GetBucketID(self,BucketFullname:str,df_categories):
        #Return BucketID for this Fullname
        try:
            result = df_categories.loc[df_categories['BucketFullname'] == BucketFullname, 'BucketId']
            if not result.empty:
                result = result.iloc[0]
                return result
            self.logger.warning(f"BucketId_NOT_FOUND for BucketFullname={BucketFullname}")
            return None
        except Exception as e:  # No matching row found.
            self.logger.warning(f"BucketId_NOT_FOUND for BucketFullname={BucketFullname}\tException = {e}")
            return None

    def ParseFilters(self, filters, df_categories):
        # Extracting and formatting the output
        formatted_output = []
        deplist = []

        for f in filters:
            column_name = f['ColumnName']
            operator = f['Operator']
            operand1 = f['Operand1']
            operand2 = f['Operand2']
            t = f"{column_name} {operator} '{operand1}'"
            if operand2 != "":
                t = t+f" and {operand2}"
            if column_name == None:
                sectionid = f['SectionID']
                scoreid = f['ScoreID']
                alertid = f['AlertID']
                bucketid = f['BucketID']
                if bucketid!=None:
                    bucketfullname = self.GetCategoryFullname(bucketid, df_categories)
                    t = f"Filtered by Bucketid({f['BucketID']} = {bucketfullname})"
                    deplist.append(f['BucketID'])
                elif sectionid!=None:
                    t=f"Filtered by SectionID({sectionid})"
                elif scoreid != None:
                    t = f"Filtered by ScoreID({scoreid})"
                elif alertid != None:
                    t = f"Filtered by AlertID({alertid})"

            formatted_output.append(t)


            # Joining the output into a single string
        result = ' AND '.join(formatted_output)
        return result,deplist



    def extract_cat_from_pattern(self,text, df_categories):
        """
        Extracts the pattern "CAT:[...]" from a string using regex.

        Args:
            text: The input string.

        Returns:
            A list of matching strings, or an empty list if no matches are found.
        """
        if text ==None or text =="":
            return "",[]

        #Look for CAT language
        pattern = r"CAT:\[[^\]]+\]"  # Raw string for regex
        try:
            matches = re.findall(pattern, text)
        except Exception as e:
            self.logger.error(f"Unable to search text({text}) for Categories. Exception = {e}")
            return "",[]

        #OTherwise continue
        bucketidlist = []

        for m in matches:
            #Get BucketID
            bucketid = self.GetBucketID(m[5:-1], df_categories)
            if bucketid is not None:
                bucketidlist.append(bucketid)
            else:
                self.logger.warning(f"Unable to find the BucketID for Match({m}) in UserEntry({text})")

        return ",".join(matches), bucketidlist



    # Get token from file
    def GetTokenFromFile(self):
        try:
            with open("token.txt", "r") as file:
                self.token = file.read().strip()
        except FileNotFoundError:
            self.logger.error("Error: 'token.txt' file not found.")
            exit(1)

        return self.token

    # Update the token file with a new token
    def write_token_to_file(self, newtoken:str):
        if newtoken != self.token:
            self.token = newtoken
            try:
                with open("token.txt", "w") as file:
                    file.write(self.token)
                    #print("Token written to token.txt successfully.")
            except Exception as e:
                self.logger.error(f"Error writing token to file: {e}")

    # retrieve json from API or handle error
    def ApiGetJson(self,url: str):
        responsejson = None
        api_response = requests.get(url, headers={'Authorization': f'JWT {self.token}'})
        status_code = api_response.status_code
        if status_code == 200:
            responsejson = api_response.json()
            newtoken = api_response.headers['auth-token-updated']
            self.write_token_to_file(newtoken)
        else:
            self.logger.error(f"ApiGet FAILED with statuscode({status_code}) = {api_response.content})")
            exit(1)
        return responsejson


    # retrieve json from API or handle error
    def ApiPostJson(self,url: str, payload):
       headers = {'Authorization': f'JWT {self.token}', 'Content-Type': 'application/json'}

       # Make the POST request
       api_response = requests.post(url, headers=headers, json=payload)
       status_code = api_response.status_code
       if status_code == 200:
           responsejson = api_response.json()
           newtoken = api_response.headers['auth-token-updated']
           self.write_token_to_file(newtoken)
       else:
           self.logger.error(f"ApiPost FAILED with statuscode({status_code}) = {api_response.content})")
           return None
       return responsejson

    def GetAllCategories(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/categories')
        return pd.json_normalize(responsejson)

    def GetAllSections(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/sections/category')
        #remove Groups
        for s in responsejson:
            s['Groups'] =""
        return pd.json_normalize(responsejson)

    def GetTenantName(self, API):
        #Get Tenant Name
        responsejson = self.ApiGetJson(f"https://{API}.callminer.net/api/v2/profile")
        if responsejson != None:
            #Get Tenant name
            tenant = responsejson["TenantName"]
        return tenant

    def GetUsedInString(self,bucketid:int, API:str):
        UsedInList = []
        usedinstring = ""

        try:
            requesturl = f'https://{API}.callminer.net/api/v2/categories/{bucketid}/dependencies'
            responsePayload = self.ApiGetJson(requesturl)
            for d in responsePayload:
                if d['Type'] == "ScoreComponent" or d['Type'] =="CateogryComponent":
                    usedin = f"({d['Type']}: {d['DependenciesContentName']}.{d['Name']})"
                else:
                    usedin = f"({d['Type']}: {d['Name']})"
                if usedin !="" and usedin!= None and usedin not in UsedInList:
                    UsedInList.append(usedin)
            if len(UsedInList)>0:
                usedinstring = ",".join(UsedInList)
        except Exception as e:
            self.logger.error(f"ERROR retrieving dependencies for Bucketid({bucketid}).  Exception = {e}")

        return usedinstring

    def show_error_file_contents(self, handler, handlername):
        logger = self.logger
        filename = handler.baseFilename
        if os.path.exists(filename):
            try:
                with open(filename, "r") as f:
                    contents = f.read()
                    logger.info(
                        "---------------------------------------------------------------------------------------")
                    if contents == "":
                        logger.info(f"No {handlername} ENCOUNTERED")
                    else:
                        logger.info(f"{handlername} ENCOUNTERED - Saved to {handler.baseFilename}")
                        logger.info(
                            ".......................................................................................")
                        logger.info(contents)
                        logger.info(
                            "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^")

            except FileNotFoundError:
                print(f"{handlername} File not found: {filename}")
            except Exception as e:
                logger.error(f"An error occurred while reading the {handlername} file: {e}")
        else:
            logger.error(f"{handler} File {filename} does not exist.")

