{"BucketId": null, "BucketFullname": "", "BucketDescription": "Identifies standard calls where the agent offered further help to the customer. SBB/Filters Included - Yes (Agent Group=Abbott_US_Eng_Voice, Quality Automation_Calls - Exclusions). Speakers - Agent speaker added for some strings in the syntax.", "UserEditable": false, "BucketName": "Further Questions", "SectionName": "", "SectionNameDisplay": "", "SectionId": 2308, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T12:01:53", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T12:01:53", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Further Questions", "Description": null, "ActionString": "[dancing+also+neck:1.2]=1!-45[concern+for+today:2]=1!-45{you+with+today:1.5}=1!-45[tell+me+some+of:2.5]=1!-45{%thing+more|else:1.2}=1!-45$OR$$OR$$OR$$OR$[that`s|that|it`s|it+all|good|fine|everything:1.4]!-30%<transfer%>!-30%<mam>!-30%<madam>!-30%<sir>!-30%<force>!-30%<parcel>!-30%<beside%>!-30%<apart>!-30%<survey>!-30%<nice>!-30%<lovely>!-30%<alright>!-30%<welcome>!-30%<appreciate>!-30%<thank%>!-30%<help>!-30%<assist%>!-30%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTNEAR:2$$OR$<anything>!-30%$NEAR:5$[is+this+the+only+concern:1.4]!-30%[no+more+question%|doubt%|concern%:1.6]!-30%[if+more|no+concern%|question%|query|queries|request%|inquir%|enquir%|doubt%:1.8]!-30%<nurse%>!-30%<specialist%>!-30%<department>!-30%<transfer%>!-30%$OR$$OR$$OR${assist%|help%+further|today:2.5}!-30%$NOTNEAR:15$[like+me+help:1.5]!-30%<escalat%>!-30%<case>!-30%<superior%>!-30%<manager%>!-30%<supervisor%>!-30%<nurse%>!-30%<specialist%>!-30%<transfer%>!-30%$OR$$OR$$OR$$OR$$OR$$OR$$OR$[assist%|help+you+with:1.5]!-30%$NOTNEAR:7$[can|could|said|anything|something|nothing+do+for+you:2]!-30%[take+information:1.2]!-30%[before+you+go|know|want%:1.5]!-30%$NOTNEAR:2$<i`ll>!-30%<i>!-30%$OR$[question%+like|want|would+ask:2]!-30%$NOTNEAR:0.5$<address>!-30%[parcel|force+before+you:4]!-30%$NOTNEAR:3$[for+you+before+finish%|end%:2.2]!-30%[question%|ask|and|pacel|force+before+end%|finish%|call:1.4]!-30%[all+you|your+concern%|question%|query|queries|request%|inquir%|enquir%|doubt%:1.4]!-30%[other+one:1]=1!-30%[i+have:1]=1!-30%$OR$<caller>!-30%$OR$[have|any|further|additional|%other+concern%|question%|query|queries|request%|inquir%|enquir%|doubt%:1.8]!-30%$NOTNEAR:1.5$[any%|nothing|something+other|else+question%|problem%|issue%|enquiry|request|doubt%|concern%:2]!-30%[help|assist+further|more:1.4]!-30%{anything|everything+you|you`d|maybe+want%|need%|ask:1.5}!-30%[anything|something|else+you|would+like:1.4]!-30%[i+could|can+hope|help:1.6]!-30%[is|would|if|maybe+there|there`s+any|%thing|else:1.2]!-30%<phone>!-30%<error>!-30%<treatement>!-30%<medical>!-30%$OR$$OR$$OR$[any|%thing+other|else|more+help|assist%|do|deal|today|hope:2]!-30%$NOTNEAR:5$[anything+else:1.5]!-30%{help|there|there`s+today|anything:2}!-30%[else|%thing+aside|apart+this:2]!-30%[is+there+else:2]!-30%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"is there else\":2 OR \"else|*thing aside|apart this\":2\nOR [help|there|there's today|anything]:2 \nOR \"anything else\":1.5 \nOR \"any|*thing other|else|more help|assist*|do|deal|today|hope\":2 NOT NEAR:5 (medical|treatement|error|phone)\nOR \"is|would|if|maybe there|there's any|*thing|else\":1.2 \nOR \"i could|can hope|help\":1.6 \nOR \"anything|something|else you|would like\":1.4\nOR [anything|everything you|you'd|maybe want*|need*|ask]:1.5\nOR \"help|assist further|more\":1.4 \nOR \"any*|nothing|something other|else question*|problem*|issue*|enquiry|request|doubt*|concern*\":2\nOR \"have|any|further|additional|*other concern*|question*|query|queries|request*|inquir*|enquir*|doubt*\":1.8 NOT NEAR:1.5 ((caller) OR (\"i have\":1 OR \"other one\":1)=agent)\nOR \"all you|your concern*|question*|query|queries|request*|inquir*|enquir*|doubt*\":1.4\nOR \"question*|ask|and|pacel|force before end*|finish*|call\":1.4\nOR \"for you before finish*|end*\":2.2                                                                                                                   \nOR \"parcel|force before you\":4 NOT NEAR:3 (address)\nOR \"question* like|want|would ask\":2 NOT NEAR:0.5 (i|i'll) \nOR \"before you go|know|want*\":1.5 NOT NEAR:2 \"take information\":1.2\nOR \"can|could|said|anything|something|nothing do for you\":2 \nOR \"assist*|help you with\":1.5 NOT NEAR:7 (transfer*|specialist*|nurse*|supervisor*|manager*|superior*|case|escalat*)  \nOR \"like me help\":1.5 \nOR [assist*|help* further|today]:2.5 NOT NEAR:15 (transfer*|department|specialist*|nurse*) \nOR \"if more|no concern*|question*|query|queries|request*|inquir*|enquir*|doubt*\":1.8 \nOR \"no more question*|doubt*|concern*\":1.6 OR \"is this the only concern\":1.4\nOR (anything) NEAR:5 ((assist*|help|thank*|appreciate|welcome|alright|lovely|nice|survey|apart|beside*|parcel|force|sir|madam|mam) NOT NEAR:2 (transfer*) OR \"that's|that|it's|it all|good|fine|everything\":1.4)){-30%} \nOR ([*thing more|else]:1.2 \nOR \"tell me some of\":2.5 \nOR [you with today]:1.5 \nOR \"concern for today\":2 \nOR \"dancing also neck\":1.2)=agent{-45s}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Autopass - Unnatural Call Endings", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T12:01:53", "ComponentCount": 2}