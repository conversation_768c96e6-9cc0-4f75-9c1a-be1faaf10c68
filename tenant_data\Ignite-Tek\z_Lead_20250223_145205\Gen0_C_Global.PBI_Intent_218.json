{"BucketId": "218", "BucketFullname": "Categories.C_Global.PBI_Intent", "BucketDescription": "new benefits and allowances only come with new plans you`d obviously be open to changing your plan to get better benefits", "UserEditable": false, "BucketName": "PBI_Intent", "SectionName": "C_Global", "SectionNameDisplay": "C_Global", "SectionId": 69, "RetroStartDate": "2024-10-02T00:00:00", "CategoryDisplay": "C_Global.PBI_Intent", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-18T15:08:06.413-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-02T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-10-17T00:00:00", "EndDate": "2024-10-17T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -31313, "Name": "Intent TEST", "Description": null, "ActionString": "[plan+to+get+benefits+and+allowances+right:5][you%+obvious%+be+open+to+changing:5]$AND$[allowanc%+only+come+with+new+plan%:5][because+these+additional+benefit%:5]$AND$$NEAR:5$", "UserEntry": "(\"because these additional benefit*\":5 AND \"allowanc* only come with new plan*\":5) NEAR:5 (\"you* obvious* be open to changing\":5  \"plan to get benefits and allowances right\":5)", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31270, "Name": "MEET SPECIALIST and XFER CLOSER", "Description": null, "ActionString": "[you%|obvious%+open|chang%|plan%+better|new%|extra||benefi%|increas%|high%|allowanc%:6][addition%|new|extra|incres%|high%|allowanc%|updat%|best|benefi%+only|new|improv%|plan%:6][addition%|new|extra|incres%|high%|allowanc%|updat%|best+benefi%|plan%|allowanc%+only|new|improv%|plan%:0.8]$OR$$BEFORE:7$", "UserEntry": "(\"addition*|new|extra|incres*|high*|allowanc*|updat*|best benefi*|plan*|allowanc* only|new|improv*|plan*\" OR \"addition*|new|extra|incres*|high*|allowanc*|updat*|best|benefi* only|new|improv*|plan*\":6) BEFORE:7 (\"you*|obvious* open|chang*|plan* better|new*|extra||benefi*|increas*|high*|allowanc*\":6)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-06T12:55:16.533", "ComponentCount": 2}