{"BucketId": "4244", "BucketFullname": "Categories.Emotions-mike.^Customer Advocacy - Emotions Index", "BucketDescription": "tuned for good news in script", "UserEditable": false, "BucketName": "^Customer Advocacy - Emotions Index", "SectionName": "Emotions-mike", "SectionNameDisplay": "Emotions-mike", "SectionId": 2326, "RetroStartDate": null, "CategoryDisplay": "Emotions-mike.^Customer Advocacy - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T20:46:02.29-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T20:44:41", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -22211, "Name": "Love Service", "Description": null, "ActionString": "<enjoying><loving><enjoy><love>$OR$$OR$$OR$<product><service>$OR$$NEAR:2$", "UserEntry": "(service|product) NEAR:2 (love|enjoy|loving|enjoying)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22210, "Name": "Use all the time", "Description": null, "ActionString": "[use+constantly:0.5][i+really+used+it:1.2][use+a+lot:1][use+every+day:1.2][use+all+time:1]$OR$$OR$$OR$$OR$", "UserEntry": "\"use all time\":1 OR \"use every day\":1.2 OR \"use a lot\":1 OR \"i really used it\":1.2 OR \"use constantly\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22209, "Name": "Fantastic Service-Product", "Description": null, "ActionString": "(emotions.extremely positive language)<product><service>$OR$$NEAR:5$", "UserEntry": "(service|product) NEAR:5 (CAT:[Emotions.Extremely Positive Language])", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22208, "Name": "Appreciate-ThankYou-Not Near Closing", "Description": null, "ActionString": "(emotions.closinglanguage)<not>$BEFORE:30$(emotions.compliments)!0-50%$AND$", "UserEntry": "CAT:[Emotions.Compliments]{0-50%} Not BEFORE:30 (CAT:[Emotions.ClosingLanguage])", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22207, "Name": "good news", "Description": null, "ActionString": "[good+news:0.5]", "UserEntry": "\"good news\"", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22206, "Name": "great news", "Description": null, "ActionString": "[to+hear:0.5](emotions.extremely positive language)$BEFORE:1$[good|great|happy|wonderful|rare|glad+to+hear:1]<news>(emotions.extremely positive language)$BEFORE:1$$OR$$OR$", "UserEntry": "( (CAT:[Emotions.Extremely Positive Language]) BEFORE:1 (news) ) OR (\"good|great|happy|wonderful|rare|glad to hear\":1 ) OR ( (CAT:[Emotions.Extremely Positive Language]) BEFORE:1 (\"to hear\") )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T20:46:03.883", "ComponentCount": 6}