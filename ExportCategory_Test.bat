@echo off
REM ExportCategory_Test.bat - Debug Launch Script for ExportCategory.py
REM This script launches ExportCategory.py with specific test parameters for debugging

echo Starting ExportCategory.py with test parameters...
echo.
echo Parameters:
echo   API: feapi
echo   Tenant: cmbia
echo   BucketFullnameFilter: ^Emotions\.
echo   GetDependencies: True
echo   IgnoreFilterDependencies: False
echo.

python ExportCategory.py --API=feapi --Tenant=cmbia --BucketFullnameFilter="^Emotions\." --GetDependencies=True --IgnoreFilterDependencies=False

echo.
echo ExportCategory.py execution completed.
echo Check the output files and logs for results.
pause
