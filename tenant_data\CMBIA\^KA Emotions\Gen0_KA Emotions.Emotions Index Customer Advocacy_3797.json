{"BucketId": "3797", "BucketFullname": "Categories.KA Emotions.Emotions Index Customer Advocacy", "BucketDescription": "Words and phrases that indicate customer is advocate or promoter", "UserEditable": false, "BucketName": "Emotions Index Customer Advocacy", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotions Index Customer Advocacy", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:30:52.313-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-02-24T13:21:38.16", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25454, "Name": "Compliments**", "Description": null, "ActionString": "(customer experience.compliments)", "UserEntry": "CAT:[Customer Experience.Compliments]", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25453, "Name": "Fantastic Service", "Description": null, "ActionString": "{fantastic|amazing|awesome|incredible+service%:1}", "UserEntry": "[fantastic|amazing|awesome|incredible service*]:1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-02-24T13:21:38.16", "ComponentCount": 2}