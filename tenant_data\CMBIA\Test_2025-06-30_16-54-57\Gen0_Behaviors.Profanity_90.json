{"BucketId": "90", "BucketFullname": "Categories.Behaviors.Profanity", "BucketDescription": null, "UserEditable": false, "BucketName": "Profanity", "SectionName": "Behaviors", "SectionNameDisplay": "Behaviors", "SectionId": 42, "RetroStartDate": null, "CategoryDisplay": "Behaviors.<PERSON><PERSON><PERSON>", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": null, "Creator": null, "Version": null, "LanguageTag": "Unknown", "EffectiveDate": "2015-09-28T11:51:00", "SlnInstallationID": null, "SemanticType": "EQL", "IsOutreach": false, "Username": "CDE\\courtney.lawton", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -30459, "Name": "Crap", "Description": "Dissatisfaction", "ActionString": "[all|with|through+this+crap:1.2][bunch|load|piece+crap:1]$OR$<other>[all|with|through+this+crap:0.8][bunch|load|piece+crap:1]$OR$$NOTNEAR:0$$OR$", "UserEntry": "(\"bunch|load|piece crap\":1 OR \"all|with|through this crap\":0.8) NOT NEAR:0 (other) OR (\"bunch|load|piece crap\":1 OR \"all|with|through this crap\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30458, "Name": "F word", "Description": "Dissatisfaction", "ActionString": "[what+the+fuck:1][stop|quit+fucking:1][fucking+issue%|problem%|wrong:1][mother+doctor%|fuck%:0.6][no+fuck%+way:0.8][that|this+is+fucked+up:0.8][go+fuck+off|your%:0.8][fuck+this|that+noise|shit:0.8][it|it`s+fuck%+crazy:0.8][fucking+absurd|bull%|insane|insanity|nuts|outrageous|ridiculous:0.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"fucking absurd|bull*|insane|insanity|nuts|outrageous|ridiculous\" OR \"it|it's fuck* crazy\":0.8 OR \"fuck this|that noise|shit\":0.8 OR \"go fuck off|your*\" OR \"that|this is fucked up\":0.8 OR \"no fuck* way\" OR \"mother doctor*|fuck*\":0.6  OR \"fucking issue*|problem*|wrong\":1 OR \"stop|quit fucking\":1 OR \"what the fuck\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30457, "Name": "<PERSON><PERSON>", "Description": "Dissatisfaction", "ActionString": "[get%|going|gonna|basically+screwed+over:0.8][screw%+me+over:0.6][he|she|someone|somebody|you|totally|they+screwed|screw+up:0.6]$OR$$OR$", "UserEntry": "(\"he|she|someone|somebody|you|totally|they screwed|screw up\":0.6 OR \"screw* me over\":0.6 OR \"get*|going|gonna|basically screwed over\":0.8)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30456, "Name": "Bullsh*t", "Description": "Dissatisfaction", "ActionString": "[bunch|complete|load|this|that%|total|ton|absolute|pile+bull|it`s|its+shit|crap:0.8][bunch|complete|load|this|that%|total|absolute|ton|pile|its|it`s+bullshit:0.8]$OR$", "UserEntry": "(\"bunch|complete|load|this|that*|total|absolute|ton|pile|its|it's bullshit\":0.8 OR \"bunch|complete|load|this|that*|total|ton|absolute|pile bull|it's|its shit|crap\":0.8)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30455, "Name": "Sh*t", "Description": null, "ActionString": "[that`s|really|very+shitty:1][shit+out+of+me:1][don`t|not|wouldn`t+give+shit:1.5][shitty+service%|product|people|answer%:0.8][piece|pieces|jack|bunch|stupid|pile+shit:1][i|she|he|just|really+don`t|not|doesn`t+get|give+shit:1]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"I|she|he|just|really don't|not|doesn't get|give shit\":1 OR \"piece|pieces|jack|bunch|stupid|pile shit\":1 OR \"shitty service*|product|people|answer*\":0.8 OR \"don't|not|wouldn't give shit\":1.5 OR \"shit out of me\":1 OR \"that's|really|very shitty\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30454, "Name": "B*tch", "Description": null, "ActionString": "[stop|quit+bitch%:1][acting|act+like+bitch:0.9][don`t|no|not+act|acting|mean|trying+bitch%:1.2][bitch%+about:0.7][absolute|complete|crazy|dirty|dumb|fucking|need|son|stupid|total|useless+bitch%:0.7][you|your|you`re+a|are|being|little+bitch|witch:0.7]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"you|your|you're a|are|being|little bitch|witch\":0.7 OR \"absolute|complete|crazy|dirty|dumb|fucking|need|son|stupid|total|useless bitch*\":0.7 OR \"bitch* about\":0.7 OR \"don't|no|not act|acting|mean|trying bitch*\":1.2 OR \"acting|act like bitch\":0.9 OR \"stop|quit bitch*\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30453, "Name": "A**hole", "Description": null, "ActionString": "[he|he`s|pretty|she`s|she|such|you|you`re|your+are|an|being|just|much+asshole%:1.2][complete+asshole:0.5]{call%|fucking|list|reach%|told+asshole%:1}$OR$$OR$", "UserEntry": "([call*|fucking|list|reach*|told asshole*]:1 OR \"complete asshole\" OR \"he|he's|pretty|she's|she|such|you|you're|your are|an|being|just|much asshole*\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30452, "Name": "Pi**ed me off", "Description": null, "ActionString": "[piss%|tick%+me+off:0.8][pissed|ticked+about|already|because|but|that|when|with:0.5][absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that`s|very|was|were|i`m|we`re|more+pissed|ticked+off:1]$OR$$OR$", "UserEntry": "(\"absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that's|very|was|were|i'm|we're|more pissed|ticked off\":1 OR \"pissed|ticked about|already|because|but|that|when|with\":0.5 OR \"piss*|tick* me off\":0.8)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30451, "Name": "G*Damn", "Description": null, "ActionString": "[god+damn+thing|thing`s|day:1][my|this+god+damn:1.2][goddamn+thing|thing`s|day:1][my|this+goddamn:1.2]$OR$$OR$$OR$", "UserEntry": "(\"my|this goddamn\":1.2 OR \"goddamn thing|thing`s|day\":1 OR \"my|this god damn\":1.2 OR \"god damn thing|thing`s|day\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30450, "Name": "Hell", "Description": null, "ActionString": "[what+the+hell:1][go+to+hell:1][can+go+hell:1]$OR$$OR$", "UserEntry": "(\"can go hell\":1 OR \"go to hell\":1 OR \"what the hell\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2015-09-28T11:51:00", "ComponentCount": 10}