
'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''
import FEAPI_Helpers
import json
import os,sys
import pandas as pd
from pathlib import Path
from datetime import datetime
import configparser



class config:



    # Default values
    '''
    [FEAPI]
    '''
    API = "feapi"
    LogRoot = "logs"
    DEBUG = False
    LoggerLevel = "DEBUG"
    username = ""
    password = ""

    '''
    [ImportScore]
    '''
    Tenant = ""
    FolderName = ""
    NewScoreName=""
    Importer=None
    RemoveFilters = False
    OverwriteExisting = False

    API_timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    json_output_dir = ""
    @staticmethod
    def FilenameInOutputFolder(filename):
        return os.path.join(config.json_output_dir, filename)

    @staticmethod
    def AllCategories_xls_filename():
        return config.FilenameInOutputFolder (  f"AllCategories.xlsx")

    @staticmethod
    def PyFilename():
        os.path.basename(sys.argv[0])

    @staticmethod
    def SetFolderName(foldername):
        config.FolderName = f"{config.DataRoot}/{foldername}"

    @staticmethod
    def read_ini_file(file_path:str=""):
        if file_path=="":
            file_path = f"{os.path.basename(sys.argv[0])}.INI"
        if os.path.exists(file_path):
            config.strip_lines_with_triple_single_quotes(file_path,file_path)
            configparse = configparser.ConfigParser()
            configparse.read(file_path)

            config.DEBUG = configparse.get('FEAPI', 'DEBUG').strip('"')  == 1
            config.LoggerLevel = configparse.get('FEAPI', 'LoggerLevel').strip('"')
            config.API = configparse.get('FEAPI', 'API').strip('"')
            config.Tenant = configparse.get('FEAPI', 'Tenant').strip('"')
            config.LogRoot = configparse.get('FEAPI', 'LogRoot').strip('"')
        else:
            print(f"ERROR: INI file at {file_path} does not exist **************************")

    @staticmethod
    def strip_lines_with_triple_single_quotes(input_file: str, output_file: str):
        with open(input_file, 'r') as file:
            lines = file.readlines()
            # Filter out lines that are just ''' (including any whitespace)
        cleaned_lines = [line for line in lines if line.strip() != "'''"]
        # Write the cleaned lines to the output file
        with open(output_file, 'w') as file:
            file.writelines(cleaned_lines)

config.read_ini_file()



# LOGGING --------------------------------------------------------------------------------------
import logging
# Set up logging configuration
logger = logging.getLogger(config.PyFilename())
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

#reset logger level if passed in args
level_string = config.LoggerLevel.upper()  # Ensure case-insensitivity
level = logging.getLevelName(level_string)
if isinstance(level, int):  # Check if a valid level was found
    logging.getLogger().setLevel(level)
    print(f"Logging level set to: {logging.getLevelName(level)}")
else:
    print(f"Invalid logging level string passed as config: {level_string}")

# Create handlers and log folders
if not os.path.exists(config.LogRoot):
    os.makedirs(config.LogRoot)

logprefix = f"{config.LogRoot}/{config.PyFilename()}-{config.timestamp}"
file_handler = logging.FileHandler(f'{logprefix}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'{logprefix}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'{logprefix}.warnings.txt')  # Log to errors.txt

console_handler = logging.StreamHandler()  # Log to console


# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)



#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, config)



#API info
# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# for FISUS, use feapif.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token variable






#---------------------------------------------------------------------------------------

import argparse

def str_to_bool(s):
    if s.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif s.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')

def main():

    # get cmdline args and overwrite defaults from INI
    config.read_ini_file()

    # retrieve username and pwd from USer Account environemtn variable
    config.username = os.getenv("FEAPI_username")
    config.password = os.getenv("FEAPI_pwd")
    config.security_api = 'https://sapicm.callminer.net/'

    # Global Helper functions ------------------------------------------------------------------------------
    global helper
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, config)

    
    # Check if the tenant matches
    logger.info(f"Checking if API tenant matches target Config.Tenant ({config.Tenant})")
    config.tenant = helper.API_CheckTenantName(config.Tenant)

    #Rename Test Category
    helper.API_RenameCategory("Categories.Test.TEST1","TEST1_renamed")
    helper.API_RenameCategory("Categories.Test.TEST1_renamed","TEST1")

    #Rename Test Score
    helper.API_RenameScore("test_score","test_score_renamed")
    helper.API_RenameScore("test_score_renamed","test_score")

if __name__ == "__main__":
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")



