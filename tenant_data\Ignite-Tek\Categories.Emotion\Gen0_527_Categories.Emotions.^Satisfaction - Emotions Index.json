{"BucketId": "527", "BucketFullname": "Categories.Emotions.^Satisfaction - Emotions Index", "BucketDescription": "tuned for sales. Satsified with current service turned off as that is standard approach", "UserEditable": false, "BucketName": "^Satisfaction - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.^Satisfaction - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:40:17.617-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30116, "Name": "Can`t complain", "Description": null, "ActionString": "[how+are+you:0.8][shouldn`t+be+complaining:0.8][can`t|cannot+beat|complain:1]$OR$$NOTAFTER:5$", "UserEntry": "((\"can`t|cannot beat|complain\":1)  OR (\"shouldn't be complaining\") ) NOT AFTER:5 \"how are you\"", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30115, "Name": "Better than", "Description": null, "ActionString": "<plans><plan><volumes>$OR$$OR$<nothing><not><no>$OR$$OR$[it+is+better|more|quicker|faster|higher|sooner|earlier+than:1.1]$NOTNEAR:0.5$$NOTNEAR:3$", "UserEntry": "(\"it is better|more|quicker|faster|better|higher|sooner|earlier|faster than\" NOT NEAR:0.5  (no|not|nothing) ) NOT NEAR:3 (volumes|plan|plans)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30114, "Name": "Pretty cool-nice-decent", "Description": null, "ActionString": "[pretty|fairly+cool|fun|nice|decent|cheap|strong|shape|solid|thorough:1]", "UserEntry": "\"pretty|fairly cool|fun|nice|decent|cheap|strong|shape|decent|solid|thorough\":1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30113, "Name": "Satisfied", "Description": null, "ActionString": "<isn`t><not>$OR$[up+my+satisfaction:1]$NOTNEAR:0.5$[pleasant%|nice|great+surprise%:1][good+spot:0.5][happy+camper|clam|baby:1]<never><not>$OR$[i|i`m+satisf%|happy|pleased|impressed|thrilled|delighted|enjoying|content+with|it|what:0.8]$NOTNEAR:0$$OR$$OR$$OR$$OR$", "UserEntry": "(\"i|i'm satisf*|happy|pleased|impressed|thrilled|delighted|thrilled|enjoying|content with|it|what\" NOT NEAR:0 (not|never) ) \nOR \"happy camper|clam|baby\":1 \nOR \"good spot\"\nOR \"pleasant*|nice|great surprise*\":1\nOR \"up my satisfaction\":1 NOT NEAR:0.5 (not|isn't)", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29968, "Name": "Already Satisfied", "Description": null, "ActionString": "[i|i`m+satisfied|happy+with|what+have|got|already|now:2]", "UserEntry": "\"i|i'm satisfied|happy with|what have|got|already|now\":2", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29967, "Name": "Upgrade", "Description": null, "ActionString": "[is|if|it+quicker|faster|better|higher|sooner|earlier+than:0.8]", "UserEntry": "\"is|if|it quicker|faster|better|higher|sooner|earlier|faster than\"", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-08T19:49:11.307", "ComponentCount": 6}