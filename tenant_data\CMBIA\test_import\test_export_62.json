{"Id": 62, "Name": "test_export", "Description": "this is to test import/export for mike d", "UseWeightedAverage": 0, "Status": "Active", "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-04T16:04:00", "Type": "Raw", "RetroStartDate": null, "Filters": [{"SearchComponentID": -21998, "BucketID": 89, "SectionID": null, "AlertID": null, "ColumnName": "", "TableFilterTypes": null, "FriendlyName": "", "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "ScoreComponents": [{"Id": 268, "Name": "testind_cat1", "DisplayDescriptionsInCoach": false, "Description": "test indicator1 based on category", "SearchComponentId": -21997, "BucketId": 92, "BucketMissScore": 0.0, "BucketMissColor": "", "ColumnName": "", "CurrentlyActive": true, "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-03T14:12:00", "Weight": 1.0, "Type": "Raw", "Bins": [{"BinId": 565, "ScoreComponentId": 268, "BinNumber": 1, "BinMin": null, "BinMax": 33.33, "BinValue": null, "BinColor": "#d90429", "BinFriendlyName": "Poor", "BinDescription": "", "Target": false, "AutoFail": false}, {"BinId": 566, "ScoreComponentId": 268, "BinNumber": 2, "BinMin": 33.33, "BinMax": 66.67, "BinValue": null, "BinColor": "#ec752c", "BinFriendlyName": "Satisfactory", "BinDescription": "", "Target": false, "AutoFail": false}, {"BinId": 567, "ScoreComponentId": 268, "BinNumber": 3, "BinMin": 66.67, "BinMax": null, "BinValue": null, "BinColor": "#fbdb7b", "BinFriendlyName": "Excellent", "BinDescription": "", "Target": false, "AutoFail": false}], "Ranges": [{"ScoreComponentId": 268, "RangeMinimum": null, "RangeMaximum": null, "PointsMinimum": 100.0, "PointsMaximum": 100.0, "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-03T14:12:00"}], "Filters": [{"SearchComponentID": -21997, "BucketID": 90, "SectionID": null, "AlertID": null, "ColumnName": "", "TableFilterTypes": null, "FriendlyName": "", "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "DisplayOrder": 0, "SubjectiveMin": null, "SubjectiveMax": null, "SlnInstallationID": null, "IsManualRequired": false, "AdditionalOptions": "None"}, {"Id": 269, "Name": "testind_measure", "DisplayDescriptionsInCoach": false, "Description": "", "SearchComponentId": null, "BucketId": null, "BucketMissScore": null, "BucketMissColor": "", "ColumnName": "Agitation", "CurrentlyActive": true, "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-03T14:12:00", "Weight": 1.0, "Type": "Raw", "Bins": [{"BinId": 568, "ScoreComponentId": 269, "BinNumber": 1, "BinMin": null, "BinMax": 33.33, "BinValue": null, "BinColor": "#d90429", "BinFriendlyName": "Poor", "BinDescription": "", "Target": false, "AutoFail": false}, {"BinId": 569, "ScoreComponentId": 269, "BinNumber": 2, "BinMin": 33.33, "BinMax": 66.67, "BinValue": null, "BinColor": "#ec752c", "BinFriendlyName": "Satisfactory", "BinDescription": "", "Target": false, "AutoFail": false}, {"BinId": 570, "ScoreComponentId": 269, "BinNumber": 3, "BinMin": 66.67, "BinMax": null, "BinValue": null, "BinColor": "#fbdb7b", "BinFriendlyName": "Excellent", "BinDescription": "", "Target": false, "AutoFail": false}], "Ranges": [], "Filters": [], "DisplayOrder": 1, "SubjectiveMin": null, "SubjectiveMax": null, "SlnInstallationID": null, "IsManualRequired": false, "AdditionalOptions": "None"}, {"Id": 270, "Name": "testind_manual", "DisplayDescriptionsInCoach": false, "Description": "manual indicators test", "SearchComponentId": null, "BucketId": null, "BucketMissScore": 0.0, "BucketMissColor": "", "ColumnName": "", "CurrentlyActive": true, "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-03T14:12:00", "Weight": 1.0, "Type": "Raw", "Bins": [{"BinId": 571, "ScoreComponentId": 270, "BinNumber": 1, "BinMin": null, "BinMax": 33.0, "BinValue": 16.5, "BinColor": "#d90429", "BinFriendlyName": "Bin 1", "BinDescription": "", "Target": false, "AutoFail": false}, {"BinId": 572, "ScoreComponentId": 270, "BinNumber": 2, "BinMin": 33.0, "BinMax": 66.0, "BinValue": 49.5, "BinColor": "#ec752c", "BinFriendlyName": "Bin 2", "BinDescription": "", "Target": false, "AutoFail": false}, {"BinId": 573, "ScoreComponentId": 270, "BinNumber": 3, "BinMin": 66.0, "BinMax": null, "BinValue": 83.0, "BinColor": "#fbdb7b", "BinFriendlyName": "Bin 3", "BinDescription": "", "Target": false, "AutoFail": false}], "Ranges": [], "Filters": [], "DisplayOrder": 2, "SubjectiveMin": 0.0, "SubjectiveMax": 100.0, "SlnInstallationID": null, "IsManualRequired": true, "AdditionalOptions": "None"}], "Bins": [{"BinId": 211, "ScoreId": 62, "BinNumber": 1, "BinMin": null, "BinMax": 33.0, "BinColor": "#d90429", "BinFriendlyName": "Poor", "Target": true}, {"BinId": 212, "ScoreId": 62, "BinNumber": 2, "BinMin": 33.0, "BinMax": 66.0, "BinColor": "#ec752c", "BinFriendlyName": "Satisfactory", "Target": false}, {"BinId": 213, "ScoreId": 62, "BinNumber": 3, "BinMin": 66.0, "BinMax": null, "BinColor": "#fbdb7b", "BinFriendlyName": "Excellent", "Target": false}], "HasPendingRetroRequest": false, "Created": "2025-03-03T14:12:00-05:00", "Creator": "<PERSON>@callminer.com", "Version": null, "LanguageTag": "Unknown", "CoachEnabled": false, "FolderId": null, "SlnInstallationID": null, "DateInactive": null, "DisplayDescriptionsInCoach": false, "EffectiveDate": "2025-03-04T16:03:30", "Group": {"GroupName": null, "GroupID": null}}