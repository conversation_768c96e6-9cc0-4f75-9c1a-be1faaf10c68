{"BucketId": "550", "BucketFullname": "Categories.Emotions.ClosingLanguage", "BucketDescription": "Category catching closing language in Emotions", "UserEditable": false, "BucketName": "ClosingLanguage", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.ClosingLanguage", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-07T20:44:06.71-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -29998, "Name": "Closing-Bye<PERSON><PERSON>", "Description": null, "ActionString": "<adios><goodnightby<PERSON>ye><goodbye><bye>$OR$$OR$$OR$", "UserEntry": "bye|goodbye|goodnight<PERSON><PERSON><PERSON>|adios", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29997, "Name": "Closing-Phrases", "Description": null, "ActionString": "[be+well:0.5][stay+safe:0.5][you`re|you|always+welcome:0.5][good+night|bye|day:0.5][take+care:0.5][have+a+nice|good|wonderful|blessed:0.8]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"have a nice|good|wonderful|blessed\" OR \"take care\" OR \"good night|bye|day\" OR \"you're|you|always welcome\" OR \"stay safe\" OR \"be well\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-07T20:44:07.17", "ComponentCount": 2}