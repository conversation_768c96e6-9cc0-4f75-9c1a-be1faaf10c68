[{"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Dissatisfaction followed by Ownership", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Dissatisfaction followed by Ownership", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction followed by Ownership"}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Dissatisfaction followed by Ownership and Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Dissatisfaction followed by Ownership and Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction followed by Ownership and Empathy"}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Medical Issue followed by Ownership", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Medical Issue followed by Ownership", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue followed by Ownership"}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Medical Issue followed by Ownership and Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Medical Issue followed by Ownership and Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue followed by Ownership and Empathy"}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Ownership", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Ownership", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 5, "DependsOnBucketfullname": "Categories.Customer Experience.Ownership", "Name": "Ownership", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Ownership"}, {"DependsOnBucketId": 6, "DependsOnBucketfullname": "Categories.Customer Experience.Customers` Appreciation", "Name": "Customer Compliments", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 6, "DependsOnBucketfullname": "Categories.Customer Experience.Customers` Appreciation", "Name": "Customer Compliments", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Compliments"}, {"DependsOnBucketId": 6, "DependsOnBucketfullname": "Categories.Customer Experience.Customers` Appreciation", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 6, "DependsOnBucketfullname": "Categories.Customer Experience.Customers` Appreciation", "Name": "Customers` Appreciation", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 7, "DependsOnBucketfullname": "Categories.Customer Experience.Agent Politeness", "Name": "Agent <PERSON>", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 7, "DependsOnBucketfullname": "Categories.Customer Experience.Agent Politeness", "Name": "Agent <PERSON>", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 7, "DependsOnBucketfullname": "Categories.Customer Experience.Agent Politeness", "Name": "Agent <PERSON>", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Agent <PERSON>"}, {"DependsOnBucketId": 7, "DependsOnBucketfullname": "Categories.Customer Experience.Agent Politeness", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "probing test"}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "ADD", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "AF - Call Avoidance", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "AF - Medical Advice", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "AF - Negative Scripting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "AF - Professionalism", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Agent <PERSON>", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Answered Within 5 Seconds", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Answered Within 5 Seconds", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Answered Within 5 Seconds"}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Answered Within 5 Secs Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Auto Pass For Non Applicable Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Empathy"}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Auto Pass For Non Applicable Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Hold Etiquette"}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Call Closing", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Call Closing Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Call Control_Background Noise", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Call Control_Minimal Verbal Collision", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Call Control_Setting Expectations", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Care_Active Listening", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Care_Rapport Building", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Care_Tempo", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Care_Understanding", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Checking Understanding", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Communication_Agent Professionalism", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Communication_Conversational Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Communication_Repetitions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Customer Compliments", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "CX Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "CX Hold Etiquette", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Empathy", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Greeting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Hold Etiquette", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "kk", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Medical Advice Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Minimal Hold Test", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Negative Towards Brand Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Original", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Ownership", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "PRE Questions Asked", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "PRE Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "probing test", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Product Knowledge_Coaching Materials", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Product Knowledge_Coaching Materials Complaint Only", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Product Knowledge_Probing Questions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Product Knowledge_Shipping Method", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Product Knowledge_Troubleshooting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Rapport Building | Positive Language", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Resolution_Further Questions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Resolution_Solution Provided", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Tempo", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "TEST", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 8, "DependsOnBucketfullname": "Categories.Customer Experience.Speaker Separated Calls", "Name": "Understandability CX", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 9, "DependsOnBucketfullname": "Categories.Customer Experience.Auditory Understandability", "Name": "Auditory Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understandability"}, {"DependsOnBucketId": 9, "DependsOnBucketfullname": "Categories.Customer Experience.Auditory Understandability", "Name": "Auditory Understandability", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understandability"}, {"DependsOnBucketId": 9, "DependsOnBucketfullname": "Categories.Customer Experience.Auditory Understandability", "Name": "Understandability", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 9, "DependsOnBucketfullname": "Categories.Customer Experience.Auditory Understandability", "Name": "Understandability", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 10, "DependsOnBucketfullname": "Categories.Customer Experience.Contextual Understandability", "Name": "Contextual Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understandability"}, {"DependsOnBucketId": 10, "DependsOnBucketfullname": "Categories.Customer Experience.Contextual Understandability", "Name": "Contextual Understandability", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understandability"}, {"DependsOnBucketId": 10, "DependsOnBucketfullname": "Categories.Customer Experience.Contextual Understandability", "Name": "Understandability", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 10, "DependsOnBucketfullname": "Categories.Customer Experience.Contextual Understandability", "Name": "Understandability", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Abbott Exclusions", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Acknowledgement", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Acknowledgement", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Acknowledgement"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Auditory Understandability", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Auditory Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Auditory Understandability"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Auditory Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Auditory Understandability_Agent"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Auditory Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Auditory Understandability_Customer"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Auditory Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Triple Auditory Understandability"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Auditory Understandability_Agent", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Auditory Understandability_Customer", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Call Opening - Greeting", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Greeting"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Exclusions", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Ghost Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Abbott Exclusions"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Ghost Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Exclusions"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Greeting", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Tandem Mention and FL2+ Sensor", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem No Dexcom"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Tandem Mention and FL2+ Sensor", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem Product Identifier"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Tandem No Dexcom", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Tandem Product Identifier", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Tandem Transfers", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem No Dexcom"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Tandem Transfers", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem Product Identifier"}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Triple Auditory Understandability", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Unnatural Call Endings", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 11, "DependsOnBucketfullname": "Categories.Call Drivers.Ghost Calls", "Name": "Unnatural Call Endings", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Unnatural Call Endings"}, {"DependsOnBucketId": 12, "DependsOnBucketfullname": "Categories.Customer Experience.Speech Understandability", "Name": "Speech Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understandability"}, {"DependsOnBucketId": 12, "DependsOnBucketfullname": "Categories.Customer Experience.Speech Understandability", "Name": "Speech Understandability", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understandability"}, {"DependsOnBucketId": 12, "DependsOnBucketfullname": "Categories.Customer Experience.Speech Understandability", "Name": "Understandability", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 12, "DependsOnBucketfullname": "Categories.Customer Experience.Speech Understandability", "Name": "Understandability", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 13, "DependsOnBucketfullname": "Categories.Customer Experience.Greeting", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 13, "DependsOnBucketfullname": "Categories.Customer Experience.Greeting", "Name": "Greeting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 13, "DependsOnBucketfullname": "Categories.Customer Experience.Greeting", "Name": "Greeting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 13, "DependsOnBucketfullname": "Categories.Customer Experience.Greeting", "Name": "Greeting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Greeting"}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Coaching Materials", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Coaching Materials - test", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Rapport Building | Positive Language", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Rapport Building | Positive Language", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 14, "DependsOnBucketfullname": "Categories.Customer Experience.Rapport Building | Positive Language", "Name": "Rapport Building | Positive Language", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Rapport Building | Positive Language"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "CASE OR Dissatisfaction", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Complaint OR Dissatisfaction - <PERSON><PERSON> Tri<PERSON>"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "CEO, BBB, FDA, FTC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Legal Threats"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "CEO, BBB, FDA, FTC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Legal Threats - testing august cat"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "CEO, BBB, FDA, FTC", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats - testing"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Complaint OR Dissatisfaction - <PERSON><PERSON> Tri<PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction 30%-70%", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction 30%-70%", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction 30%-70%"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction After Hold Silence", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "TEST"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction First 30%", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction First 30%", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction First 30%"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction followed by Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction followed by Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction followed by Empathy"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction followed by Ownership", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction followed by Ownership", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction followed by Ownership"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction followed by Ownership and Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction followed by Ownership and Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction followed by Ownership and Empathy"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction Last 30%", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction Last 30%", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction Last 30%"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction within 60s After Hold", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Dissatisfaction within 60s After Hold", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction within 60s After Hold"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Legal Threats", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Legal Threats", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Legal Threats"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Legal Threats", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Legal Threats - testing august cat"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Legal Threats", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats - testing"}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Legal Threats - testing", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "Legal Threats - testing august cat", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 15, "DependsOnBucketfullname": "Categories.Customer Experience.Dissatisfaction", "Name": "TEST", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "MISS test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Reads - Troubleshooting|Data Collection"}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Reads - Troubleshooting|Data Collection", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Reads - Troubleshooting|Data Collection", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Reads - Troubleshooting|Data Collection", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Reads - Troubleshooting|Data Collection"}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Reads - Troubleshooting|Data Collection", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Reads - Troubleshooting|Data Collection"}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Reads - Troubleshooting|Data Collection"}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Understandability", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Understandability", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understandability CX"}, {"DependsOnBucketId": 16, "DependsOnBucketfullname": "Categories.Customer Experience.Understandability", "Name": "Understandability CX", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Call Closing", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Call Closing", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Call Closing", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Closing"}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Can you hold", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Can you hold?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Can you hold"}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Hold Etiquette", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Please Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Hold Etiquette"}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Unnatural Call Ending", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 17, "DependsOnBucketfullname": "Categories.Customer Experience.Call Closing", "Name": "Unnatural Call Ending", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Unnatural Call Ending"}, {"DependsOnBucketId": 19, "DependsOnBucketfullname": "Categories.Reporting Support Categories.Silence Longer than 60sec", "Name": "Silence >60sec", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 19, "DependsOnBucketfullname": "Categories.Reporting Support Categories.Silence Longer than 60sec", "Name": "Silence >60sec", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Silence >60sec"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Auto Pass For Non Applicable Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Hold Etiquette"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Can you Hold Before Hold Silence", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "TEST"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "CX Hold Etiquette", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Dissatisfaction within 60s After Hold", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Dissatisfaction within 60s After Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction within 60s After Hold"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Dissatisfaction within 60s After Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "TEST Dissatisfaction within 60s After Hold"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Dissatisfaction within 60s After Hold", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Dissatisfaction within 60s After Hold", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction within 60s After Hold"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Setting Expectations"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Hold Etiquette", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Longest Silence Block on HOLD calls", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Negative Silence", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Negative Silence", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Silence"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Returning or Retaking", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Hold Etiquette"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Setting Expectations", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Short Silences Excludes Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Silence"}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "Silence", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "TEST", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 20, "DependsOnBucketfullname": "Categories.Customer Experience.Can you hold", "Name": "TEST Dissatisfaction within 60s After Hold", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 21, "DependsOnBucketfullname": "Categories.Behaviours.Hold Etiquette", "Name": "CX Hold Etiquette", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 21, "DependsOnBucketfullname": "Categories.Behaviours.Hold Etiquette", "Name": "Returning or Retaking", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Hold Etiquette"}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "11. Understanding", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Care_Understanding"}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "Care_Understanding", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "Care_Understanding", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "Care_Understanding", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "Care_Understanding", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 131, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Understanding", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 23, "DependsOnBucketfullname": "Categories.Customer Experience.Checking Understanding", "Name": "Checking Understanding", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 23, "DependsOnBucketfullname": "Categories.Customer Experience.Checking Understanding", "Name": "Checking Understanding", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 23, "DependsOnBucketfullname": "Categories.Customer Experience.Checking Understanding", "Name": "Checking Understanding", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Checking Understanding"}, {"DependsOnBucketId": 23, "DependsOnBucketfullname": "Categories.Customer Experience.Checking Understanding", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "<PERSON><PERSON><PERSON><PERSON> followed by <PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "<PERSON><PERSON><PERSON><PERSON> followed by <PERSON><PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "<PERSON><PERSON><PERSON><PERSON> followed by <PERSON><PERSON>"}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "CX Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Dissatisfaction followed by Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Dissatisfaction followed by Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction followed by Empathy"}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Dissatisfaction followed by Ownership and Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Dissatisfaction followed by Ownership and Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction followed by Ownership and Empathy"}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Empathy", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Empathy", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Empathy"}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Empathy Language", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Empathy"}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Medical Issue followed by Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Medical Issue followed by Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue followed by Empathy"}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Medical Issue followed by Ownership and Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 24, "DependsOnBucketfullname": "Categories.Behaviours.Empathy", "Name": "Medical Issue followed by Ownership and Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue followed by Ownership and Empathy"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Additional Inquiry Language", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiry"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Agent <PERSON>", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Silence"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Base-Line Coaching Phrases", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Call Closing", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Checking Understanding", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Coaching", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Coaching", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Coaching", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Coaching Materials", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Coaching Materials - test", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Customer Compliments", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Education - Reads", "Type": "Category", "Owner": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "<PERSON><PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "<PERSON><PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Empathy", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Escalations/Transfer", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiry"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Greeting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Hold Etiquette", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Hold Over Two Minutes", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Silence"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Inquiry", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Inquiry Language", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiry"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Insertion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Insertion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Insertion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Negative Silence", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Negative Silence", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Silence"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Off-Label Use", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Ownership", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Product", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Product", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Product", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Rapport Building | Positive Language", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Reads - Education", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Reads Primary Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Reads Primary Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Reads - Education"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Short Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiry"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Short Silences Excludes Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Silence"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Silence", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Tempo", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "TEST", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Test 2", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 25, "DependsOnBucketfullname": "Categories.Customer Experience.Abbott Exclusions", "Name": "Understandability CX", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Address Confirmation", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Troubleshooting"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Autopass - Complaint Identifier", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "CASE OR Dissatisfaction", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Complaint OR Dissatisfaction - <PERSON><PERSON> Tri<PERSON>"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Coaching", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Coaching", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Coaching", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Coaching Materials", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Coaching Materials - test", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Complaint OR Dissatisfaction - <PERSON><PERSON> Tri<PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Education", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Education - Reads", "Type": "Category", "Owner": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Inquiry", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Insertion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Insertion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Insertion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "PRE Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Product", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Coaching Materials - test"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Product", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Education - Reads"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Product", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Coaching Materials"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Product Knowledge_Coaching Materials Complaint Only", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Product Knowledge_Shipping Method", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Shipping Method", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Shipping Method Process", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Short Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiry"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Troubleshooting", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "Troubleshooting", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Troubleshooting"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "TSG - DSPC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Troubleshooting"}, {"DependsOnBucketId": 27, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint Identifier", "Name": "TSG - Insertion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Troubleshooting"}, {"DependsOnBucketId": 28, "DependsOnBucketfullname": "Categories.Customer Experience.CX Empathy", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 28, "DependsOnBucketfullname": "Categories.Customer Experience.CX Empathy", "Name": "Empathy", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 29, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint OR Dissatisfaction - Empathy Trigger", "Name": "Auto Pass For Non Applicable Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Empathy"}, {"DependsOnBucketId": 29, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint OR Dissatisfaction - Empathy Trigger", "Name": "CX Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 29, "DependsOnBucketfullname": "Categories.Customer Experience.Complaint OR Dissatisfaction - Empathy Trigger", "Name": "Empathy Language", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "CX Empathy"}, {"DependsOnBucketId": 30, "DependsOnBucketfullname": "Categories.Customer Experience.CX Hold Etiquette", "Name": "Customer Experience Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 30, "DependsOnBucketfullname": "Categories.Customer Experience.CX Hold Etiquette", "Name": "Hold Etiquette", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 30, "DependsOnBucketfullname": "Categories.Customer Experience.CX Hold Etiquette", "Name": "Hold Etiquette", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer Experience Scorecard"}, {"DependsOnBucketId": 30, "DependsOnBucketfullname": "Categories.Customer Experience.CX Hold Etiquette", "Name": "Hold Etiquette", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Hold Etiquette"}, {"DependsOnBucketId": 32, "DependsOnBucketfullname": "Categories.Customer Experience.Escalations", "Name": "Dissatisfaction", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 32, "DependsOnBucketfullname": "Categories.Customer Experience.Escalations", "Name": "Want someone else/Escalations", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction"}, {"DependsOnBucketId": 35, "DependsOnBucketfullname": "Categories.Customer Experience.Legal Threats", "Name": "Legal Threats", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 35, "DependsOnBucketfullname": "Categories.Customer Experience.Legal Threats", "Name": "Legal Threats", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats"}, {"DependsOnBucketId": 43, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Unprofessional Behaviour", "Name": "Positive Agent <PERSON><PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 43, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Unprofessional Behaviour", "Name": "Positive Agent <PERSON><PERSON><PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Positive Agent <PERSON><PERSON><PERSON>"}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "1. Conversational Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Communication_Conversational Skills"}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "Communication_Conversational Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "Communication_Conversational Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "Communication_Conversational Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "Communication_Conversational Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 130, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Conversational Skills", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Auditory Understandability Issue", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Auditory Understandability Issue", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Auditory Understandability Issue"}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Auditory Understandability Issue", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Lack of Understanding"}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Auditory Understandability Issue", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Lack of Understanding"}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Lack of Understanding", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Lack of Understanding", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Support Location", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 44, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Language Barrier Issue", "Name": "Support Location", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Support Location"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "CEO, BBB, FDA, FTC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Legal Threats"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "CEO, BBB, FDA, FTC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Legal Threats - testing august cat"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "CEO, BBB, FDA, FTC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Legal Threats OG"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "CEO, BBB, FDA, FTC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "CEO, BBB, FDA, FTC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats - new review"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "CEO, BBB, FDA, FTC", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "CEO, BBB, FDA, FTC", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats - testing"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Dissatisfaction within 60s After Hold", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Dissatisfaction within 60s After Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction within 60s After Hold"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Legal Threats"}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats - new review", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats - testing", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats - testing august cat", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 48, "DependsOnBucketfullname": "Categories.Behaviours.Dissatisfaction - Customer", "Name": "Legal Threats OG", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 55, "DependsOnBucketfullname": "Categories.Behaviours.TEST Dissatisfaction - Customer", "Name": "Dissatisfaction within 60s After Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "TEST Dissatisfaction within 60s After Hold"}, {"DependsOnBucketId": 55, "DependsOnBucketfullname": "Categories.Behaviours.TEST Dissatisfaction - Customer", "Name": "TEST Dissatisfaction within 60s After Hold", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 55, "DependsOnBucketfullname": "Categories.Behaviours.TEST Dissatisfaction - Customer", "Name": "Testing 1", "Type": "Score", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 55, "DependsOnBucketfullname": "Categories.Behaviours.TEST Dissatisfaction - Customer", "Name": "Testing 2", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Testing 1"}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "22. Shipping Method", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Product Knowledge_Shipping Method"}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "Product Knowledge_Shipping Method", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "Product Knowledge_Shipping Method", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "Product Knowledge_Shipping Method", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "Product Knowledge_Shipping Method", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 132, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method Process", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 62, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Reader Not Working As Expected", "Name": "Negative Experience Service", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 62, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Reader Not Working As Expected", "Name": "Negative Experience Service", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Experience Service"}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "9. Rapport Building", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Care_Rapport Building"}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "Care_Rapport Building", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "Care_Rapport Building", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "Care_Rapport Building", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "Care_Rapport Building", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 133, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport Building", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 91, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Sensor Not Working As Expected", "Name": "Negative Experience Service", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 91, "DependsOnBucketfullname": "Categories.Automatic Survey Categorisation.Sensor Not Working As Expected", "Name": "Negative Experience Service", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Experience Service"}, {"DependsOnBucketId": 94, "DependsOnBucketfullname": "Categories.Behaviours.Unnatural Call Ending", "Name": "PRE Questions Asked", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 94, "DependsOnBucketfullname": "Categories.Behaviours.Unnatural Call Ending", "Name": "PRE Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 96, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Contextual Understandability", "Name": "Contextual Understandability", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Conversational Skills"}, {"DependsOnBucketId": 96, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Contextual Understandability", "Name": "Conversational Skills", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 99, "DependsOnBucketfullname": "Categories.Behaviours.Repeated Call_ANI", "Name": "Repeated Call_ANI", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 99, "DependsOnBucketfullname": "Categories.Behaviours.Repeated Call_ANI", "Name": "Repeated Call_ANI", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Repeated Call_ANI"}, {"DependsOnBucketId": 100, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Complaint", "Name": "AF - Missed PRE, MED, LEG or READ Issue", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 100, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Complaint", "Name": "AF - Missed PRE, MED, LEG or READ Issue", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 100, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Complaint", "Name": "<PERSON><PERSON><PERSON><PERSON> followed by <PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 100, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Complaint", "Name": "<PERSON><PERSON><PERSON><PERSON> followed by <PERSON><PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "<PERSON><PERSON><PERSON><PERSON> followed by <PERSON><PERSON>"}, {"DependsOnBucketId": 100, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Complaint", "Name": "PRE Questions Asked", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 100, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Complaint", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 100, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Complaint", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Application", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Application Issues", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Application"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Direct To Patient Website Inquiries", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Direct to Patient Website Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Direct To Patient Website Inquiries"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Display Messages Or Errors", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Display Messages|Errors Consumer|Libre Sense|POC", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Display Messages Or Errors"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Inquiries", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Inquiries", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Other", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Other", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Packaging Or Labelling", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Packaging Or Labelling", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Packaging Or Labelling"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Packaging Or Labelling", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Packaging Or Labelling - WIP"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Packaging Or Labelling - WIP", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "PRE", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "PRE Test AF"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "PRE Questions Asked", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "PRE Questions Asked", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "PRE Questions Asked"}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "PRE Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Probing Questions", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 101, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "Name": "Probing Questions", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "probing test"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Acknowledgement", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Acknowledgement", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Acknowledgement"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "AF - Call Avoidance", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "AF - Medical Advice", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "AF - Negative Scripting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "AF - Professionalism", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Answered Within 5 Secs Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Autopass - Unnatural Call Endings", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Further Questions"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Call Closing Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Call Control_Background Noise", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Call Control_Minimal Verbal Collision", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Call Control_Setting Expectations", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Care_Active Listening", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Care_Rapport Building", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Care_Tempo", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Care_Understanding", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Communication_Agent Professionalism", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Communication_Conversational Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Communication_Repetitions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Exclusions Test", "Type": "Category", "Owner": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Further Questions", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Further Questions", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Further Questions"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Ghost Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Exclusions Test"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Ghost Calls 2", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Exclusions Test"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Less than", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Exclusions Test"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Medical Advice Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Minimal Hold Test", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Negative Towards Brand Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "PRE Questions Asked", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "PRE Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "probing test", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Product Knowledge_Coaching Materials", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Product Knowledge_Coaching Materials Complaint Only", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Product Knowledge_Probing Questions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Product Knowledge_Shipping Method", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Product Knowledge_Troubleshooting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Quality", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Quality", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Resolution_Further Questions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Resolution_Solution Provided", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Shipping Method", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Shipping Method", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Short OB Calls", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Exclusions Test"}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Transfer", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 103, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Exclusions", "Name": "Transfer", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Transfer"}, {"DependsOnBucketId": 104, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Politeness", "Name": "Conversational Skills", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 104, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Politeness", "Name": "Politeness", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Conversational Skills"}, {"DependsOnBucketId": 105, "DependsOnBucketfullname": "Categories.Abbott.Testing", "Name": "Abbott 3", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Testing 3"}, {"DependsOnBucketId": 105, "DependsOnBucketfullname": "Categories.Abbott.Testing", "Name": "Testing 1", "Type": "Score", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 105, "DependsOnBucketfullname": "Categories.Abbott.Testing", "Name": "Testing 3", "Type": "Score", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "2. Agent Professionalism", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Communication_Agent Professionalism"}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "AF - Professionalism", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "AF - Professionalism", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "AF - Professionalism", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "AF - Professionalism"}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "AF - Professionalism", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "Communication_Agent Professionalism", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "Communication_Agent Professionalism", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "Communication_Agent Professionalism", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "Communication_Agent Professionalism", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 106, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Professionalism", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "25. Further Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Resolution_Further Questions"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Call Back", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Call Back Offered By Agent", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Call Back Opening Hours", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Can you hold", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Can you hold?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Can you hold"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Hold Etiquette", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Please Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Hold Etiquette"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Resolution_Further Questions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Resolution_Further Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Resolution_Further Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 108, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Further Questions", "Name": "Resolution_Further Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "3. Repetitions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Communication_Repetitions"}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Communication_Repetitions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Communication_Repetitions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Communication_Repetitions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Communication_Repetitions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Test Repetition"}, {"DependsOnBucketId": 109, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Repetitions", "Name": "Test Repetition", "Type": "Category", "Owner": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 111, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport", "Name": "Rapport", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Rapport Building"}, {"DependsOnBucketId": 111, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Rapport", "Name": "Rapport Building", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "4. Setting Expectations", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Control_Setting Expectations"}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "Call Control_Setting Expectations", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "Call Control_Setting Expectations", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "Call Control_Setting Expectations", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "Call Control_Setting Expectations", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 112, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Setting Expectations", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 113, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Ownership", "Name": "Ownership", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understanding"}, {"DependsOnBucketId": 113, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Ownership", "Name": "Understanding", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 115, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Address Confirmation", "Name": "Address Confirmation", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 115, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Address Confirmation", "Name": "Address Confirmation", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process - New"}, {"DependsOnBucketId": 115, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Address Confirmation", "Name": "Shipping Method Process", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 115, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Address Confirmation", "Name": "Shipping Method Process - New", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 116, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method", "Name": "Shipping Method", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 116, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method", "Name": "Shipping Method", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process - New"}, {"DependsOnBucketId": 116, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method", "Name": "Shipping Method Process", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 116, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method", "Name": "Shipping Method Process - New", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 116, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method", "Name": "Solution", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Solution Provided"}, {"DependsOnBucketId": 116, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Shipping Method", "Name": "Solution Provided", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 117, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Escalations", "Name": "Acknowledge & Assume", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Overcoming Adversity"}, {"DependsOnBucketId": 117, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Escalations", "Name": "Auto Pass - No Escalation", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Overcoming Adversity"}, {"DependsOnBucketId": 117, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Escalations", "Name": "Offers Alternatives", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Overcoming Adversity"}, {"DependsOnBucketId": 117, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Escalations", "Name": "Overcoming Adversity", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 117, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Escalations", "Name": "Overcoming Custm. Compl. + Apologize + Calms Custm", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Overcoming Adversity"}, {"DependsOnBucketId": 117, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Escalations", "Name": "Redirects the Customer", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Overcoming Adversity"}, {"DependsOnBucketId": 117, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Escalations", "Name": "Refute the No`s", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Overcoming Adversity"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "probing test"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "21. Troubleshooting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Product Knowledge_Troubleshooting"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Anything Changed?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Are You Getting Sensors On Prescription?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Clarifying Customer Symptoms", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Environment", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "kk", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Original", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Other", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Probing Questions", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "probing test", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Product Knowledge_Troubleshooting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Product Knowledge_Troubleshooting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Product Knowledge_Troubleshooting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Product Knowledge_Troubleshooting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Setting Expectations", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Setting Expectations", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Setting Expectations"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "TEST", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 118, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Troubleshooting", "Name": "Worked Previously?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 119, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Customer Exclusions", "Name": "Hold Over Two Minutes", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Silence"}, {"DependsOnBucketId": 119, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Customer Exclusions", "Name": "Negative Silence", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 119, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Customer Exclusions", "Name": "Negative Silence", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Silence"}, {"DependsOnBucketId": 119, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Customer Exclusions", "Name": "Short Silences Excludes Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Silence"}, {"DependsOnBucketId": 119, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Customer Exclusions", "Name": "Silence", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 120, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Empathy", "Name": "Active Listening", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 120, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Empathy", "Name": "Active Listening", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Active Listening"}, {"DependsOnBucketId": 120, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Empathy", "Name": "Active Listening 2", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Active Listening"}, {"DependsOnBucketId": 120, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Empathy", "Name": "Active Listening 3", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Active Listening"}, {"DependsOnBucketId": 120, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Empathy", "Name": "Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Rapport Building"}, {"DependsOnBucketId": 120, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Empathy", "Name": "Rapport Building", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "5. Negotiation Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Control_Negotiation Skills"}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "Call Control_Negotiation Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "Call Control_Negotiation Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "Call Control_Negotiation Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "Call Control_Negotiation Skills", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 121, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overcoming Adversity", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "10. Active Listening", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Care_Active Listening"}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "Care_Active Listening", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "Care_Active Listening", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "Care_Active Listening", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "Care_Active Listening", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 123, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Active Listening", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "7. <PERSON><PERSON> Hold/Dead Air", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Control_Minimal Hold/Dead Air"}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "Call Control_Minimal Hold/Dead Air", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "Call Control_Minimal Hold/Dead Air", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "Call Control_Minimal Hold/Dead Air", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "Call Control_Minimal Hold/Dead Air", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 124, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Silence", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "5 sECS", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Answered Within 5 Secs Test AF"}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "AF - Call Avoidance", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "AF - Call Avoidance", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "AF - Call Avoidance", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "AF - Call Avoidance"}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "AF - Call Avoidance", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "Answered Within 5 Secs Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 125, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Answered Within 5 Seconds", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "19. Coaching Materials", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Product Knowledge_Coaching Materials"}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "19. Coaching Materials", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Product Knowledge_Coaching Materials Complaint Only"}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Product Knowledge_Coaching Materials", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Product Knowledge_Coaching Materials", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Product Knowledge_Coaching Materials", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Product Knowledge_Coaching Materials", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Product Knowledge_Coaching Materials Complaint Only", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 126, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Coaching Materials", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 127, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Confidence", "Name": "Agent Confidence", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Understanding"}, {"DependsOnBucketId": 127, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Agent Confidence", "Name": "Understanding", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 128, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Triple Auditory Understandability", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 128, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Triple Auditory Understandability", "Name": "Call Back", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 128, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Triple Auditory Understandability", "Name": "Call Back Offered By Agent", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 128, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Triple Auditory Understandability", "Name": "Call Back Opening Hours", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Resolution_Solution Provided", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Resolution_Solution Provided", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Resolution_Solution Provided"}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Resolution_Solution Provided", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Resolution_Solution Provided", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 129, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Solution Provided", "Name": "Resolution_Solution Provided", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 134, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Closing", "Name": "Call Closing Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 134, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Closing", "Name": "Can you hold", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 134, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Closing", "Name": "Can you hold?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Can you hold"}, {"DependsOnBucketId": 134, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Closing", "Name": "close", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Closing Test AF"}, {"DependsOnBucketId": 134, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Closing", "Name": "Hold Etiquette", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 134, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Closing", "Name": "Please Hold", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Hold Etiquette"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "probing test"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "20. Probing Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Product Knowledge_Probing Questions"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "ADD", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Anything Changed?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Are You Getting Sensors On Prescription?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Clarifying Customer Symptoms", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Environment", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "kk", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Original", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "OS Requested", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Other", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Probing Questions", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "probing test", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Product Knowledge_Probing Questions", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Product Knowledge_Probing Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Product Knowledge_Probing Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Product Knowledge_Probing Questions", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "TEST", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "kk"}, {"DependsOnBucketId": 135, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Probing Questions", "Name": "Worked Previously?", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Probing Questions"}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "AF - Medical Advice", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "AF - Medical Advice", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "AF - Medical Advice", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "AF - Medical Advice"}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "AF - Medical Advice", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "MEDICAL", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Advice Test AF"}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "Medical Advice Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 137, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Medical Advice", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "AF - Negative Scripting", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "AF - Negative Scripting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "AF - Negative Scripting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "AF - Negative Scripting"}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "AF - Negative Scripting", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "nEGATIVE", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Towards Brand Test AF"}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "Negative Towards Brand Test AF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 138, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 141, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Acknowledgement", "Name": "Acknowledgement", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Rapport Building"}, {"DependsOnBucketId": 141, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Acknowledgement", "Name": "Active Listening", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 141, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Acknowledgement", "Name": "Active Listening", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Active Listening"}, {"DependsOnBucketId": 141, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Acknowledgement", "Name": "Active Listening 2", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Active Listening"}, {"DependsOnBucketId": 141, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Acknowledgement", "Name": "Active Listening 3", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Active Listening"}, {"DependsOnBucketId": 141, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Acknowledgement", "Name": "Rapport Building", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 142, "DependsOnBucketfullname": "Categories.Acknowledgement.Empathy", "Name": "Empathy", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Soft Skills"}, {"DependsOnBucketId": 142, "DependsOnBucketfullname": "Categories.Acknowledgement.Empathy", "Name": "Soft Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 143, "DependsOnBucketfullname": "Categories.Ownership.Ensuring", "Name": "Ensuring", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Ownership"}, {"DependsOnBucketId": 144, "DependsOnBucketfullname": "Categories.Acknowledgement.Acknowledgement", "Name": "Soft Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 145, "DependsOnBucketfullname": "Categories.Ownership.Attempt to assist", "Name": "Attempt to Assist", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Ownership"}, {"DependsOnBucketId": 146, "DependsOnBucketfullname": "Categories.Acknowledgement.Apologies", "Name": "Apologies", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Soft Skills"}, {"DependsOnBucketId": 146, "DependsOnBucketfullname": "Categories.Acknowledgement.Apologies", "Name": "Soft Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 147, "DependsOnBucketfullname": "Categories.Working From Home.Background Noise", "Name": "Background Noise", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Work From Home"}, {"DependsOnBucketId": 147, "DependsOnBucketfullname": "Categories.Working From Home.Background Noise", "Name": "Work From Home", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 148, "DependsOnBucketfullname": "Categories.Working From Home.Personal Calls", "Name": "Personal Calls", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Work From Home"}, {"DependsOnBucketId": 148, "DependsOnBucketfullname": "Categories.Working From Home.Personal Calls", "Name": "Work From Home", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 149, "DependsOnBucketfullname": "Categories.Ownership.Reassuring", "Name": "Reassuring", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Ownership"}, {"DependsOnBucketId": 150, "DependsOnBucketfullname": "Categories.Ownership.Offer of assistance", "Name": "Offer of Assistance", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Ownership"}, {"DependsOnBucketId": 151, "DependsOnBucketfullname": "Categories.Courtesy.Politeness", "Name": "Politeness", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Soft Skills"}, {"DependsOnBucketId": 151, "DependsOnBucketfullname": "Categories.Courtesy.Politeness", "Name": "Soft Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 152, "DependsOnBucketfullname": "Categories.Working From Home.Work From Home", "Name": "Work From Home", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 152, "DependsOnBucketfullname": "Categories.Working From Home.Work From Home", "Name": "Work From Home", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Work From Home"}, {"DependsOnBucketId": 153, "DependsOnBucketfullname": "Categories.Ownership.Ownership", "Name": "Soft Skills", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Address Confirmation", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Autopass - Replacement Identifier", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "FOR MISSES", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "General|Technical Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Inquiries", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Inquiries", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Inquiries - Agent speech", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Inquiries - WIP", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Product Knowledge_Shipping Method", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Product|Order Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Shipping Method", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Shipping Method Process"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Shipping Method Process", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Single and Multiple Replacements Identifier", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "Single Replacement Identifier", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Single and Multiple Replacements Identifier"}, {"DependsOnBucketId": 154, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Replacement Identifier", "Name": "test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 156, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Back Exclusions", "Name": "Autopass", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 156, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Back Exclusions", "Name": "Call Back", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 156, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Back Exclusions", "Name": "Call Back Offered By Agent", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 156, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Back Exclusions", "Name": "Call Back Opening Hours", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Back"}, {"DependsOnBucketId": 156, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Back Exclusions", "Name": "Setting Expectations", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 156, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Call Back Exclusions", "Name": "Survey", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Setting Expectations"}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "6. <PERSON><PERSON> Verbal Collis<PERSON>", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Control_Minimal Verbal Collision"}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "Call Control_Minimal Verbal Collision", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "Call Control_Minimal Verbal Collision", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "Call Control_Minimal Verbal Collision", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "Call Control_Minimal Verbal Collision", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 157, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Overtalk", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Active Listening", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Autopass - UCE", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Active Listening"}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Autopass - UCE", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Setting Expectations"}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Autopass - Unnatural Call Endings", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Further Questions"}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Autopass - Unnatural Call Endings", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Solution Provided"}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Further Questions", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Further Questions", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Further Questions"}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Resolution_Solution Provided", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Resolution_Solution Provided", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Resolution_Solution Provided"}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Setting Expectations", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Solution", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Solution Provided"}, {"DependsOnBucketId": 158, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "Name": "Solution Provided", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Application", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Application Errors", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Application"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "FOR MISSES", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "General|Technical Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Inquiries", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Inquiries", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Inquiries - Agent speech", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Inquiries - WIP", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Product|Order Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Test Does Not Start Or Finish", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 164, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Display Messages Or Errors", "Name": "Test Does Not Start/Finish", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Test Does Not Start Or Finish"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Application", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Application Issues", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Application"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "FOR MISSES", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "General|Technical Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Inquiries", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Inquiries", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Inquiries - Agent speech", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Inquiries - WIP", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Potentially Reportable Event", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Potentially Reportable Event", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Potentially Reportable Event"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Product|Order Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Single and Multiple Replacements Identifier", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "Single Replacement Identifier", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Single and Multiple Replacements Identifier"}, {"DependsOnBucketId": 166, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Insertion", "Name": "test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 167, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Application", "Name": "Data Management", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 167, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Application", "Name": "Data Management", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Data Management"}, {"DependsOnBucketId": 172, "DependsOnBucketfullname": "Categories.Call Drivers.Test Call", "Name": "Tandem Mention and FL2+ Sensor", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem No Dexcom"}, {"DependsOnBucketId": 172, "DependsOnBucketfullname": "Categories.Call Drivers.Test Call", "Name": "Tandem Mention and FL2+ Sensor", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem Product Identifier"}, {"DependsOnBucketId": 172, "DependsOnBucketfullname": "Categories.Call Drivers.Test Call", "Name": "Tandem No Dexcom", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 172, "DependsOnBucketfullname": "Categories.Call Drivers.Test Call", "Name": "Tandem Product Identifier", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 172, "DependsOnBucketfullname": "Categories.Call Drivers.Test Call", "Name": "Tandem Transfers", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem No Dexcom"}, {"DependsOnBucketId": 172, "DependsOnBucketfullname": "Categories.Call Drivers.Test Call", "Name": "Tandem Transfers", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem Product Identifier"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "2ND VERSION", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Scenario 1 - Confirmed"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "3RD VERSION", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Scenario 1 - Confirmed"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Compression", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Low Inaccurate Readings - Compression"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Confirmed Inaccurate Readings", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inaccurate Readings - Confirmed"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Confirmed Inaccurate Readings", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inaccurate Readings - Confirmed - validated"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "FOR MISSES", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "General|Technical Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "General|Technical Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inaccurate Readings - Confirmed", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inaccurate Readings - Confirmed - validated", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inaccurate Readings - Unconfirmed", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inquiries", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inquiries", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inquiries - Agent speech", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inquiries - Agent speech", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Inquiries - WIP", "Type": "Search", "Owner": "<EMAIL>", "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Irritation", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Low Inaccurate Readings - Irritation or Swelling"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Low Inaccurate Readings - Compression", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Low Inaccurate Readings - Irritation or Swelling", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "miss test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Scenario 1 - Confirmed"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "No Other Monitoring Devices Used Language", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inaccurate Readings - Unconfirmed"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Product|Order Inquiry", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Product|Order Inquiry", "Type": "SearchComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inquiries - WIP"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Scenario 1 - Confirmed", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 173, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Readings", "Name": "Unconfirmed Inaccurate Readings", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inaccurate Readings - Unconfirmed"}, {"DependsOnBucketId": 180, "DependsOnBucketfullname": "Categories.Abbott.Dexcom", "Name": "Tandem Mention and FL2+ Sensor", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem No Dexcom"}, {"DependsOnBucketId": 180, "DependsOnBucketfullname": "Categories.Abbott.Dexcom", "Name": "Tandem Mention on Other Lines", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem No Dexcom"}, {"DependsOnBucketId": 180, "DependsOnBucketfullname": "Categories.Abbott.Dexcom", "Name": "Tandem No Dexcom", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 180, "DependsOnBucketfullname": "Categories.Abbott.Dexcom", "Name": "Tandem Transfers", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Tandem No Dexcom"}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical (Potential PRE) Language", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical (Potential PRE) Language", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical (Potential PRE) Language"}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue followed by Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue followed by Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue followed by Empathy"}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue followed by Ownership", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue followed by Ownership", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue followed by Ownership"}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue followed by Ownership and Empathy", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue followed by Ownership and Empathy", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue followed by Ownership and Empathy"}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue Test - Wording", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 184, "DependsOnBucketfullname": "Categories.Abbott.Medical Issue", "Name": "Medical Issue Test - Wording", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Medical Issue Test - Wording"}, {"DependsOnBucketId": 218, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Inquiries", "Name": "Inquiries", "Type": "Category", "Owner": "<EMAIL>", "Group": null, "Active": "0", "DependenciesContentName": null}, {"DependsOnBucketId": 218, "DependsOnBucketfullname": "Categories.Perception Categories_Calls.Inquiries", "Name": "test", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "0", "DependenciesContentName": "Inquiries"}, {"DependsOnBucketId": 225, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Backup Questions", "Name": "Call Control_Background Noise", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard_NoAF"}, {"DependsOnBucketId": 225, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Backup Questions", "Name": "Quality Scorecard_NoAF", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 236, "DependsOnBucketfullname": "Categories.Insights.NPS Promoter", "Name": "NPS", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 236, "DependsOnBucketfullname": "Categories.Insights.NPS Promoter", "Name": "Promoter", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "NPS"}, {"DependsOnBucketId": 237, "DependsOnBucketfullname": "Categories.Insights.NPS Passive", "Name": "NPS", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 237, "DependsOnBucketfullname": "Categories.Insights.NPS Passive", "Name": "Passive", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "NPS"}, {"DependsOnBucketId": 238, "DependsOnBucketfullname": "Categories.Insights.NPS Detractor", "Name": "Detractor", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "NPS"}, {"DependsOnBucketId": 238, "DependsOnBucketfullname": "Categories.Insights.NPS Detractor", "Name": "NPS", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 239, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Silence", "Name": "Minimal Hold", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Minimal Hold Test"}, {"DependsOnBucketId": 239, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Negative Silence", "Name": "Minimal Hold Test", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 242, "DependsOnBucketfullname": "Categories.Contact Reasons.Apply for Something", "Name": "Apply for Something", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 242, "DependsOnBucketfullname": "Categories.Contact Reasons.Apply for Something", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 243, "DependsOnBucketfullname": "Categories.Contact Reasons.Buy or Order", "Name": "Buy or Order", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 243, "DependsOnBucketfullname": "Categories.Contact Reasons.Buy or Order", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 244, "DependsOnBucketfullname": "Categories.Contact Reasons.Cancel Event", "Name": "Cancel Event", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 244, "DependsOnBucketfullname": "Categories.Contact Reasons.Cancel Event", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 245, "DependsOnBucketfullname": "Categories.Contact Reasons.Cancel Something", "Name": "Cancel Something", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 245, "DependsOnBucketfullname": "Categories.Contact Reasons.Cancel Something", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 246, "DependsOnBucketfullname": "Categories.Contact Reasons.Cannot Pay", "Name": "Cannot Pay", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 246, "DependsOnBucketfullname": "Categories.Contact Reasons.Cannot Pay", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 247, "DependsOnBucketfullname": "Categories.Contact Reasons.Check Status", "Name": "Check Status", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 247, "DependsOnBucketfullname": "Categories.Contact Reasons.Check Status", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 248, "DependsOnBucketfullname": "Categories.Contact Reasons.Did Not Receive", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 248, "DependsOnBucketfullname": "Categories.Contact Reasons.Did Not Receive", "Name": "Did Not Receive", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 249, "DependsOnBucketfullname": "Categories.Contact Reasons.File a Complaint", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 249, "DependsOnBucketfullname": "Categories.Contact Reasons.File a Complaint", "Name": "File a Complaint", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 250, "DependsOnBucketfullname": "Categories.Contact Reasons.Fix Something", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 250, "DependsOnBucketfullname": "Categories.Contact Reasons.Fix Something", "Name": "Fix Something", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 251, "DependsOnBucketfullname": "Categories.Contact Reasons.Modify Schedule", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 251, "DependsOnBucketfullname": "Categories.Contact Reasons.Modify Schedule", "Name": "Modify Schedule", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 252, "DependsOnBucketfullname": "Categories.Contact Reasons.Need Information", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 252, "DependsOnBucketfullname": "Categories.Contact Reasons.Need Information", "Name": "Need Information", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 253, "DependsOnBucketfullname": "Categories.Contact Reasons.Need to Schedule", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 253, "DependsOnBucketfullname": "Categories.Contact Reasons.Need to Schedule", "Name": "Need to Schedule", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 254, "DependsOnBucketfullname": "Categories.Contact Reasons.Return or Replace", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 254, "DependsOnBucketfullname": "Categories.Contact Reasons.Return or Replace", "Name": "Return or Replace", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 255, "DependsOnBucketfullname": "Categories.Contact Reasons.Want to Pay", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 255, "DependsOnBucketfullname": "Categories.Contact Reasons.Want to Pay", "Name": "Want to Pay", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 256, "DependsOnBucketfullname": "Categories.Contact Reasons.Online Issues", "Name": "Contact Drivers", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 256, "DependsOnBucketfullname": "Categories.Contact Reasons.Online Issues", "Name": "Online Issues", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Contact Drivers"}, {"DependsOnBucketId": 260, "DependsOnBucketfullname": "Categories.Insights.Low Inaccurate Readings - Compression", "Name": "Irritation", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Low Inaccurate Readings - Irritation or Swelling"}, {"DependsOnBucketId": 260, "DependsOnBucketfullname": "Categories.Insights.Low Inaccurate Readings - Compression", "Name": "Low Inaccurate Readings - Irritation or Swelling", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 262, "DependsOnBucketfullname": "Categories.Insights.Inaccurate Readings - Confirmed", "Name": "Inaccurate Readings - Unconfirmed", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 262, "DependsOnBucketfullname": "Categories.Insights.Inaccurate Readings - Confirmed", "Name": "No Other Monitoring Devices Used Language", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Inaccurate Readings - Unconfirmed"}, {"DependsOnBucketId": 268, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Background Noise", "Name": "8. Background Noise", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Call Control_Background Noise"}, {"DependsOnBucketId": 268, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Background Noise", "Name": "Call Control_Background Noise", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 268, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Background Noise", "Name": "Call Control_Background Noise", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard"}, {"DependsOnBucketId": 268, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Background Noise", "Name": "Call Control_Background Noise", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Quality Scorecard - Match Rate"}, {"DependsOnBucketId": 268, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Background Noise", "Name": "Quality Scorecard", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 268, "DependsOnBucketfullname": "Categories.Quality Automation_Calls.Background Noise", "Name": "Quality Scorecard - Match Rate", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 280, "DependsOnBucketfullname": "Categories.Emotions.Irate", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 280, "DependsOnBucketfullname": "Categories.Emotions.Irate", "Name": "<PERSON><PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 281, "DependsOnBucketfullname": "Categories.Dissatisfaction.Unprofessional", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 281, "DependsOnBucketfullname": "Categories.Dissatisfaction.Unprofessional", "Name": "Unprofessional", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 282, "DependsOnBucketfullname": "Categories.Deflection.Hold <PERSON> <PERSON><PERSON><PERSON><PERSON>", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 282, "DependsOnBucketfullname": "Categories.Deflection.Hold <PERSON> <PERSON><PERSON><PERSON><PERSON>", "Name": "<PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 283, "DependsOnBucketfullname": "Categories.Dissatisfaction.Disagreements", "Name": "Disagreements", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 283, "DependsOnBucketfullname": "Categories.Dissatisfaction.Disagreements", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 284, "DependsOnBucketfullname": "Categories.Emotions.Anger", "Name": "Anger", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Emotion"}, {"DependsOnBucketId": 284, "DependsOnBucketfullname": "Categories.Emotions.Anger", "Name": "Negative Emotion", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 285, "DependsOnBucketfullname": "Categories.Risk.Unfair", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 285, "DependsOnBucketfullname": "Categories.Risk.Unfair", "Name": "Unfair", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 286, "DependsOnBucketfullname": "Categories.Deflection.Bounced Around", "Name": "Bounced Around", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 286, "DependsOnBucketfullname": "Categories.Deflection.Bounced Around", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 287, "DependsOnBucketfullname": "Categories.Dissatisfaction.Customer Confusion", "Name": "Customer Confusion", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 287, "DependsOnBucketfullname": "Categories.Dissatisfaction.Customer Confusion", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 288, "DependsOnBucketfullname": "Categories.Emotions.Fear", "Name": "Fear", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Emotion"}, {"DependsOnBucketId": 288, "DependsOnBucketfullname": "Categories.Emotions.Fear", "Name": "Negative Emotion", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 289, "DependsOnBucketfullname": "Categories.Dissatisfaction.Complaints", "Name": "Complaints Language", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer <PERSON><PERSON><PERSON><PERSON>"}, {"DependsOnBucketId": 289, "DependsOnBucketfullname": "Categories.Dissatisfaction.Complaints", "Name": "Customer <PERSON><PERSON><PERSON><PERSON>", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 290, "DependsOnBucketfullname": "Categories.Deflection.Dropped the Ball", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 290, "DependsOnBucketfullname": "Categories.Deflection.Dropped the Ball", "Name": "Dropped the Ball", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 291, "DependsOnBucketfullname": "Categories.Emotions.Disgust", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Emotion"}, {"DependsOnBucketId": 291, "DependsOnBucketfullname": "Categories.Emotions.Disgust", "Name": "Negative Emotion", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 292, "DependsOnBucketfullname": "Categories.Emotions.Disappointment", "Name": "Disappointment", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Negative Emotion"}, {"DependsOnBucketId": 292, "DependsOnBucketfullname": "Categories.Emotions.Disappointment", "Name": "Negative Emotion", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 293, "DependsOnBucketfullname": "Categories.Dissatisfaction.Frustration", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 293, "DependsOnBucketfullname": "Categories.Dissatisfaction.Frustration", "Name": "Frustration", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 294, "DependsOnBucketfullname": "Categories.Dissatisfaction.Want Someone Else", "Name": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>", "Type": "Category", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 294, "DependsOnBucketfullname": "Categories.Dissatisfaction.Want Someone Else", "Name": "Want Someone Else", "Type": "CategoryComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Dissatisfaction - <PERSON>mp<PERSON><PERSON>"}, {"DependsOnBucketId": 295, "DependsOnBucketfullname": "Categories.Dissatisfaction.Dissatisfaction - Comp<PERSON>ts", "Name": "Customer <PERSON><PERSON><PERSON><PERSON>", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 296, "DependsOnBucketfullname": "Categories.Emotions.Negative Emotion", "Name": "Customer <PERSON><PERSON><PERSON><PERSON>", "Type": "Score", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": null}, {"DependsOnBucketId": 296, "DependsOnBucketfullname": "Categories.Emotions.Negative Emotion", "Name": "Negative Emotion", "Type": "ScoreComponent", "Owner": null, "Group": null, "Active": "1", "DependenciesContentName": "Customer <PERSON><PERSON><PERSON><PERSON>"}]