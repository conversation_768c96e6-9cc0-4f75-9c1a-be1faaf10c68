{"BucketId": "515", "BucketFullname": "Categories.Emotions.Profanity", "BucketDescription": "For targeting calls with negative sentiment or abusive language", "UserEditable": false, "BucketName": "Profanity", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.<PERSON><PERSON><PERSON>", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T08:07:11.22-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30225, "Name": "Pissed Me Off", "Description": null, "ActionString": "[piss%|tick%+me+off:1][pissed|ticked+about|already|because|but|that|when|with:1][absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that`s|very|was|were|i`m|we`re|more+pissed|ticked+off:1.2]$OR$$OR$", "UserEntry": "(\"absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that's|very|was|were|i'm|we're|more pissed|ticked off\":1.2 OR \"pissed|ticked about|already|because|but|that|when|with\":1 OR \"piss*|tick* me off\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30224, "Name": "Bunch of BS", "Description": null, "ActionString": "[bunch|complete|load|this|that%|total|ton|absolute|pile+bull|it`s|its+shit|crap|shite:1.2][bunch|complete|load|this|that%|total|absolute|ton|pile|its|it`s+bullshit:1.2]$OR$", "UserEntry": "(\"bunch|complete|load|this|that*|total|absolute|ton|pile|its|it's bullshit\":1.2 OR \"bunch|complete|load|this|that*|total|ton|absolute|pile bull|it's|its shit|crap|shite\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30223, "Name": "Go to Hell", "Description": null, "ActionString": "[what+the+hell:1.2][go+to+hell:1.2][can+go+hell:1.2]$OR$$OR$", "UserEntry": "(\"can go hell\":1.2 OR \"go to hell\":1.2 OR \"what the hell\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30222, "Name": "F Word", "Description": null, "ActionString": "[what+the+fuck:1.2][stop|quit+fucking:1.2][fucking+issue%|problem%|wrong:1.2][mother+doctor%|fuck%:1][no+fuck%+way:1.2][that|this+is+fucked+up:1.2][go+fuck+off|your%:1.2][fuck+this|that+noise|shit:1.2][it|it`s|that%+fuck%+crazy:1][fuck%+absurd|bull%|insane|insanity|nuts|outrageous|ridiculous:1.2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"fuck* absurd|bull*|insane|insanity|nuts|outrageous|ridiculous\":1.2 OR \"it|it's|that* fuck* crazy\":1 OR \"fuck this|that noise|shit\":1.2 OR \"go fuck off|your*\":1.2 OR \"that|this is fucked up\":1.2 OR \"no fuck* way\":1.2 OR \"mother doctor*|fuck*\":1  OR \"fucking issue*|problem*|wrong\":1.2 OR \"stop|quit fucking\":1.2 OR \"what the fuck\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30221, "Name": "Screwed", "Description": null, "ActionString": "[get%|going|gonna|basically+screwed+over:1.2][screw%+me+over:1][he|she|someone|somebody|you|totally|they+screwed|screw+up:1]$OR$$OR$", "UserEntry": "(\"he|she|someone|somebody|you|totally|they screwed|screw up\":1 OR \"screw* me over\":1 OR \"get*|going|gonna|basically screwed over\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30220, "Name": "S Word", "Description": null, "ActionString": "[that`s|really|very+shitty|shit:1][shit+out+of+me:1][don`t|not|wouldn`t+give+shit:1.5][shitty|shit+service%|product|people|answer%:1][piece|pieces|jack|bunch|stupid|pile+shit:1.2][i|she|he|just|really|they+don`t|not|doesn`t+get|give+shit:1.2]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"I|she|he|just|really|they don't|not|doesn't get|give shit\":1.2 OR \"piece|pieces|jack|bunch|stupid|pile shit\":1.2 OR \"shitty|shit service*|product|people|answer*\":1 OR \"don't|not|wouldn't give shit\":1.5 OR \"shit out of me\":1 OR \"that's|really|very shitty|shit\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30219, "Name": "GD", "Description": null, "ActionString": "[god+damn+thing|thing`s|day:1][my|this+god+damn:1.2][goddamn+thing|thing`s|day:1][my|this+goddamn:1.2]$OR$$OR$$OR$", "UserEntry": "(\"my|this goddamn\":1.2 OR \"goddamn thing|thing`s|day\":1 OR \"my|this god damn\":1.2 OR \"god damn thing|thing`s|day\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30218, "Name": "B Word", "Description": null, "ActionString": "[stop|quit+bitch%:1][acting|act+like+bitch:1][don`t|no|not+act|acting|mean|trying+bitch%:1.2][bitch%+about:1][absolute|complete|crazy|dirty|dumb|fucking|need|son|stupid|total|useless+bitch%:1.2][you|your|you`re+a|are|being|little+bitch|witch:1.2]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"you|your|you're a|are|being|little bitch|witch\":1.2 OR \"absolute|complete|crazy|dirty|dumb|fucking|need|son|stupid|total|useless bitch*\":1.2 OR \"bitch* about\":1 OR \"don't|no|not act|acting|mean|trying bitch*\":1.2 OR \"acting|act like bitch\":1 OR \"stop|quit bitch*\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30217, "Name": "Crap", "Description": null, "ActionString": "[all|with|through+this+crap:1.2][bunch|load|piece+crap:1]$OR$<other>[all|with|through+this+crap:1.2][bunch|load|piece+crap:1.2]$OR$$NOTNEAR:0$$OR$", "UserEntry": "(((\"bunch|load|piece crap\":1.2 OR \"all|with|through this crap\":1.2) NOT NEAR:0 (other)) OR (\"bunch|load|piece crap\":1 OR \"all|with|through this crap\":1.2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30216, "Name": "A Word", "Description": null, "ActionString": "[he|he`s|pretty|she`s|she|such|you|you`re|your+are|an|being|just|much+asshole%|arsehole%:1.5][complete+asshole|arsehole:1.2]{call%|fucking|list|reach%|told+asshole%|arsehole%:1.2}$OR$$OR$", "UserEntry": "([call*|fucking|list|reach*|told asshole*|arsehole*]:1.2 OR \"complete asshole|arsehole\":1.2 OR \"he|he's|pretty|she's|she|such|you|you're|your are|an|being|just|much asshole*|arsehole*\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30108, "Name": "cussing", "Description": null, "ActionString": "[use+language+with:0.8]<speak><understand>$OR$[that+language:0.5]$NOTAFTER:3$[cuss%+at|out:0.5]$OR$$OR$", "UserEntry": "\"cuss* at|out\" OR (\"that language\" NOT AFTER (understand|speak)) OR \"use language with\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-03T14:25:30.777", "ComponentCount": 11}