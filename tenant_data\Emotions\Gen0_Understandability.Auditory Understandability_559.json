{"BucketId": "559", "BucketFullname": "Categories.Understandability.Auditory Understandability", "BucketDescription": "Language indicating trouble hearing due to telecom quality or equipment.", "UserEditable": false, "BucketName": "Auditory Understandability", "SectionName": "Understandability", "SectionNameDisplay": "Understandability", "SectionId": 162, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Understandability.Auditory Understandability", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-16T15:14:37.723-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 30, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -29927, "Name": "Anyone there", "Description": null, "ActionString": "[anyone|someone+there|their|line+hello:1.5][hi|hello|hey+anyone|someone+in|on|the+line%:1.5][hello|hi|hey+anyone|someone+there|their:1.5]$OR$$OR$", "UserEntry": "(\"hello|hi|hey anyone|someone there|their\":1.5 OR \"hi|hello|hey anyone|someone in|on|the line*\":1.5 OR \"anyone|someone there|their|line hello\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29926, "Name": "Bad connection", "Description": null, "ActionString": "[have|really|very|you+bad|poor|terrible|awful+connection%|connecting:2]", "UserEntry": "(\"have|really|very|you bad|poor|terrible|awful connection*|connecting\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29925, "Name": "Call is breaking up", "Description": null, "ActionString": "[you+are|really|keep+breaking+up:1.5][apologize|apologise|apologies|call%|mobile|cell%|conversation|connection%|kind|kinda|phone%|sorry|telephone%|voice%|your|you`re|are|you+broke|break|breaking+up:1.5][broke|breaking+up+again|bit|little|middle:1.5]$OR$$OR$", "UserEntry": "(\"broke|breaking up again|bit|little|middle\":1.5 OR \"apologize|apologise|apologies|call*|mobile|cell*|conversation|connection*|kind|kinda|phone*|sorry|telephone*|voice*|your|you're|are|you broke|break|breaking up\":1.5 OR \"you are|really|keep breaking up\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29924, "Name": "Can you hear me", "Description": null, "ActionString": "[if+you+can|cant|can`t|cannot|unable+hear+me:1.5]!25-90%[you+able+hear+me:1.5]!25-90%[can|could|did|do+you+hear+me:1.5]!25-90%$OR$$OR$", "UserEntry": "(\"can|could|did|do you hear me\":1.5 OR \"you able hear me\":1.5 OR \"if you can|cant|can't|cannot|unable hear me\":1.5){25%-90%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29923, "Name": "Can`t hear you", "Description": null, "ActionString": "<wanna><want><from><back>$OR$$OR$$OR$[not|wasn`t+able+hear+you:1.5][i+don`t|can`t+hear+you:1.2][cant|can`t|cannot|couldn`t|didn`t|not|unable|weren`t+hear+you|what|said:1]$OR$$OR$$NOTNEAR:0$", "UserEntry": "((\"cant|can't|cannot|couldn't|didn't|not|unable|weren't hear you|what|said\":1 OR \"i don't|can't hear you\":1.2 OR \"not|wasn't able hear you\":1.5) NOT NEAR:0 (back|from|want|wanna))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29922, "Name": "Can`t make out", "Description": null, "ActionString": "[can`t|cant|cannot|unable|couldn`t+make+out+said|saying:1.5]", "UserEntry": "(\"can't|cant|cannot|unable|couldn't make out said|saying\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29921, "Name": "Hard time hearing", "Description": null, "ActionString": "[hardly+hear:1][hard+of+hearing:1.2]$AND$[hard+time+hearing:1.2]$OR$", "UserEntry": "(\"hard time hearing\":1.2 OR \"hard of hearing\":1.2 \"hardly hear\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29920, "Name": "Headset issue", "Description": null, "ActionString": "[headset+breaking|broken|crappy|faulty|sucks|rubbish:1.5][headset+going|isn`t|not|quit|stopped+bad|good|great|right|working:1.5][having+difficult%|issue%|problem%|trouble%+headset%:1.5]$OR$$OR$", "UserEntry": "(\"having difficult*|issue*|problem*|trouble* headset*\":1.5 OR \"headset going|isn't|not|quit|stopped bad|good|great|right|working\":1.5 OR \"headset breaking|broken|crappy|faulty|sucks|rubbish\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29919, "Name": "Interference", "Description": null, "ActionString": "[line+staticky:1][hear%|lot|lots|there%|sound%+background%+noise%:1.5][getting|hear%|sound%+bad|lot|lots|some+echo|static:1.5][echo|interference|static+on+the+call|line%|phone|telephone:1.5]$OR$$OR$$OR$", "UserEntry": "(\"echo|interference|static on the call|line*|phone|telephone\":1.5 OR \"getting|hear*|sound* bad|lot|lots|some echo|static\":1.5 OR \"hear*|lot|lots|there*|sound* background* noise*\":1.5) OR ( \"line staticky\":1 )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29918, "Name": "Muffled", "Description": null, "ActionString": "[bit|kinda|little|real%|very|extremely|voice|sound%+muffl%:1.2]", "UserEntry": "(\"bit|kinda|little|real*|very|extremely|voice|sound* muffl*\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29917, "Name": "Trouble hearing", "Description": null, "ActionString": "[hard|difficult+to+hear:1.5][hard+hearing:1.2][barely|hardly+hear|heard:1.5][difficult%|issue%|problem%|trouble%+hearing:2]$OR$$OR$$OR$", "UserEntry": "(\"difficult*|issue*|problem*|trouble* hearing\":2 OR \"barely|hardly hear|heard\":1.5 OR \"hard hearing\":1.2 OR \"hard|difficult to hear\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29916, "Name": "What was that", "Description": null, "ActionString": "<again>[could+you+repeat+that:1.5][sorry+what+was+that:1.5]$AND$[didn`t|don`t+know+what+was+that:1.5]$OR$$NOTBEFORE:0.8$", "UserEntry": "((\"didn't|don't know what was that\":1.5 OR \"sorry what was that\":1.5 \"could you repeat that\":1.5) NOT BEFORE:0.8 (again))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29915, "Name": "You`re cutting out", "Description": null, "ActionString": "[cut%|drop%+in+and+out:1.5][cut%+out+again:1.4][apologiz%|apologis%|call|calls|cell%|connection%|conversation|constantly|keep%|phone%|sorry|telephone%|voice%|were|we`re|your|you`re+cutting+out:1.5]$OR$$OR$", "UserEntry": "(\"apologiz*|apologis*|call|calls|cell*|connection*|conversation|constantly|keep*|phone*|sorry|telephone*|voice*|were|we're|your|you're cutting out\":1.5 OR \"cut* out again\":1.4 OR \"cut*|drop* in and out\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-22T17:28:30.233", "ComponentCount": 13}