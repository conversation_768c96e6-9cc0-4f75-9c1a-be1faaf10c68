{"BucketId": null, "BucketFullname": "", "BucketDescription": "Locates parts of the call where the agent is performing troubleshooting with the customer.\nSpeakers - None SBB/Filters Included - Yes (Agent Group=Abbott_US_Eng_Voice)", "UserEditable": false, "BucketName": "Troubleshooting", "SectionName": "", "SectionNameDisplay": "", "SectionId": 58, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 3.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T17:50:56", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T17:50:56", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Troubleshooting", "Description": null, "ActionString": "[receive+any+display|message:2]!85%[recieved|replace%+sensor+display|message:2]!85%<wearing>=1!85%<using>=1!85%<use%>=1!85%<stop%>=1!85%<fell>=1!85%<sensor%>=1!85%$OR$$OR$$OR$$OR$$OR$[how+long:1.2]!85%[how+many+days:1.3]!85%[there|getting+display|error+message:2]!85%$AND$$OR$$NEAR:3$[important|information+from+app|applicaton:2]!85%[get+from+app|box:2]!85%[please|open|delete|uninstall+app|application:1.5]!85%[you+select:1]!85%<market>!85%<cob>!85%<version>!85%<full>!85%<soft%>!85%<address>!85%<deliver%>!85%<postcode>!85%$OR$$OR$$OR$$OR$$OR$$OR$$OR$<box>!85%<meter>!85%<read>!85%<reader%>!85%<cen%>!85%<sponsor%>!85%<sent%>!85%<sens%>!85%<ceiling>!85%<cereal>!85%<serial>!85%<number>!85%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[at|on+bottom|top|back|side:1]!85%$NEAR:3$$NOTNEAR:5$[serial+number:1.2]!85%[press+that|three|line%:1]!85%[gear|grill|%wheel+icon:1]!85%[right|left+corner:1]!85%[let|let`s+troubleshoot%:1.2]!85%<transfer%>!85%<they>!85%<apartment%>!85%<department%>!85%$OR$$OR$$OR$[troubleshoot%+for|the|your|this+you|issue%|problem:1.8]!85%{we|will|after|we`ll|perform|begin%|start%|do|i|i`ll|i`m|some|need|going|gonna|continu%|proceed|ahead|process|quick|first|make+troubleshoot%:2.2}!85%$OR$$NOTNEAR:4$[click+on:1]!85%{alarm%|signal|bluetooth+problem%|issue%|loss|%available|alert%|notification%|lack:1.2}!85%[how+day%|long+use%|using|wear%+sens%|cent%:2.5]!85%[provide+solution%:2]!85%{switch%|turn%+on+off:1.8}!85%[not+turn%+on+at+all:1.8]!85%[go|open+setting%:1.8]!85%[not+allow%:1]!85%[trouble|job+shoot%:1]!85%$NOTNEAR:5$[go+back+page:1.8]!85%[system+status|info%:1.2]!85%[three+dot%:1]!85%[scroll+down|up:1.2]!85%[%vent|error%|display+log%|code%:1.5]!85%<find>!85%<got>!85%<please>!85%<venue>!85%<menu>!85%<press>!85%<get>!85%<go>!85%<click>!85%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[back+help:1.5]!85%$NEAR:5$[click|option+off+load:3]!85%[location%+service%:1.8]!85%[sound%+setting%|sitting%:2.5]!85%[%phone+setting%|sitting%:2.5]!85%<connection%>!85%<location%>!85%<storage>!85%<sound%>!85%<modification%>!85%<notification%>!85%$OR$$OR$$OR$$OR$$OR$$NEAR:8$<place>!85%<put>!85%$OR$[hold|down+thirty+second%:3]!85%$NOTNEAR:2$[press+hold:1.5]!85%[press|click|tap|open|check+storage|general|system:2]!85%[go+to+nfc:1.5]!85%[attachment|attach+file%|photo%:3]!85%[file%+recent+picture%+click|press|tap|look:6]!85%[hold+same+time:5]!85%[home+lock+button:5]!85%[press+together:4]!85%[hard+reset:1]!85%[hold+button:4]!85%[phone+go+general|storage:1.8]!85%[look+for+storage|general|list|libra|link:2]!85%[click|tap|press+general|notification%|modification%|sound%|storage|location%|connection%:3]!85%[wait+few|couple+second%:1.8]!85%[search+free|freestyle|libra|link:2]!85%[go+apple|play+store:3]!85%[page+uninstall+option:3]!85%[have|there|see+option%+%install%:2]!85%[line%+top+left:2]!85%{storage|cache|cash|catch|data+clear%|delete:2.2}!85%[option+call%+notification%|modification%|sound%|storage|location%|connection%|catch|cache|cash|data:3]!85%[look|tap|click|press|leave+android+beam|meeting|been:3]!85%[install+the+driver:1.8]!85%[look+for+connection:2]!85%<order>!85%[general%|%phone+setting%|sitting%|check:3]!85%$NOTNEAR:5${arrow+vertical|horizontal:1.7}!85%{provid%|give|tell+serial|single+number+sens%|cent%|reader%|meter%|device:2.2}!85%[reset+centre|center|serial|sensor|meter|radar|reader|aerial|phone:1.5]!85%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"reset centre|center|serial|sensor|meter|radar|reader|aerial|phone\":1.5\nOR [provid*|give|tell serial|single number sens*|cent*|reader*|meter*|device]:2.2\nOR [arrow vertical|horizontal]:1.7\nOR \"general*|*phone setting*|sitting*|check\":3 NOT NEAR:5 (order)\nOR \"look for connection\":2\nOR \"install the driver\":1.8\nOR \"look|tap|click|press|leave android beam|meeting|been\":3\nOR \"option call* notification*|modification*|sound*|storage|location*|connection*|catch|cache|cash|data\":3\nOR [storage|cache|cash|catch|data clear*|delete]:2.2                                               \nOR \"line* top left\":2\nOR \"have|there|see option* *install*\":2\nOR \"page uninstall option\":3\nOR \"go apple|play store\":3\nOR \"search free|freestyle|libra|link\":2\nOR \"wait few|couple second*\":1.8\nOR \"click|tap|press general|notification*|modification*|sound*|storage|location*|connection*\":3\nOR \"look for storage|general|list|libra|link\":2\nOR \"phone go general|storage\":1.8\nOR \"hold button\":4\nOR \"hard reset\":1\nOR \"press together\":4\nOR \"home lock button\":5\nOR \"hold same time\":5\nOR \"file* recent picture* click|press|tap|look\":6\nOR \"attachment|attach file*|photo*\":3\nOR \"go to nfc\":1.5\nOR \"press|click|tap|open|check storage|general|system\":2\nOR \"press hold\":1.5\nOR \"hold|down thirty second*\":3 NOT NEAR:2 (put|place)\nOR (notification*|modification*|sound*|storage|location*|connection*) NEAR:8 \"*phone setting*|sitting*\":2.5\nOR \"sound* setting*|sitting*\":2.5\nOR \"location* service*\":1.8\nOR \"click|option off load\":3                                                                                                \nOR \"back help\":1.5 NEAR:5 (click|go|get|press|menu|venue|please|got|find)           \nOR \"*vent|error*|display log*|code*\":1.5\nOR \"scroll down|up\":1.2                                                                                                             \nOR \"three dot*\":1 \nOR \"system status|info*\":1.2                                                                                                         \nOR \"go back page\":1.8                                                                                                       \nOR \"trouble|job shoot*\":1 NOT NEAR:5 \"not allow*\":1                                                          \nOR \"go|open setting*\":1.8                                                                                                   \nOR \"not turn* on at all\":1.8                                                                                                  \nOR [switch*|turn* on off]:1.8                                                                                                \nOR \"provide solution*\":2                                                                                                                             \nOR \"how day*|long use*|using|wear* sens*|cent*\":2.5\nOR [alarm*|signal|bluetooth problem*|issue*|loss|*available|alert*|notification*|lack]:1.2\nOR \"click on\":1\nOR ([we|will|after|we'll|perform|begin*|start*|do|i|i'll|i'm|some|need|going|gonna|continu*|proceed|ahead|process|quick|first|make troubleshoot*]:2.2\nOR \"troubleshoot* for|the|your|this you|issue*|problem\":1.8) NOT NEAR:4 (department*|apartment*|they|transfer*)\nOR \"let|let's troubleshoot*\":1.2\nOR \"right|left corner\":1 \nOR \"gear|grill|*wheel icon\":1             \nOR \"press that|three|line*\":1\nOR \"serial number\":1.2 \nOR (\"at|on bottom|top|back|side\":1 NEAR:3 (number|serial|cereal|ceiling|sens*|sent*|sponsor*|cen*|reader*|read|meter|box)) NOT NEAR:5 (postcode|deliver*|address|soft*|full|version|cob|market)\nOR \"you select\":1                                                                                                                                                                                                                                                                                                                    \nOR \"please|open|delete|uninstall app|application\":1.5                                                                                                                                                                                                                                                  \nOR \"get from app|box\":2 OR \"important|information from app|applicaton\":2 OR (\"there|getting display|error message\":2 \"how many days\":1.3 OR \"how long\":1.2) NEAR:3 (sensor*|fell|stop*|use*|using|wearing)=AGENT OR \"recieved|replace* sensor display|message\":2 OR \"receive any display|message\":2){+85%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "TSG - DSPC", "Description": null, "ActionString": "[what+specific+%phone%:1.5][the+software+version+is:1.5][make|model+of|your|the+mobile|phone|device:2][is+that+right|correct:1]<version>$NEAR:3$[may|know|which+state+calling+from:2][os+version+please:1][what|how+is|about+os|version|service|software:2][please+provide+software|ios|dos|version%:2][gave+message+that+said|stated:2][current+meter|reader|device+using:2]{what+meter|reader|product+type|using|use:2}[currently+sensor|center+tellingsaying:2][what+exact+error+message:2][what+was+message+exactly|displayed|shown|saw:2]<replace><state%>$OR$[error|display+message:1]$NEAR:5$[use|using+reader+or+application:2][may|know|ask+product%+currently+using:2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"may|know|ask product* currently using\":2 OR \"use|using reader or application\":2                                                                                                                                                                                                         \nOR \"error|display message\":1 NEAR:5 (state*|replace)                                                                                                                                                                                                                                                                                      OR \"what was message exactly|displayed|shown|saw\":2                                                                                                                                                                                                                                                          OR \"what exact error message\":2                                                                                                                                                                                                                                                                                                  \nOR \"currently sensor|center tellingsaying\":2                                                                                                                                                                                                                                                                                    OR [what meter|reader|product type|using|use]:2                                                                                                                                                                                                                                                                     \nOR \"current meter|reader|device using\":2                                                                                                                                                                                                                                                                                     OR \"gave message that said|stated\":2                                                                                                                                                                                                                                                                                         OR \"please provide software|ios|dos|version*\":2                                                                                                                                                                                                                                                                         \nOR \"what|how is|about OS|version|service|software\":2                                                                                                                                                                                                                                                                   OR \"OS version please\":1                                                                                                                                                                                                                                                                                                                      OR \"may|know|which state calling from\":2                                                                                                                                                                                                                                                                                   \nOR (version NEAR:3 \"is that right|correct\":1)                                                                                                                                                                                                                                                                                   OR \"make|model of|your|the mobile|phone|device\":2                                                                                                                                                                                                                                                                \nOR \"the software version is\":1.5                                                                                                                                                                                                                                                                                                   \nOR \"what specific *phone*\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "TSG - Insertion", "Description": null, "ActionString": "[before+fell+off:1.5]<address><product><center%><sensor%><day%>$OR$$OR$$OR$[how+long|many:1]$NEAR:5$$NOTBEFORE:5$[how+long+wear%|using:1][what|type|kind+sensor%|center|freestyle|libre+using|use:2]$OR$$OR$$OR$", "UserEntry": "\"what|type|kind sensor*|center|freestyle|libre using|use\":2                                                                                                                                                                                                                                                                                 \nOR \"how long wear*|using\":1                                                                                                                                                                                                                                                                                                            \nOR ((\"how long|many\":1 NEAR:5 (day*|sensor*|center*|product)) NOT BEFORE:5 address)                                                                                                                                                                                                  OR \"before fell off\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Autopass", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T17:50:56", "ComponentCount": 4}