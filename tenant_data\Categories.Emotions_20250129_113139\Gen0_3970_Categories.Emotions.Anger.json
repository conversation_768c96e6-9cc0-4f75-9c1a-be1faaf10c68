{"BucketId": "3970", "BucketFullname": "Categories.Emotions.Anger", "BucketDescription": "Detects when anger is present on a call.", "UserEditable": false, "BucketName": "Anger", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Anger", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2023-05-04T11:01:55.18-04:00", "Creator": "<EMAIL>", "Version": 3, "LanguageTag": "en", "EffectiveDate": "2023-05-04T11:02:04.56", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -24058, "Name": "<PERSON>", "Description": "", "ActionString": "[very|so|absolutely|extra|especially|quite|really|super|extremely|highly+grumpy|crabby|bitchy|snappy|piss|pissed|anger|madness|hellish|livid|horrified|irate|battered|upset|aggravating|irritated|angry|annoyed|aggravated|furious|agitated|perturbed|exasperated|stubborn|spotty|grouchy|witchy|runaround|excruciating|fail|terrible|pushy|nosy|jerk|snotty|rude|harsh|offensive|abrasive|invasive|arrogant|insensitive|insecure|noxious|hurtful|frazzled|belligerent|audacity|traumatic|nasty|painful:1]", "UserEntry": "\"very|so|absolutely|extra|especially|quite|really|super|extremely|highly grumpy|crabby|bitchy|snappy|piss|pissed|anger|madness|hellish|livid|horrified|irate|battered|upset|aggravating|irritated|angry|annoyed|aggravated|furious|agitated|perturbed|exasperated|stubborn|spotty|grouchy|witchy|runaround|excruciating|fail|terrible|pushy|nosy|jerk|snotty|rude|harsh|offensive|abrasive|invasive|arrogant|insensitive|insecure|noxious|hurtful|frazzled|belligerent|audacity|traumatic|nasty|painful\":1", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24057, "Name": "negative_sentiment-angry", "Description": "", "ActionString": "[taken+aback:1][beside%+myself:0.8][fuck+fuck:0.5][jerked+around:1][not+happy|pleased|nice|cool:0.8][just+great:0.8]<shit><damn><hate><sucks><painful><nasty><traumatic><audacity><belligerent><frazzled><hurtful><noxious><insecure><insensitive><arrogant><invasive><abrasive><offensive><harsh><rude><snotty><jerk><nosy><pushy><terrible><fail><excruciating><runaround><witchy><grouchy><spotty><stubborn><exasperated><perturbed><disgusted><agitated><furious><aggravated><annoyed><angry><irritat%><aggravating><upset%><battered><irate><horrified><livid><hellish><madness><anger><piss%><snappy><bitchy><crabby><grumpy>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((grumpy|crabby|bitchy|snappy|piss*|anger|madness|hellish|livid|horrified|irate|battered|upset*|aggravating|irritat*|angry|annoyed|aggravated|furious|agitated|disgusted|perturbed|exasperated|stubborn|spotty|grouchy|witchy|runaround|excruciating|fail|terrible|pushy|nosy|jerk|snotty|rude|harsh|offensive|abrasive|invasive|arrogant|insensitive|insecure|noxious|hurtful|frazzled|belligerent|audacity|traumatic|nasty|painful|sucks|hate|damn|shit) OR (\"just great\":.8) OR (\"not happy|pleased|nice|cool\":.8) OR (\"jerked around\":1) OR (\"fuck fuck\") OR (\"beside* myself\":.8) OR (\"taken aback\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24056, "Name": "negative_sentiment-angry-complaint", "Description": "", "ActionString": "<complain>[complaint+against:1][formal|official+complaint:1.2]$OR$$OR$", "UserEntry": "((\"formal|official complaint\":1.2 OR \"complaint against\":1) OR (complain))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24055, "Name": "negative_sentiment-angry-frustrated", "Description": "", "ActionString": "[fresh+ration%:1][fresh+traded:1]<freshened><frustrat%>$OR$$OR$$OR$", "UserEntry": "((frustrat*|freshened) OR (\"fresh traded\":1) OR (\"fresh ration*\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24054, "Name": "negative_sentiment-angry-signs_of_anger", "Description": "", "ActionString": "<swore><swear><swears><cuss><yelling><swearing><cursing><cussing><yelled><yells><scream%><argu%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(argu*|scream*|yells|yelled|cussing|cursing|swearing|yelling|cuss|swears|swear|swore)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24053, "Name": "negative_sentiment-lying", "Description": "", "ActionString": "<ignoring><bugging><avoiding><lying><dying><losing>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(losing|dying|lying|avoiding|bugging|ignoring)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24052, "Name": "negative_sentiment-really angry", "Description": "", "ActionString": "[extremely|highly+angry|irate|upset:1.2]", "UserEntry": "(\"extremely|highly angry|irate|upset\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24051, "Name": "negative_sentiment-impatient", "Description": "", "ActionString": "<impatient><boredom><restless><inpatient>$OR$$OR$$OR$", "UserEntry": "(inpatient|restless|boredom|impatient)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24050, "Name": "negative_sentiment-inconvenient", "Description": "", "ActionString": "<inconvenient>", "UserEntry": "(inconvenient)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24049, "Name": "negative_sentiment-awful", "Description": "", "ActionString": "[heart+wrenching:1]<miserable><emotional><distraught><sad><devasta%><depressed><sadness><grievance><mushy><whiny><cranky>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((cranky|whiny|mushy|grievance|sadness|depressed|devasta*|sad|distraught|emotional|miserable) OR (\"heart wrenching\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24048, "Name": "negative_sentiment-catastrophic", "Description": "", "ActionString": "[god+forbid:1][god+for+bid|bed:1.2]$OR$<catastroph%>$OR$", "UserEntry": "((catastroph*) OR (\"god for bid|bed\":1.2 OR \"god forbid\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24047, "Name": "negative_sentiment-chaotic", "Description": "", "ActionString": "[jam+packed|backed|jacked|hacked:1.2]<convoluted><unprepared><unorganized><chaotic><disorganized>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((disorganized|chaotic|unorganized|unprepared|convoluted) OR (\"jam packed|backed|jacked|hacked\":1.2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24046, "Name": "negative_sentiment-shocked", "Description": "", "ActionString": "<alarm%><shock%><surpris%><shocked>[blown+away:0.5]<stunned><flabbergasted>$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(flabbergasted|stunned|\"blown away\"|shocked|surpris*|shock*|alarm*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-05-04T11:02:04.56", "ComponentCount": 13}