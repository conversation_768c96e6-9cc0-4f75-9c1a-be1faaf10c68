ImportScore.py - CallMiner Score Import Utility
===============================================

OVERVIEW:
---------
ImportScore.py is a comprehensive utility for importing CallMiner scores and their configurations into the FEAPI system. This script provides robust import capabilities with validation, dependency resolution, and error handling to support migration, restoration, and bulk configuration workflows.

PRIMARY PURPOSE:
----------------
- Import score configurations into CallMiner FEAPI
- Restore scores from backup files
- Migrate scores between environments
- Bulk create scores from templates
- Validate and resolve score dependencies
- Support configuration management workflows

KEY FEATURES:
-------------

1. FLEXIBLE INPUT FORMATS:
   - Import from Excel workbooks (exported by ExportScore.py)
   - Import from JSON files with score configurations
   - Import from CSV files with score data
   - Support for individual score files or bulk imports
   - Template-based score creation

2. COMPREHENSIVE VALIDATION:
   - Pre-import validation of score data
   - Algorithm validation and syntax checking
   - Dependency validation and resolution
   - Duplicate detection and handling
   - Data integrity checks and corrections

3. DEPENDENCY RESOLUTION:
   - Automatic resolution of category dependencies
   - Handling of score-to-score relationships
   - Missing dependency detection and reporting
   - Dependency order optimization for imports

4. FLEXIBLE IMPORT OPTIONS:
   - Create new scores or update existing ones
   - Selective import with filtering capabilities
   - Dry-run mode for validation without changes
   - Rollback capabilities for failed imports
   - Incremental import support

COMMAND LINE INTERFACE:
-----------------------
The script supports extensive command-line configuration:

Basic Usage:
python ImportScore.py --InputFile "Scores_backup.xlsx"

Common Parameters:
--InputFile: Path to input file (Excel, JSON, or CSV)
--ScoreFilter: Filter scores to import by name pattern
--FolderFilter: Filter by folder name
--OutputPrefix: Customize output file naming
--LogRoot: Specify log file directory
--DryRun: Validate without making changes
--OverwriteExisting: Overwrite existing scores
--CreateMissing: Create missing dependencies

Example Commands:
python ImportScore.py --InputFile "backup.xlsx" --DryRun True
python ImportScore.py --InputFile "scores.json" --OverwriteExisting True
python ImportScore.py --InputFile "data.csv" --ScoreFilter "Quality.*"

CONFIGURATION:
--------------
Configuration is managed through INI files and command-line arguments:

INI File Structure (ImportScore.py.INI):
[FEAPI]
API = feapi
Tenant = YourTenant
LogRoot = logs
DEBUG = False

[ImportScore]
InputFile = 
ScoreFilter = 
FolderFilter = 
OutputPrefix = Import
DryRun = False
OverwriteExisting = False
CreateMissing = True
ValidateOnly = False

MAIN FUNCTIONS:
---------------

1. LoadInputData(config):
   - Loads score data from various input formats
   - Validates file format and structure
   - Converts data to standardized internal format
   - Handles encoding and format issues

2. ValidateScores(helper, config, score_data):
   - Validates score data integrity
   - Checks for required fields and valid values
   - Validates scoring algorithms and syntax
   - Identifies potential conflicts and issues

3. ResolveDependencies(helper, config, score_data):
   - Analyzes score dependencies on categories
   - Resolves dependency order for import
   - Identifies missing category dependencies
   - Handles complex scoring relationships

4. ImportScores(helper, config, validated_data):
   - Performs the actual score import
   - Creates or updates scores in FEAPI
   - Handles errors and rollback scenarios
   - Provides progress tracking and reporting

5. GenerateReport(helper, config, import_results):
   - Creates detailed import reports
   - Summarizes success and failure statistics
   - Documents any issues or warnings
   - Provides recommendations for resolution

INPUT FILE FORMATS:
-------------------

1. Excel Format (.xlsx):
   - Scores worksheet with score data
   - Dependencies worksheet with relationship data
   - Algorithms worksheet with scoring logic
   - Must match ExportScore.py output format

2. JSON Format (.json):
   - Array of score objects with complete configurations
   - Nested structure for algorithms and weightings
   - Metadata and dependency information included

3. CSV Format (.csv):
   - Flat structure with score properties
   - Separate files for dependencies and algorithms
   - Header row with column definitions

VALIDATION PROCESS:
-------------------

1. STRUCTURE VALIDATION:
   - Verify required fields are present
   - Check data types and formats
   - Validate score name conventions
   - Ensure proper folder structure

2. ALGORITHM VALIDATION:
   - Check scoring algorithm syntax
   - Validate weighting and threshold values
   - Verify category references in algorithms
   - Ensure mathematical consistency

3. DEPENDENCY VALIDATION:
   - Verify referenced categories exist
   - Check for valid category references
   - Validate folder assignments
   - Ensure proper dependency relationships

IMPORT PROCESS:
---------------

1. PRE-IMPORT PHASE:
   - Load and validate input data
   - Resolve dependencies and ordering
   - Check for conflicts with existing data
   - Generate import plan and validation report

2. IMPORT EXECUTION:
   - Create scores in dependency order
   - Handle errors and retry logic
   - Update existing scores if specified
   - Track progress and maintain transaction log

3. POST-IMPORT VALIDATION:
   - Verify all scores were created successfully
   - Validate score configurations in FEAPI
   - Check algorithm execution and results
   - Generate final import report

SCORE DATA ELEMENTS:
--------------------
The import handles comprehensive score information:

- Basic Properties: Name, Description, Folder assignment
- Configuration: Active status, Priority settings
- Algorithms: Scoring methods, Weightings, Thresholds
- Categories: Referenced categories and their weights
- Filters: Applied filters and conditions
- Metadata: Version information, Generation data
- Dependencies: Related categories and other scores

ERROR HANDLING:
---------------
- Comprehensive error logging with detailed context
- Graceful handling of API failures and timeouts
- Rollback capabilities for partial failures
- Clear error messages with resolution guidance
- Validation warnings for potential issues

ROLLBACK CAPABILITIES:
----------------------
- Transaction logging for rollback support
- Backup of existing scores before modification
- Selective rollback of failed imports
- Recovery from partial import failures
- Audit trail of all changes made

PERFORMANCE CONSIDERATIONS:
---------------------------
- Efficient batch processing for large imports
- Optimized API calls to minimize requests
- Memory-efficient processing of large datasets
- Progress indicators for long-running imports
- Configurable batch sizes and timeouts

SECURITY FEATURES:
------------------
- Secure credential management
- Validation of input data for security issues
- Proper authentication token handling
- Audit trail of import operations
- Access control validation

USE CASES:
----------

1. BACKUP RESTORATION:
   - Restore scores from backup files
   - Disaster recovery scenarios
   - Environment restoration after failures

2. MIGRATION SUPPORT:
   - Migrate scores between environments
   - Cross-tenant score migration
   - Development to production deployment

3. BULK CONFIGURATION:
   - Mass creation of similar scores
   - Template-based score deployment
   - Standardization across environments

4. DEVELOPMENT WORKFLOWS:
   - Import test data for development
   - Configuration synchronization
   - Automated deployment processes

INTEGRATION:
------------
- Designed to work with ExportScore.py output
- Integrates with other FEAPI utilities
- Supports automation and CI/CD workflows
- Compatible with configuration management tools

TROUBLESHOOTING:
----------------
Common issues and solutions:
- File format errors: Verify input file format and structure
- Algorithm errors: Check scoring algorithm syntax and references
- Dependency errors: Verify all referenced categories exist
- API errors: Verify credentials and permissions
- Validation failures: Review validation report for specific issues

This utility is essential for CallMiner administrators who need to import, migrate, or restore score configurations across different environments and tenants.
