{"BucketId": "564", "BucketFullname": "Categories.Emotions.^Appreciate-ThankYou - Emotions Index", "BucketDescription": "Cloned from Base Appreciateion category for use in Emotions Index", "UserEditable": false, "BucketName": "^Appreciate-ThankYou - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.^Appreciate-ThankYou - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-17T12:18:25.87-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 30, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -29860, "Name": "Appreciate", "Description": null, "ActionString": "<answering><time><patience>$OR$$OR$[appreciate|appreciated|appreciates+it|that+so|very+much:1.1]$NOTNEAR:3$", "UserEntry": "\"appreciate|appreciated|appreciates it|that so|very much\" NOT NEAR:3 (patience|time|answering)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29859, "Name": "Big help", "Description": null, "ActionString": "[big+help|helps:0.5]<that`ll><would><was><been>$OR$$OR$$OR$$BEFORE:1.4$", "UserEntry": "(been|was|would|that`ll) BEFORE:1.4 (\"big help|helps\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29858, "Name": "I appreciate everything", "Description": null, "ActionString": "[appreciate+everything:1.2]", "UserEntry": "(\"appreciate everything\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29857, "Name": "I appreciate what you are doing", "Description": null, "ActionString": "[appreciate+what+you|you`re|your+doing:2]", "UserEntry": "(\"appreciate what you|you`re|your doing\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29856, "Name": "I appreciate your being nice", "Description": null, "ActionString": "[appreciate+your+nice|polite|pleasant:1.4]", "UserEntry": "(\"appreciate your nice|polite|pleasant\":1.4)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29855, "Name": "I appreciate your help", "Description": null, "ActionString": "[i|really|much+appreciate+your|you`re|you+help|helping|assist|assisting|assistance:2.1]", "UserEntry": "(\"i|really|much appreciate your|you`re|you help|helping|assist|assisting|assistance\":2.1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29854, "Name": "I appreciate your taking the time", "Description": null, "ActionString": "[appreciate+your|you`re+time|effort|efforts:1.4]", "UserEntry": "(\"appreciate your|you`re time|effort|efforts\":1.4)", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29853, "Name": "I appreciate your understanding", "Description": null, "ActionString": "[appreciate+your|you`re+understand|understanding:1.4]", "UserEntry": "(\"appreciate your|you`re understand|understanding\":1.4)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29852, "Name": "Thank you for being kind", "Description": null, "ActionString": "[your+kindness:0.3][very+good:0.2]<great><fantastic><wonderful><pleasant><polite><kind>$OR$$OR$$OR$$OR$$OR$$OR$$OR$[for+being:0.5][thank+you:0.3]<thanks>$OR$$BEFORE:0.4$$BEFORE:0.4$", "UserEntry": "(thanks|\"thank you\":0.3) BEFORE:0.4 (\"for being\") BEFORE:0.4 (kind|polite|pleasant|wonderful|fantastic|great|\"very good\":0.2|\"your kindness\":0.3)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29851, "Name": "Thank you for everything", "Description": null, "ActionString": "[for+everything:0.7][thank+you:0.7]<thanks>$OR$$BEFORE:1$", "UserEntry": "(thanks|\"thank you\":0.7) BEFORE:1.0 (\"for everything\":0.7)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29850, "Name": "Thank you for helping", "Description": null, "ActionString": "<holding><calling>$OR$[how+can+you+help:2.1][assisting+me:0.7][your|you`re|you+assistance:0.5][your|you`re+help:0.5]$OR$$OR$[thank+you:0.7]<thanks>$OR$$BEFORE:3$$NOTAFTER:3$$NOTBEFORE:3$", "UserEntry": "((thanks|\"thank you\":0.7) BEFORE (\"your|you`re help\"|\"your|you`re|you assistance\"|\"assisting me\":0.7)) NOT AFTER (\"how can you help\":2.1) NOT BEFORE (calling|holding)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29849, "Name": "Thank you much", "Description": null, "ActionString": "[thank+you+much:1]", "UserEntry": "\"thank you much\":1", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29848, "Name": "Thanks for helping", "Description": null, "ActionString": "[thank|thanks+for+help|helping:1][thank|thanks+for+your+help:1]$OR$", "UserEntry": "(\"thank|thanks for your help\":1) OR (\"thank|thanks for help|helping\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29847, "Name": "Thanks for taking the time", "Description": null, "ActionString": "[thank|thanks+your+time:1]", "UserEntry": "\"thank|thanks your time\":1", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29846, "Name": "You have been so nice", "Description": null, "ActionString": "<pleasant><polite><sweetheart>[sweet+heart:0.7]<blessing>[very+nice:0.7]$OR$$OR$$OR$$OR$$OR$<been><you><you`ve>[you+have:0.7]$OR$$OR$$OR$$BEFORE:1$", "UserEntry": "(\"you have\":0.7|you`ve|you|been) BEFORE:1.0 (\"very nice\":0.7|blessing|\"sweet heart\":0.7|sweetheart|polite|pleasant)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29845, "Name": "You have been wonderful", "Description": null, "ActionString": "<helpful><wonderful>$OR$<quite>[more+than:0.7]<fairly><extremely><very>$OR$$OR$$OR$$OR$<been><you`ve>[you+have:0.7]$OR$$OR$$BEFORE:1$$BEFORE:1$", "UserEntry": "(\"you have\":0.7|you`ve|been) BEFORE:1.0 (very|extremely|fairly|\"more than\":0.7|quite) BEFORE:1.0 (wonderful|helpful)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29844, "Name": "You`ve been a great help", "Description": null, "ActionString": "[been|were|very|great+helpful:1]", "UserEntry": "\"been|were|very|great helpful\":1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29790, "Name": "thank you double much", "Description": null, "ActionString": "[thank+you+so|very+so|very+much:1.5]", "UserEntry": "\"thank you so|very so|very much\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-22T17:07:47.663", "ComponentCount": 18}