'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''
import FEAPI_Helpers

import requests
import json
import os
import pandas as pd
pd.set_option('io.excel.xlsx.writer', 'openpyxl')
import argparse
import configparser


# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token file.
# the URL will look like this: https://feapi.callminer.net//swagger/ui/index?JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9anjOeIiPmLGX_q5DqTme12Gmx2L-ztYMgFwg66b4vI
# eveyrthing after JWT= is what should be copied into token.txt


#Global Variables
DEBUG = True
API = "feapi"
BucketFullnameFilter = ''
GetDependencies = True
IgnoreFilterDependencies = False


def read_ini_file(file_path):
    if os.path.exists(file_path):
        config = configparser.ConfigParser()
        config.read(file_path)

        global DEBUG, API, BucketFullnameFilter, GetDependencies, IgnoreFilterDependencies
        DEBUG = config.getboolean('Parameters', 'DEBUG')
        API = config.get('Parameters', 'API')
        BucketFullnameFilter = config.get('Parameters', 'BucketFullnameFilter')
        GetDependencies = config.get('Parameters', 'GetDependencies') == 1
        IgnoreFilterDependencies = config.get('Parameters', 'GetDependencies') == 1
    else:
        logger.info(f"INI file at {file_path} does not exist")

#Setup Timestamps
from datetime import datetime
now = datetime.now()
current_date_time = now.strftime("%Y-%m-%dT%H:%M:%S")
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

import logging
# Set up logging configuration
logger = logging.getLogger('GetCategoryContent')
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

# Create handlers
if not os.path.exists("logs"):
    os.makedirs("logs")
console_handler = logging.StreamHandler()  # Log to console
file_handler = logging.FileHandler(f'logs\\GetCategoryContent-{timestamp}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'logs\\GetCategoryContent-{timestamp}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'logs\\GetCategoryContent-{timestamp}.warnings.txt')  # Log to errors.txt


# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)



#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, API)









# Generate current timestamp
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")





#------------------------------------------------------------------------------------------------------------


def GetGenerations():
    # Get Generations dataframe from API
    responsejson = helper.ApiGetJson(f'https://{API}.callminer.net/api/v2/categories/generation')
    df_generations = pd.DataFrame(responsejson)
    df_generations.rename(columns={'BucketID': 'BucketId'}, inplace=True)
    df_generations.drop(columns=['SearchID'], inplace=True)
    return df_generations

def GetAllCategories():
    #Get list of all categories from API
    responsejson = helper.ApiGetJson(f'https://{API}.callminer.net/api/v2/categories')
    return pd.json_normalize(responsejson)


#------------------------------------------------------------------------------------------------------------


def GetCategory(bucketrow, df_AllCategories, xls_filename, json_output_dir):
    global IgnoreFilterDependencies
    deplist_forthisCategory = []
    bucketid = bucketrow.BucketId.iloc[0]
    gen = bucketrow.Generation.iloc[0]
    bucketfullname = bucketrow.BucketFullname.iloc[0]

    #Get who depends on this cat
    usedinstring =  helper.GetUsedInString(bucketid,API)

    # Get category information
    requesturl = f'https://{API}.callminer.net/api/v2/categories/{bucketid}?components=true'
    responsePayload = helper.ApiGetJson(requesturl)
    responsePayload['BucketId'] = f'{bucketid}'

    bucketfilename = f"Gen{gen}_{bucketfullname}_{bucketid}"  # Use a meaningful name for the sheet
    bucketfilename = helper.cleanfilename(bucketfilename, "_")
    json_filename = os.path.join(json_output_dir, f"{bucketfilename}.json")
    # Write JSON object to a file
    with open(json_filename, 'w') as json_file:
        json.dump(responsePayload, json_file, indent=4)  # `indent=4` for pretty-logger.infoing

    # Update excel file with components for this category
    df_SearchComps = pd.json_normalize(responsePayload, record_path=['SearchComponents'], meta=['BucketId', 'BucketFullname'])
    df_SearchComps['FilterString'] = ""
    df_SearchComps['DependsOn'] = ""

    # Get Dependencies in SearchComponent Filters
    # Update SearchComponent keys
    data = responsePayload

    for sc in data['SearchComponents']:
        deplist_forthisSC = []
        sc_name = sc["Name"]
        filterstring, filterdeplist = helper.ParseFilters(sc['Filters'],df_AllCategories)
        # Update Filterstring
        df_SearchComps.loc[
            df_SearchComps['Id'] == sc['Id'], 'FilterString'] = filterstring

        for dep in filterdeplist:
            if IgnoreFilterDependencies==False:
                logger.warning(f"Adding Filter Dependency on BucketID({dep}={helper.GetCategoryFullname(dep,df_AllCategories)}) from FILTER of SearchComponent ({bucketfullname}.{sc_name})")
                #add BucketID to deplist if not alreayd added
                if dep not in deplist_forthisSC:
                    deplist_forthisSC.append(dep)
            else:
                logger.warning(
                    f"IGNORING Filter Dependency on BucketID({dep}={helper.GetCategoryFullname(dep, df_AllCategories)}) from FILTER of SearchComponent ({bucketfullname}.{sc_name})")

        # Add Filters for this search component to the output file
        filterfilename = os.path.join(json_output_dir, f"Filters.xlsx")
        if filterstring != "":
            helper.LogFilter(bucketfullname, sc['Name'], filterstring, filterfilename)
            logger.info(
                f"Saving Filters for SearchComponent {bucketfullname}.{sc_name} = {filterstring}")

        # Check for Category Dependency
        matches,founddeplist = helper.ParseEQL(sc['UserEntry'], df_AllCategories)
        if matches !="":
            logger.warning(f"Found Dependencies ({matches}) in UserEntry of SearchComponent ({bucketfullname}.{sc_name})")

        for dep in founddeplist:
            #add BucketID to deplist if not alreayd added
            if dep not in deplist_forthisSC:
                deplist_forthisSC.append(dep)

        filtered_df = df_AllCategories[df_AllCategories['BucketId'].isin(deplist_forthisSC)]
        deplist_string = filtered_df['BucketFullname'].str.cat(sep=',')
        if deplist_string != "":
            logger.info(
                f"Adding cumulative DependsOn and FilterString for this SearchComponent {bucketfullname}.{sc_name}\tDependsOn={deplist_string} \tFilterString={filterstring}")

        df_SearchComps.loc[df_SearchComps['Id'] == sc['Id'], 'FilterString'] = filterstring
        df_SearchComps.loc[df_SearchComps['Id'] == sc['Id'], 'DependsOn'] = deplist_string

        #add deplist for this SC to deplistfor this Category
        for dep in deplist_forthisSC:
            if dep not in deplist_forthisCategory:
                deplist_forthisCategory.append(dep)

    helper.AppenddataframeAsSheetToExcel(xls_filename,bucketfilename, df_SearchComps)

    return deplist_forthisCategory, usedinstring



def GetCategories(json_output_dir:str, bucketfullnamefilter:str):
    # Dump the json and xls of each category with its components

    xls_filename = os.path.join(json_output_dir, f"{bucketfullnamefilter}.xlsx")


    #Retrieve the Generations so the Categories can be ordered correctly
    df_generations = GetGenerations()
    #helper.AppenddataframeAsSheetToExcel(xls_filename,"Generations",df_generations)

    #Retrieve the list of all categories
    df_AllCategories = GetAllCategories()
    # Remove "Categories." from the "BucketFullNames" column
    df_AllCategories['BucketFullname'] = df_AllCategories['BucketFullname'].str.replace('Categories.', '')
    df_AllCategories['UsedIn'] = ""
    # Move the UsedIn column to the 3rd spot
    col = df_AllCategories.pop('UsedIn')
    df_AllCategories.insert(2, 'UsedIn', col)

    # Add Generations to AllCats
    df_AllCategories = pd.merge(df_generations[['BucketId', 'Generation']], df_AllCategories, on='BucketId', how='right')
    # Sort the DataFrame by col1 in a case-insensitive manner
    df_AllCategories['bfn_lower'] = df_AllCategories['BucketFullname'].str.lower()  # Create a new column with lowercase values of col1
    df_AllCategories = df_AllCategories.sort_values(by='bfn_lower', ignore_index=True)
    # Drop the temporary lowercase column
    df_AllCategories = df_AllCategories.drop(columns='bfn_lower')


    # Filter the Categories by the FilterString
    CatsToGet = df_AllCategories[df_AllCategories['BucketFullname'].str.contains(BucketFullnameFilter)]['BucketId'].astype(int).tolist()
    df_AllCategories['DependsOn'] = ""
    # Move the Dependson column to the 3rd spot
    col = df_AllCategories.pop('DependsOn')
    df_AllCategories.insert(3, 'DependsOn', col)

    #Loop Thru each Category, retrieving it and outputting it to JSON
    i = 0
    extradeplist = []
    while i < len(CatsToGet):
        BucketID = CatsToGet[i]

        BucketRow = df_AllCategories[df_AllCategories['BucketId'] == BucketID]
        bucketfullname = BucketRow.BucketFullname.iloc[0]
        logger.info("-------------------------------------------------------------------------------------------")
        logger.info(f"RETRIEVING ({i+1}/{len(CatsToGet)}) - Bucketid={BucketID} - {bucketfullname}")

        # Process this Category, and get any Dependencies Found
        deplist_foundinthisCategory, usedin = GetCategory(BucketRow,df_AllCategories, xls_filename, json_output_dir)

        #Update UsedIn
        df_AllCategories.loc[df_AllCategories['BucketId'] == BucketID, 'UsedIn'] = usedin

        for depcatid in deplist_foundinthisCategory:
            # Add Any Dependencies not already in the list
            if GetDependencies:
                if depcatid not in CatsToGet:
                    logger.info(f"Adding Dependency on BucketID ({depcatid}={helper.GetCategoryFullname(depcatid,df_AllCategories)}) to list of Categories to retrieve ")
                    CatsToGet.append(depcatid)
                    extradeplist.append(depcatid)
                else:
                    logger.info(f"Already exporting Dependency on BucketID ({depcatid}={helper.GetCategoryFullname(depcatid, df_AllCategories)})")
            else:
                logger.info(
                    f"Ignoring Dependency on BucketID ({depcatid}={helper.GetCategoryFullname(depcatid, df_AllCategories)}) - GetDependencies set to False...")

        #Update the deplist_forthiscat
        filtered_df = df_AllCategories[df_AllCategories['BucketId'].isin(deplist_foundinthisCategory)]
        deplist_string = filtered_df['BucketFullname'].str.cat(sep=',')
        df_AllCategories.loc[df_AllCategories['BucketId'] == BucketID, 'DependsOn'] = deplist_string

        logger.info(f"EXPORT COMPLETE ( {i+1} / {len(CatsToGet)} ) - Category ( {bucketfullname} ) ")

        i +=1

    #df_AllCategories = df_AllCategories.sort_values(by=['Generation', 'BucketFullname'])
    helper.AppenddataframeAsSheetToExcel(xls_filename,"ALL Categories",df_AllCategories)
    helper.move_last_sheet_to_front(xls_filename)

    # Get the df of Categories Exported by filtering by extradeplist and export to excel
    df_filteredCats = df_AllCategories[df_AllCategories['BucketId'].isin(extradeplist)]
    helper.AppenddataframeAsSheetToExcel(xls_filename, "Extra DependsOn Categories", df_filteredCats)
    helper.move_last_sheet_to_front(xls_filename)

    #Get the df of Categories Exported by filtering by CatsToGet and export to excel as first sheet
    df_filteredCats= df_AllCategories[df_AllCategories['BucketId'].isin(CatsToGet)]
    helper.AppenddataframeAsSheetToExcel(xls_filename,  "Exported Categories",df_filteredCats)
    helper.move_last_sheet_to_front(xls_filename)



def main():
    global DEBUG, API, BucketFullnameFilter, GetDependencies, IgnoreFilterDependencies

    ini_file_path = 'GetCategoryContent.config.ini'
    read_ini_file(ini_file_path)

    parser = argparse.ArgumentParser(description='Argument Parser')
    parser.add_argument('--DEBUG', action='store_true', default=False,  help='Set DEBUG flag')
    parser.add_argument('--API', type=str, help='Specify API name')
    parser.add_argument('--BucketFullnameFilter', type=str, help='Specify bucket fullname filter')
    parser.add_argument('--GetDependencies', choices=[0,1], default=0,  help='Set GetDependencies flag')
    parser.add_argument('--IgnoreFilterDependencies', choices=[0,1], default=0, help='Ignore Dependencies found in filters')

    args = parser.parse_args()

    if args.DEBUG is not None:
        DEBUG = args.DEBUG
    if args.API is not None:
        API = args.API
    if args.BucketFullnameFilter is not None:
        BucketFullnameFilter = args.BucketFullnameFilter
    if args.GetDependencies is not None:
        GetDependencies = (args.GetDependencies == 1)
    if args.IgnoreFilterDependencies is not None:
        IgnoreFilterDependencies = (args.IgnoreFilterDependencies ==1)

    print(f'DEBUG: {DEBUG}')
    print(f'API: {API}')
    print(f'BucketFullnameFilter: {BucketFullnameFilter}')
    print(f'GetDependencies: {GetDependencies}')
    print(f'IgnoreFilterDependencies: {IgnoreFilterDependencies}')

    # Global Helper functions ------------------------------------------------------------------------------
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, API)

    # Check if the directory exists and append timestamp if it does
    tenant = helper.API_CheckTenantName(API)
    outputName = f"{BucketFullnameFilter}"
    json_output_dir = f"{tenant}/{outputName}"
    if os.path.exists(json_output_dir):
        logger.warning(
            f"Output Folder ( {json_output_dir} ) already exists - creating copy as {json_output_dir}_{timestamp}")
        json_output_dir = f"{json_output_dir}_{timestamp}"

    # Create a directory for JSON files
    os.makedirs(json_output_dir, exist_ok=True)
    logger.info(f"Retrieving Content from Tenant( {tenant}) into {json_output_dir}")

    GetCategories(json_output_dir,BucketFullnameFilter)
    

if __name__ == '__main__':
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")

