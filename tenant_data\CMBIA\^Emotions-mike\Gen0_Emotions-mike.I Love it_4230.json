{"BucketId": "4230", "BucketFullname": "Categories.Emotions-mike.I Love it", "BucketDescription": "language indication extreme like for something", "UserEditable": false, "BucketName": "I Love it", "SectionName": "Emotions-mike", "SectionNameDisplay": "Emotions-mike", "SectionId": 2326, "RetroStartDate": null, "CategoryDisplay": "Emotions-mike.I Love it", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T20:45:23.247-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T20:44:41", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -22321, "Name": "i love it", "Description": null, "ActionString": "[help|assist|put:0.2][don`t|can`t|not:0.2][i|i`d|would+love|adore|delighted|thrilled|glad|cherish+to|it|that|this:1]$NOTNEAR:0$$NOTBEFORE:0.3$", "UserEntry": "( \"i|i'd|would love|adore|delighted|thrilled|glad|cherish to|it|that|this\":1 NOT NEAR:0 ( \"don't|can't|not\") ) NOT BEFORE:0.3 \"help|assist|put\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T20:45:23.61", "ComponentCount": 1}