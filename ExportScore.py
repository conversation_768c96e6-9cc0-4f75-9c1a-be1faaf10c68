'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''

import FEAPI_Helpers

import json
import os,sys
import pandas as pd
import re
pd.set_option('io.excel.xlsx.writer', 'openpyxl')
import argparse
import configparser
#Setup Timestamps
from datetime import datetime

# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token file.
# the URL will look like this: https://feapi.callminer.net//swagger/ui/index?JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9anjOeIiPmLGX_q5DqTme12Gmx2L-ztYMgFwg66b4vI
# eveyrthing after JWT= is what should be copied into token.txt

class config:



    # Default values
    '''
    [FEAPI]
    '''
    API = "feapi"
    DataRoot = "tenant_data/scores"
    LogRoot = "logs"
    DEBUG = False
    LoggerLevel = "DEBUG"

    '''
    [GetCategory]
    '''
    Tenant = "specify target tenant"
    NameFilter = "specify scorename to export"
    IgnoreFilterDependencies = False
    OutputPrefix=""



    API_timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    json_output_dir = ""
    @staticmethod
    def FilenameInOutputFolder(filename):
        return os.path.join(config.json_output_dir, filename)

    @staticmethod
    def xls_filename():
        return config.FilenameInOutputFolder ( f"{config.OutputPrefix}.Exported_Categories.xlsx")

    @staticmethod
    def AllCategories_xls_filename():
        return config.FilenameInOutputFolder (  f"AllCategories.xlsx")

    @staticmethod
    def PyFilename():
        os.path.basename(sys.argv[0])

    @staticmethod
    def SetOutputfolder(defaultfolder):
        # Create handlers and log folders
        if not os.path.exists(config.DataRoot):
            os.makedirs(config.DataRoot)

        # Check if the directory exists and append timestamp if it does
        if config.OutputPrefix == "" or config.OutputPrefix == None:
            config.OutputPrefix = helper.cleanfilename(defaultfolder)
        json_output_dir = f"{config.DataRoot}/{config.Tenant}/{config.OutputPrefix}"
        if os.path.exists(json_output_dir):
            logger.info(
                f"Output Folder ( {json_output_dir} ) already exists - creating copy as {json_output_dir}_{config.timestamp}")
            json_output_dir = f"{json_output_dir}_{config.timestamp}"
        config.json_output_dir = json_output_dir


    @staticmethod
    def read_ini_file(file_path:str=""):
        if file_path=="":
            file_path = f"{os.path.basename(sys.argv[0])}.INI"
        if os.path.exists(file_path):
            config.strip_lines_with_triple_single_quotes(file_path,file_path)
            configparse = configparser.ConfigParser()
            configparse.read(file_path)

            config.DEBUG = configparse.get('FEAPI', 'DEBUG').strip('"')  == 1
            config.LoggerLevel = configparse.get('FEAPI', 'LoggerLevel').strip('"')
            config.API = configparse.get('FEAPI', 'API').strip('"')
            config.Tenant = configparse.get('FEAPI', 'Tenant').strip('"')
            config.LogRoot = configparse.get('FEAPI', 'LogRoot').strip('"')
            config.DataRoot = configparse.get('FEAPI', 'DataRoot').strip('"')
        else:
            print(f"ERROR: INI file at {file_path} does not exist **************************")

    @staticmethod
    def strip_lines_with_triple_single_quotes(input_file: str, output_file: str):
        with open(input_file, 'r') as file:
            lines = file.readlines()
            # Filter out lines that are just ''' (including any whitespace)
        cleaned_lines = [line for line in lines if line.strip() != "'''"]
        # Write the cleaned lines to the output file
        with open(output_file, 'w') as file:
            file.writelines(cleaned_lines)

config.read_ini_file()

import logging
# Set up logging configuration
logger = logging.getLogger(config.PyFilename())
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

#reset logger level if passed in args
level_string = config.LoggerLevel.upper()  # Ensure case-insensitivity
level = logging.getLevelName(level_string)
if isinstance(level, int):  # Check if a valid level was found
    logging.getLogger().setLevel(level)
    print(f"Logging level set to: {logging.getLevelName(level)}")
else:
    print(f"Invalid logging level string passed as config: {level_string}")

# Create handlers and log folders
if not os.path.exists(config.LogRoot):
    os.makedirs(config.LogRoot)

logprefix = f"{config.LogRoot}/{config.PyFilename()}-{config.timestamp}"
file_handler = logging.FileHandler(f'{logprefix}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'{logprefix}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'{logprefix}.warnings.txt')  # Log to errors.txt

console_handler = logging.StreamHandler()  # Log to console


# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)



#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, config)


#------------------------------------------------------------------------------------------------------------








def ExportScore(scorejson, df_AllCategories):

    #Dump ScoreJSON to file in the tenant folder
    scorefilename = f"{config.NameFilter}_{scorejson['Id']}"  # Use a meaningful name for the sheet
    scorefilename = helper.cleanfilename(scorefilename, "_")
    json_filename = os.path.join(config.json_output_dir, f"{scorefilename}.json")

    # Write JSON object to a file
    with open(json_filename, 'w') as json_file:
        json.dump(scorejson, json_file, indent=4)  # `indent=4` for pretty-logger.infoing

    if not config.IgnoreFilterDependencies:

        #Get ScoreFilters
        filterstring = ""
        filterdeplist = []
        filterstring,filterdeplist = helper.ParseFilters(scorejson["Filters"],df_AllCategories)
        logger.info(f"Found {len(filterdeplist)} score filter dependencies = {filterstring}")

        # Add Score's Dependencies to Excel file

        # Get ScoreComponent Dependencies
        scString, scdeplist = helper.ParseSearchComponent(scorejson, df_AllCategories)
        logger.info(f"Found {len(scdeplist)} score component dependencies = {scString}")

        #Add Score Dependencies to List

        # Get ScoreComponent Fitlers
        scfilterstring = ""
        scfilterdeplist = []
        scfilterstring, scfilterdeplist = helper.ParseFilters(scorejson["Filters"], df_AllCategories)
        logger.info(f"Found {len(scfilterdeplist)} score component filter dependencies = {scfilterstring}")

        #Add sc filters to output




#------------------------------------------------------------------------------------------------------------


def GetScores():
    # Dump the json and xls of each category with its components
    xls_filename = config.xls_filename()

    helper.GetFilterDependencyTables(config.json_output_dir, ignore_alerts=True)

    #Getall categories with Genrations added
    df_AllCategories = helper.GetAllCategories()

    # #SetupSCoreFilters
    # columns = ['ScoreID', 'ScoreName', 'SearchComponentID', 'SearchComponentName', 'FilterBucketID',
    #            'FilterBucketFullname', 'FilterString']
    # # Create an empty DataFrame with the specified columns
    # df_ScoreFilters = pd.DataFrame(columns=columns)
    #
    # # SetupScoreDependencies
    # columns = ['ScoreID', 'ScoreName', 'SearchComponentID', 'SearchComponentName', 'FilterBucketID',
    #            'FilterBucketFullname', 'FilterString']
    # # Create an empty DataFrame with the specified columns
    # df_ScoreFilters = pd.DataFrame(columns=columns)

    # Get all scores json
    allscoresJSON, df_allscores = helper.API_GetAllScores()

    #Find out if Scorename in NameFilter to export
    for s in allscoresJSON:
        scorename = df_allscores[df_allscores['Id'] == s["Id"]]['ScoreFullname'].values[0]

        if FEAPI_Helpers.FEAPI_Helper.MatchesAnyRegex(config.NameFilter, scorename):
            ExportScore(s, df_AllCategories)

    #copy AllCategories sheet to AllCategories.xlsx
    helper.AppenddataframeAsSheetToExcel(config.AllCategories_xls_filename(), "ALL Categories", df_AllCategories)


def str_to_bool(s):
    if s.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif s.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')


def main():
    #get cmdline args and overwrite defaults from INI
    config.read_ini_file()

    parser = argparse.ArgumentParser(description='Argument Parser')
    parser.add_argument('--DEBUG', action='store_true', default=False,  help='Set DEBUG flag')
    parser.add_argument('--Tenant', type=str, default=config.Tenant, help='Specify target tenant (must match tenant that token is for)')
    parser.add_argument('--NameFilter', type=str, default = config.NameFilter, help='Specify scorename to export as a comma-separated list of regexes. Fodlers should be separated with forward-slash')
    parser.add_argument('--OutputPrefix', nargs='?', type=str, default=config.OutputPrefix, help='Specify output prefix to use. Leave blank to use NameFilter')
    parser.add_argument('--IgnoreFilterDependencies',  type=str_to_bool, default=config.IgnoreFilterDependencies,  help='Ignore Dependencies found in filters, default False')
    parser.add_argument('--API', nargs='?', type=str, default=config.API, help='API (feapi/feapif) (optional)')

    args = parser.parse_args()
    logger.info(f"ARGUMENTS PASSED:-------------------------------------------")
    logger.info("\n".join(FEAPI_Helpers.FEAPI_Helper.show_all_attributes_and_values(args)))

    # Loop through attributes of A and copy from B if they match
    FEAPI_Helpers.FEAPI_Helper.UpdateObjectA_WithB(config,args)

    # Global Helper functions ------------------------------------------------------------------------------
    global helper
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, config)

    # Check if the tenant matches
    logger.info(f"Checking if API tenant matches target Config.Tenant ({config.Tenant})")
    config.tenant = helper.API_CheckTenantName(config.Tenant)

    # Create a directory for JSON files
    if config.NameFilter== "":
        logger.error(f"Must specify a NameFilter.  Exiting...")
        exit(1)

    config.SetOutputfolder(config.NameFilter)
    logger.info(f"Creating outputfolder ({config.json_output_dir})")
    os.makedirs((config.json_output_dir))

    logger.info(f"CONFIG: -----------------------------------------------------")
    logger.info("\n".join(FEAPI_Helpers.FEAPI_Helper.show_all_attributes_and_values(config)))

    logger.info(f"Retrieving Score from Tenant( {config.tenant}) into {config.json_output_dir}")
    GetScores()
    

if __name__ == '__main__':
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")

