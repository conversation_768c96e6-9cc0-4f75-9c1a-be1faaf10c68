{"AlertID": -2147483544, "Name": "testalert", "SearchComponents": [{"Id": -18835, "Name": "testalert_sc2", "Description": null, "ActionString": "<test>", "UserEntry": "test", "SpeakerList": "1", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -18835, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Agent", "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": "<PERSON>", "Operand2": "", "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "CoolOffPeriodInSeconds": 10.0, "Threshold": 1.0, "FolderID": -32529, "Description": "For testing API import/export 2", "LastUpdated": "2025-04-01T15:01:00", "LastUser": "<PERSON>@callminer.com", "MessageType": {"MessageTypeID": 5, "Name": "Other", "Color": "#64899c", "Priority": 0, "SlnInstallationId": null}, "Message": {"Message": "<p>test alert2</p>", "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-28T15:00:00"}, "Version": 1, "Status": 1, "SlnInstallationID": null, "Creator": "<PERSON>@callminer.com", "Created": "2025-03-28T15:00:00", "AlertFullname": "API Test.testalert"}