{"BucketId": "3801", "BucketFullname": "Categories.KA Emotions.Emotions Index Negative Experience", "BucketDescription": "Words and phrases that indicate customer is angry", "UserEditable": false, "BucketName": "Emotions Index Negative Experience", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotions Index Negative Experience", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:46:28.363-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-02-28T09:27:24.563", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25437, "Name": "Disagreement", "Description": null, "ActionString": "[no+way+no+how:1.2]<afford><cancel>$OR$[no+way:0.8]$NEAR:3$[gonna|going+do:1][doing+that:0.8]$OR$[no+way:0.8]$BEFORE:1$<paying><pay>$OR$[no+way:0.8]$NEAR:2$[don’t|never|won`t|not+agree:0.8]<disagree%>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "disagree* OR \"don’t|never|won't|not agree\":.8 OR \"no way\":.8 NEAR:2 (pay|paying) OR \"no way\":.8 BEFORE:1 (\"doing that\":.8 OR \"gonna|going do\":1) OR \"no way\":.8 NEAR:3 (cancel|afford) OR \"no way no how\":1.2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25436, "Name": "Failure to follow through", "Description": null, "ActionString": "<ivr><rejected>$NOTAFTER:1$[not+issued|notified|expected|filed|corrected|elected:1]<reflected><reject><infected><decline%><denied>$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(denied|decline*|infected|reject|reflected) OR \"not issued|notified|expected|filed|corrected|elected\":1 OR (rejected NOT AFTER:1 ivr)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25435, "Name": "<PERSON><PERSON>", "Description": null, "ActionString": "[your|her|their|agent|agents|representative%|his|compan%|guy|guys|who`s+fault:1][not|isn`t+my|our+fault%:1]$OR$", "UserEntry": "\"not|isn't my|our fault*\":1 OR \"your|her|their|agent|agents|representative*|his|compan*|guy|guys|who's fault\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25434, "Name": "Horrific", "Description": null, "ActionString": "<catastroph%><accident><wreck>$OR$<horrif%>$NOTNEAR:1$$OR$", "UserEntry": "(horrif* NOT NEAR:1 (wreck|accident)) OR catastroph*", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25433, "Name": "Mistake", "Description": null, "ActionString": "[mixed|mix+up:0.6][he|she|someone|somebody|you|totally|they+screwed|screw+up:0.6]$OR$", "UserEntry": "\"he|she|someone|somebody|you|totally|they screwed|screw up\":0.6 OR \"mixed|mix up\":.6", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25432, "Name": "Not helpful", "Description": null, "ActionString": "[not|wasn`t|isn`t+helping|helped|helper|helpful:0.6][doesn`t|didn`t|couldn`t|wouldn`t|not|refus%+help:1]$OR$", "UserEntry": "\"doesn't|didn't|couldn't|wouldn't|not|refus* help\":1 OR \"not|wasn't|isn't helping|helped|helper|helpful\":0.6", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25431, "Name": "Offensive", "Description": null, "ActionString": "[take|find%|taking|taken+offense:1]", "UserEntry": "\"take|find*|taking|taken offense\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25430, "Name": "Overwhelmed", "Description": null, "ActionString": "[little|too|bit|not|being|getting|it`s|so|very|just|was|i`m+overwhelm%:2.5]<flustered><stress%>$OR$$OR$", "UserEntry": "(stress*|flustered) OR \"little|too|bit|not|being|getting|it's|so|very|just|was|i'm overwhelm*\":2.5", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25429, "Name": "Product or Service References", "Description": null, "ActionString": "[not|isn`t|wasn`t+reliable:1][shitty+service:0.6][piece|pieces+shit:1]{negativ%+impact%:1}{lack%+service%:1}$OR$$OR$$OR$$OR$", "UserEntry": "[lack* service*]:1 OR [negativ* impact*]:1 OR \"piece|pieces shit\":1 OR \"shitty service\":.6 OR \"not|isn't|wasn't reliable\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25428, "Name": "<PERSON><PERSON><PERSON>", "Description": null, "ActionString": "[they|you|stop|agent|kept|keep%|quit|try|trying|tried+pressur%+me:1.5][feel%|felt+rush%|pressur%:1][forcing+me|us:1][gonna|going+force+me|us:1.5][was|am|be+forced+upon|cancel|assume|sign|contract|on|into|call:1.5]<not><wasn`t>$OR$[very|being|little|too|overly+pushy:1.2]$NOTAFTER:1$[agent%|rep|reps%|representative%+pushy:1.5]$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"agent*|rep|reps*|representative* pushy\":1.5 OR (\"very|being|little|too|overly pushy\":1.2 NOT AFTER:1 (wasn't|not)) OR \"was|am|be forced upon|cancel|assume|sign|contract|on|into|call\":1.5 OR \"gonna|going force me|us\":1.5 OR \"forcing me|us\":1 OR \"feel*|felt rush*|pressur*\":1 OR \"they|you|stop|agent|kept|keep*|quit|try|trying|tried pressur* me\":1.5", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25427, "Name": "<PERSON><PERSON>", "Description": null, "ActionString": "[not+be+rude:2][coming|come+across+rude:2][don`t+mean|need+rude:2.4][try%+rude:1.5]$OR$$OR$$OR$", "UserEntry": "\"try* rude\":1.5 OR \"don't mean|need rude\":2.4 OR \"coming|come across rude\":2 OR \"not be rude\":2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25426, "Name": "Terrible Experience", "Description": null, "ActionString": "[mean+nasty:1][nasty+e-mail|email|message%|call:1.2]{terrible+experience:1}[terrible+service|review%|wait%:1]$OR$$OR$$OR$", "UserEntry": "\"terrible service|review*|wait*\":1 OR [terrible experience]:1 OR \"nasty e-mail|email|message*|call\":1.2 OR \"mean nasty\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25425, "Name": "Uninformed", "Description": null, "ActionString": "<were><am><i`m>$OR$$OR$<uninformed>$NOTBEFORE:1$[wasn`t|isn`t|not+knowledgeable:0.8]<truck><dumb>$NOTBEFORE:1$<unreliable><clueless>$OR$$OR$$OR$$OR$", "UserEntry": "(clueless|unreliable) OR (dumb NOT BEFORE:1 truck) OR \"wasn't|isn't|not knowledgeable\":.8 OR (uninformed NOT BEFORE:1 (i'm|am|were))", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25424, "Name": "Yelling", "Description": null, "ActionString": "[yell%+at+me:1.2][call+yell:1.5][stop+yelling:1]$OR$$OR$", "UserEntry": "\"stop yelling\":1 OR \"call yell\":1.5 OR \"yell* at me\":1.2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-02-28T09:27:24.563", "ComponentCount": 14}