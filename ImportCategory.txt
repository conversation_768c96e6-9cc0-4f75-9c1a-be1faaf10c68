ImportCategory.py - CallMiner Category Import Utility
=====================================================

OVERVIEW:
---------
ImportCategory.py is a comprehensive utility for importing CallMiner categories and their configurations into the FEAPI system. This script provides robust import capabilities with validation, dependency resolution, and error handling to support migration, restoration, and bulk configuration workflows.

PRIMARY PURPOSE:
----------------
- Import category configurations into CallMiner FEAPI
- Restore categories from backup files
- Migrate categories between environments
- Bulk create categories from templates
- Validate and resolve category dependencies
- Support configuration management workflows

KEY FEATURES:
-------------

1. FLEXIBLE INPUT FORMATS:
   - Import from Excel workbooks (exported by ExportCategory.py)
   - Import from JSON files with category configurations
   - Import from CSV files with category data
   - Support for individual category files or bulk imports
   - Template-based category creation

2. COMPREHENSIVE VALIDATION:
   - Pre-import validation of category data
   - Dependency validation and resolution
   - Duplicate detection and handling
   - Data integrity checks and corrections
   - Configuration compliance validation

3. DEPENDENCY RESOLUTION:
   - Automatic resolution of category dependencies
   - Handling of circular dependencies
   - Missing dependency detection and reporting
   - Dependency order optimization for imports

4. FLEXIBLE IMPORT OPTIONS:
   - Create new categories or update existing ones
   - Selective import with filtering capabilities
   - Dry-run mode for validation without changes
   - Rollback capabilities for failed imports
   - Incremental import support

COMMAND LINE INTERFACE:
-----------------------
The script supports extensive command-line configuration:

Basic Usage:
python ImportCategory.py --InputFile "Categories_backup.xlsx"

Common Parameters:
--InputFile: Path to input file (Excel, JSON, or CSV)
--CategoryFilter: Filter categories to import by name pattern
--SectionFilter: Filter by section name
--OutputPrefix: Customize output file naming
--LogRoot: Specify log file directory
--DryRun: Validate without making changes
--OverwriteExisting: Overwrite existing categories
--CreateMissing: Create missing dependencies

Example Commands:
python ImportCategory.py --InputFile "backup.xlsx" --DryRun True
python ImportCategory.py --InputFile "categories.json" --OverwriteExisting True
python ImportCategory.py --InputFile "data.csv" --CategoryFilter "Quality.*"

CONFIGURATION:
--------------
Configuration is managed through INI files and command-line arguments:

INI File Structure (ImportCategory.py.INI):
[FEAPI]
API = feapi
Tenant = YourTenant
LogRoot = logs
DEBUG = False

[ImportCategory]
InputFile = 
CategoryFilter = 
SectionFilter = 
OutputPrefix = Import
DryRun = False
OverwriteExisting = False
CreateMissing = True
ValidateOnly = False

MAIN FUNCTIONS:
---------------

1. LoadInputData(config):
   - Loads category data from various input formats
   - Validates file format and structure
   - Converts data to standardized internal format
   - Handles encoding and format issues

2. ValidateCategories(helper, config, category_data):
   - Validates category data integrity
   - Checks for required fields and valid values
   - Validates category names and hierarchies
   - Identifies potential conflicts and issues

3. ResolveDependencies(helper, config, category_data):
   - Analyzes category dependencies
   - Resolves dependency order for import
   - Identifies missing dependencies
   - Handles circular dependency detection

4. ImportCategories(helper, config, validated_data):
   - Performs the actual category import
   - Creates or updates categories in FEAPI
   - Handles errors and rollback scenarios
   - Provides progress tracking and reporting

5. GenerateReport(helper, config, import_results):
   - Creates detailed import reports
   - Summarizes success and failure statistics
   - Documents any issues or warnings
   - Provides recommendations for resolution

INPUT FILE FORMATS:
-------------------

1. Excel Format (.xlsx):
   - Categories worksheet with category data
   - Dependencies worksheet with relationship data
   - Filters worksheet with filter configurations
   - Must match ExportCategory.py output format

2. JSON Format (.json):
   - Array of category objects with complete configurations
   - Nested structure for search components and filters
   - Metadata and dependency information included

3. CSV Format (.csv):
   - Flat structure with category properties
   - Separate files for dependencies and filters
   - Header row with column definitions

VALIDATION PROCESS:
-------------------

1. STRUCTURE VALIDATION:
   - Verify required fields are present
   - Check data types and formats
   - Validate category name conventions
   - Ensure proper hierarchy structure

2. BUSINESS RULE VALIDATION:
   - Check category naming standards
   - Validate search component syntax
   - Verify filter logic and references
   - Ensure section and folder assignments

3. DEPENDENCY VALIDATION:
   - Verify referenced categories exist
   - Check for circular dependencies
   - Validate score and alert references
   - Ensure proper dependency ordering

IMPORT PROCESS:
---------------

1. PRE-IMPORT PHASE:
   - Load and validate input data
   - Resolve dependencies and ordering
   - Check for conflicts with existing data
   - Generate import plan and validation report

2. IMPORT EXECUTION:
   - Create categories in dependency order
   - Handle errors and retry logic
   - Update existing categories if specified
   - Track progress and maintain transaction log

3. POST-IMPORT VALIDATION:
   - Verify all categories were created successfully
   - Validate category configurations in FEAPI
   - Check dependency relationships
   - Generate final import report

ERROR HANDLING:
---------------
- Comprehensive error logging with detailed context
- Graceful handling of API failures and timeouts
- Rollback capabilities for partial failures
- Clear error messages with resolution guidance
- Validation warnings for potential issues

ROLLBACK CAPABILITIES:
----------------------
- Transaction logging for rollback support
- Backup of existing categories before modification
- Selective rollback of failed imports
- Recovery from partial import failures
- Audit trail of all changes made

PERFORMANCE CONSIDERATIONS:
---------------------------
- Efficient batch processing for large imports
- Optimized API calls to minimize requests
- Memory-efficient processing of large datasets
- Progress indicators for long-running imports
- Configurable batch sizes and timeouts

SECURITY FEATURES:
------------------
- Secure credential management
- Validation of input data for security issues
- Proper authentication token handling
- Audit trail of import operations
- Access control validation

USE CASES:
----------

1. BACKUP RESTORATION:
   - Restore categories from backup files
   - Disaster recovery scenarios
   - Environment restoration after failures

2. MIGRATION SUPPORT:
   - Migrate categories between environments
   - Cross-tenant category migration
   - Development to production deployment

3. BULK CONFIGURATION:
   - Mass creation of similar categories
   - Template-based category deployment
   - Standardization across environments

4. DEVELOPMENT WORKFLOWS:
   - Import test data for development
   - Configuration synchronization
   - Automated deployment processes

INTEGRATION:
------------
- Designed to work with ExportCategory.py output
- Integrates with other FEAPI utilities
- Supports automation and CI/CD workflows
- Compatible with configuration management tools

TROUBLESHOOTING:
----------------
Common issues and solutions:
- File format errors: Verify input file format and structure
- Dependency errors: Check for missing or circular dependencies
- API errors: Verify credentials and permissions
- Validation failures: Review validation report for specific issues
- Performance issues: Adjust batch sizes and timeout settings

This utility is essential for CallMiner administrators who need to import, migrate, or restore category configurations across different environments and tenants.
