{"BucketId": "3971", "BucketFullname": "Categories.Emotions.Disgust", "BucketDescription": "Detects when disgust is present on a call.", "UserEditable": false, "BucketName": "<PERSON><PERSON><PERSON><PERSON>", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Dis<PERSON>t", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2023-05-04T11:02:05.133-04:00", "Creator": "<EMAIL>", "Version": 3, "LanguageTag": "en", "EffectiveDate": "2023-05-04T11:02:15.55", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -24045, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Description": "", "ActionString": "[very|so|absolutely|extra|especially|quite|really|super|extremely|highly+dissatisfied|unsatisfied|unhappy|displeased|unacceptable|discouraged|outrageous|hurtful|damaging|damaged|damage|tarnish|tarnished|violation|nightmare|nightmares|atrocious|dreadful|brutal|miserable|crappy|horrific|horrendous|disgusting|lousy|horrifying|horrid|laughable|difficult|worthless|challenge|challenging|hassle|panic|hard|criminal:0.5]", "UserEntry": "\"very|so|absolutely|extra|especially|quite|really|super|extremely|highly dissatisfied|unsatisfied|unhappy|displeased|unacceptable|discouraged|outrageous|hurtful|damaging|damaged|damage|tarnish|tarnished|violation|nightmare|nightmares|atrocious|dreadful|brutal|miserable|crappy|horrific|horrendous|disgusting|lousy|horrifying|horrid|laughable|difficult|worthless|challenge|challenging|hassle|panic|hard|criminal\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24044, "Name": "negative_sentiment-escalate", "Description": "", "ActionString": "<escalat%><escalade>$OR$", "UserEntry": "(escalade|escalat*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24043, "Name": "negative_sentiment-hassles", "Description": "", "ActionString": "[nickel+and+dime:1.2][too+deep:1][big+picture:1][pain+ass|butt:1.2][background+noise:1][wait+too+long:1][not+big+fan:1][real+hard:1][deal+breaker:1][got%+be+careful:1][matter+of+fact:1][was|is+big+deal:1]<bitty><hardship><no-brainer><feedback><panic><hassle><challenge%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((challenge*|hassle|panic|feedback|no-brainer|hardship|bitty) OR (\"was|is big deal\":1) OR (\"matter of fact\":1) OR (\"got* be careful\":1) OR (\"deal breaker\":1) OR (\"real hard\":1) OR (\"not big fan\":1) OR (\"wait too long\":1) OR (\"background noise\":1) OR (\"pain ass|butt\":1.2)  OR (\"big picture\":1) OR (\"too deep\":1) OR (\"nickel and dime\":1.2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24042, "Name": "negative_sentiment-crime", "Description": "", "ActionString": "[penalties+including:1]<guilty><criminal><crime>$OR$$OR$$OR$", "UserEntry": "((crime|criminal|guilty) OR (\"penalties including\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24041, "Name": "negative_sentiment-dissatisfied", "Description": "", "ActionString": "[don`t|not|didn`t+appeciate:1]<discouraged><unacceptable><displeased><unhappy><unsatisf%><dissatisf%>$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((dissatisf*|unsatisf*|unhappy|displeased|unacceptable|discouraged) OR (\"don't|not|didn't appeciate\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24040, "Name": "negative_sentiment-outrageous", "Description": "", "ActionString": "[dangerous|aggressive|drastic|astronomical|political|economical:1.5][serious%|strong%|huge%|big|significant|unusual|high|uncommon%+risk|outrageous:1.5]$OR$", "UserEntry": "((\"serious*|strong*|huge*|big|significant|unusual|high|uncommon* risk|outrageous\":1.5) OR (\"dangerous|aggressive|drastic|astronomical|political|economical\":1.5))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24039, "Name": "negative_sentiment-slander", "Description": "", "ActionString": "[my+belief:1][life+threaten%:1][hurt|damage|tarnish+reputation:1.2]<emergency><inflation><violation><crisis><stipulation><suspect><civil><limitation><lawsuit>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((lawsuit|limitation|civil|suspect|stipulation|crisis|violation|inflation|emergency) OR (\"hurt|damage|tarnish reputation\":1.2) OR (\"life threaten*\":1) OR (\"my belief\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24038, "Name": "negative_sentiment-terrible", "Description": "", "ActionString": "<nightmare><laughable><horrid><horrifying><lousy><disgusting><horrendous><horrific><crappy><miserable><brutal><dreadful><atrocious><nightmares>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(nightmares|atrocious|dreadful|brutal|miserable|crappy|horrific|horrendous|disgusting|lousy|horrifying|horrid|laughable|nightmare)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24037, "Name": "negative_sentiment-cant stand", "Description": "", "ActionString": "[can`t|cannot|won’t|ain’t|not+remember|find|even|believe|walk|stand|imagine|bother:1.5]", "UserEntry": "(\"can't|cannot|won’t|ain’t|not remember|find|even|believe|walk|stand|imagine|bother\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24036, "Name": "negative_sentiment-difficult", "Description": "", "ActionString": "<worthless><frustration><difficult><stressful><struggle><frustrated><overwhelm><frustrating><difficult><ridiculous><confusing>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(confusing|ridiculous|difficult|frustrating|overwhelm|frustrated|struggle|stressful|difficult|frustration|worthless)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24035, "Name": "negative_sentiment-insistent", "Description": "", "ActionString": "<persistent><adamant><demand%><persisting><insist%>$OR$$OR$$OR$$OR$", "UserEntry": "(insist*|persisting|demand*|adamant|persistent)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24034, "Name": "negative_sentiment-pretty bad", "Description": "", "ActionString": "[pretty|fairly+tough|rough|close|pricey|strict:1.5]", "UserEntry": "(\"pretty|fairly tough|rough|close|pricey|tough|strict\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24033, "Name": "negative_sentiment-uncomplimentary", "Description": "", "ActionString": "[need%+clarification:1.2][not+careful:1]<deceptive><inconvenience><complicated><inaccurate>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((inaccurate|complicated|inconvenience|deceptive) OR (\"not careful\":1) OR (\"need* clarification\":1.2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24032, "Name": "negative_sentiment-fraudulent", "Description": "", "ActionString": "[who+knowingly:1.2]<defraud><misleading><false><fraud>$OR$$OR$$OR$$OR$", "UserEntry": "((fraud|false|misleading|defraud) OR (\"who knowingly\":1.2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -24031, "Name": "negative_sentiment-legal obligation", "Description": "", "ActionString": "[into+consideration:1][in+accordance|preparation|consideration:1.2][demand+satisfaction:1.2][your|their|there+legal+obligation:1.5][federal|state|local+laws|fund|consideration|association|generation|ration|compensation|denial|registration|revocation|entity|appliances:1.5]<prosecution><arbitration>$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((arbitration|prosecution) OR (\"federal|state|local laws|fund|consideration|association|generation|ration|compensation|denial|registration|revocation|entity|appliances\":1.5) OR (\"your|their|there legal obligation\":1.5) OR (\"demand satisfaction\":1.2) OR (\"in accordance|preparation|consideration\":1.2) OR (\"into consideration\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-05-04T11:02:15.55", "ComponentCount": 15}