{"BucketId": "154", "BucketFullname": "Categories.Quality Automation_Calls.Replacement Identifier", "BucketDescription": "to identify calls where replacement order is being created", "UserEditable": false, "BucketName": "Replacement Identifier", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-05-14T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Replacement Identifier", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2024-02-15T07:38:18.83-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-05-14T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-06-18T00:00:00", "EndDate": "2024-06-24T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -32250, "Name": "Create order", "Description": null, "ActionString": "[get+order+set|sent+out|up:1.8][get+order%+you+need:2][get+order+placed:2][already+placed+order:2.2]<order%>[i`m|am+about+to+place:2]$NEAR:5$[in+order+to:1.2]<mail><test>$OR$$OR$[make|making+order:1.6]$NOTNEAR:1.5$[gonna|going+put+order:2][make|making+sure+order:2.2]!-60%[create+new|another+shipment|order:2]!-60%[gonna+send+replace%:1.5]=1!-60%[plac%+order:1]!-60%[create+case+send+order:2]!-60%{replac%|damag%|brok%+place+order:1.5}!-60%[order+replace:1]!-60%[get+order+done|ready:1]!-60%<removed>!-60%[creat%|gener%+order|doctor:1]!-60%$NOTNEAR:7$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"creat*|gener* order|doctor\":1 NOT NEAR:7 removed OR \"get order done|ready\":1 OR \"order replace\":1 OR  [replac*|damag*|brok* place order]:1.5 OR \"create case send order\":2 OR \"plac* order\":1 OR \"gonna send replace*\":1.5=agent OR \"create new|another shipment|order\":2 OR \"make|making sure order\":2.2){-60%} OR \"gonna|going put order\":2 OR \"make|making order\":1.6 NOT NEAR:1.5 (test|mail OR \"in order to\":1.2) OR \"i'm|am about to place\":2 NEAR:5 order* OR \"already placed order\":2.2 OR \"get order placed\":2 OR \"get order* you need\":2 OR \"get order set|sent out|up\":1.8", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32250, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32250, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32233, "Name": "going to send/replacement", "Description": null, "ActionString": "<readings><need><female><absurd><letter><fix><start><check>$OR$$OR$$OR$$OR$$OR$$OR$$OR$[meter%|reader|reading|replacement|sensor+will|going|gonna|i`ll|we`ll|ahead|be+send%|sent|receiv%|arriv%:2.2]$NOTNEAR:2$[warehouse|ware+will|gonna|going+send%|dispatch%|ship%:2][can+send+one+more+time:2.4][express|fast%+priority|shipping|delivery:1.6][business|working+day%:1.2]{send%|receiv%|arriv%|deliver%+senor|order|meter%:5.6}[will+able+got+it:1.2][you+will+get:0.9][if+can`t|cannot|no|not:1.4][call+back:1]<callback><information>$OR$$OR$$OR$[going|gonna|will|we`ll|ahead|she`ll|be|you`ll+send|replac%|receiv%|arriv%|sent:2.4]$NOTNEAR:2$$OR$$OR$$OR$$NEAR:60$<second>[ahead|going|gonna|will|i`ll|we`ll+mail+it|device|machine|meter%:2]$NOTNEAR:1$[if+can`t|cannot|no|not:1.4]!-80%[ahead|gonna|going+replace+test%|vial|bario|barrio|verio|trip%|lance%|strip%|meter|device:3]!-80%$NOTNEAR:2$<information>!-80%<properly>!-80%<troubleshoot%>!-80%<anything>!-80%<unable>!-80%<cannot>!-80%<can`t>!-80%<chills>!-80%<if>!-80%<maybe>!-80%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[would|will|want|wanna|proceed|forward|ahead|i`ll|we`ll|going|gonna|be|now+send%+meter%|reader|reading:2.6]!-80%$NOTNEAR:2.5$$OR$[can+replac%|send|mail:2][will|gonna+replac%|send%:2][will+send+bills:2][get+replacement+for+you:1.2]!-60%[going|gonna+place|please+order:1.2]!-60%<if>!-60%[have|able+to+send+replacement:1.2]!-60%$NOTNEAR:1${will|would|be|going+send%|said+replacement|new|news|free:3}!-60%[check+will+send:2]!-60%[in+case+that|we:1.2]!-60%[if+not|no|isn`t|cannot:1.2]!-60%$OR$[going|gonna|need+to|go|proceed|process+replace%:1.5]!-60%$NOTNEAR:2$[i%|will|can|able+resend:1.5]!-60%[going|gonna+send|proceed+new|peace|replace%+shipment|order:2]!-60%[let+me+send+again:2]!-60%[send+new+one|machine|meter:1.5]!-60%[send|sing|sense|seeing|senior+you+machine%|device%|meter%|replace%:1.5]!-60%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[going|gonna|will|would|have|able+send%|sent|replac%|receiv%+again|new|that|broken|damag%+meet|meter|device:1.2]!-180<if>!-180<share>!-180$OR${going|gonna|will|would|need|about+replac%+send|sent:1.5}!-180$NOTNEAR:2.2$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "([going|gonna|will|would|need|about replac* send|sent]:1.5 NOT NEAR:2.2 (share|if) \nOR \"going|gonna|will|would|have|able send*|sent|replac*|receiv* again|new|that|broken|damag* meet|meter|device\":1.2){-180} \nOR (\"send|sing|sense|seeing|senior you machine*|device*|meter*|replace*\":1.5 \nOR \"send new one|machine|meter\":1.5 \nOR \"let me send again\":2 \nOR \"going|gonna send|proceed new|peace|replace* shipment|order\":2 \nOR \"i*|will|can|able resend\":1.5 \nOR \"going|gonna|need to|go|proceed|process replace*\":1.5 NOT NEAR:2 (\"if not|no|isn't|cannot\":1.2 OR \"in case that|we\":1.2) \nOR \"check will send\":2 \nOR ([will|would|be|going send*|said replacement|new|news|free]:3) \nOR \"have|able to send replacement\":1.2 NOT NEAR:1 if  \nOR \"going|gonna place|please order\":1.2 \nOR \"get replacement for you\":1.2){-60%} \nOR \"will send bills\":2 \nOR \"will|gonna replac*|send*\":2\nOR \"can replac*|send|mail\":2\nOR (\"would|will|want|wanna|proceed|forward|ahead|i'll|we'll|going|gonna|be|now send* meter*|reader|reading\":2.6 NOT NEAR:2.5 (maybe|if|chills|can't|cannot|unable|anything|troubleshoot*|properly|information) \nOR \"ahead|gonna|going replace test*|vial|bario|barrio|verio|trip*|lance*|strip*|meter|device\":3 NOT NEAR:2 \"if can't|cannot|no|not\":1.4){-80%} \nOR \"ahead|going|gonna|will|i'll|we'll mail it|device|machine|meter*\":2 NOT NEAR:1 second \nOR (\"going|gonna|will|we'll|ahead|she'll|be|you'll send|replac*|receiv*|arriv*|sent\":2.4 NOT NEAR:2 (information|callback \nOR \"call back\":1 OR \"if can't|cannot|no|not\":1.4) \nOR \"you will get\":0.9 \nOR \"will able got it\":1.2 \nOR [send*|receiv*|arriv*|deliver* senor|order|meter*]:5.6) NEAR:60 \"business|working day*\":1.2 \nOR \"express|fast* priority|shipping|delivery\":1.6 \nOR \"can send one more time\":2.4 \nOR \"warehouse|ware will|gonna|going send*|dispatch*|ship*\":2 \nOR \"meter*|reader|reading|replacement|sensor will|going|gonna|i'll|we'll|ahead|be send*|sent|receiv*|arriv*\":2.2 NOT NEAR:2 (check|start|fix|letter|absurd|female|need|readings)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32233, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32233, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-08-12T08:05:50.57", "ComponentCount": 2}