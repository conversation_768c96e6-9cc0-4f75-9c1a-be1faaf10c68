class A:
    def __init__(self, attr1=None, attr2=None):
        self.attr1 = attr1
        self.attr2 = attr2


class B:
    def __init__(self, attr1=None, attr3=None):
        self.attr1 = attr1
        self.attr3 = attr3


def copy_matching_attributes(instance_a, instance_b):
    # Get the attributes of both instances as dictionaries
    attrs_a = vars(instance_a)
    attrs_b = vars(instance_b)

    # Loop through attributes of A and copy from B if they match
    for key in attrs_a.keys():
        if key in attrs_b:
            attrs_a[key] = attrs_b[key]

        # Example usage


a = A(attr1='value A1', attr2='value A2')
b = B(attr1='value B1', attr3='value B3')

print(f"Before copying: A.attr1 = {a.attr1}, A.attr2 = {a.attr2}")

# Copy attributes from B to A
copy_matching_attributes(a, b)

print(f"After copying: A.attr1 = {a.attr1}, A.attr2 = {a.attr2}")