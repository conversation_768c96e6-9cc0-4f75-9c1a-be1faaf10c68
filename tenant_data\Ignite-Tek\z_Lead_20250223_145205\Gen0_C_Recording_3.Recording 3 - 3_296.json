{"BucketId": "296", "BucketFullname": "Categories.C_Recording_3.Recording 3 - 3", "BucketDescription": "there is no obligation to enroll your current or future medicare enrollment status will not be impacted and automatic enrollment will not occur", "UserEditable": false, "BucketName": "Recording 3 - 3", "SectionName": "C_Recording_3", "SectionNameDisplay": "C_Recording_3", "SectionId": 92, "RetroStartDate": "2024-10-21T00:00:00", "CategoryDisplay": "C_Recording_3.Recording 3 - 3", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-28T17:03:21.083-04:00", "Creator": "<EMAIL>", "Version": 2, "LanguageTag": "", "EffectiveDate": "2024-10-21T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31084, "Name": "_SCRIPT", "Description": null, "ActionString": "[there+is+no+obligation+to+enroll+your+current+or+future+medicare+enrollment+status+will+not+be+impacted+and+automatic+enrollment+will+not+occur:6.8]", "UserEntry": "\"there is no obligation to enroll your current or future medicare enrollment status will not be impacted and automatic enrollment will not occur\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31024, "Name": "Recording 3 - 3", "Description": null, "ActionString": "[obligation|enroll+current|future+medicare|enrollment|status+impacted|automatic+enrollment|occur:10]", "UserEntry": "\"obligation|enroll current|future medicare|enrollment|status impacted|automatic enrollment|occur\":10", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-11-04T13:44:03.137", "ComponentCount": 2}