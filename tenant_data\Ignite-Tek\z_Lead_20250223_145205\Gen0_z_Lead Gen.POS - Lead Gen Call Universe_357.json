{"BucketId": "357", "BucketFullname": "Categories.z_Lead Gen.POS - Lead Gen Call Universe", "BucketDescription": "", "UserEditable": false, "BucketName": "POS - Lead Gen Call Universe", "SectionName": "z_Lead Gen", "SectionNameDisplay": "z_Lead Gen", "SectionId": 105, "RetroStartDate": "2024-10-14T00:00:00", "CategoryDisplay": "z_Lead Gen.POS - Lead Gen Call Universe", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-11-11T14:06:00.073-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-14T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30624, "Name": "Fronter Closer Combine", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -30624, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Subject", "TableFilterTypes": null, "FriendlyName": "<PERSON><PERSON><PERSON>", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "FRONTER_CLOSER_COMBINE", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30624, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "APTCON", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30624, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "APTSET", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30624, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "INTSET", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30623, "Name": "Meet Specialist", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -30623, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Subject", "TableFilterTypes": null, "FriendlyName": "<PERSON><PERSON><PERSON>", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "MEET_SPECIALIST", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30623, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "APTCON", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30623, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "APTSET", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30623, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "INTSET", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30622, "Name": "Phone Quiz / CTC", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -30622, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Subject", "TableFilterTypes": null, "FriendlyName": "<PERSON><PERSON><PERSON>", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "PhoneQuiz", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30622, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Subject", "TableFilterTypes": null, "FriendlyName": "<PERSON><PERSON><PERSON>", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "PhoneQuizCTC", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30622, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "XFER", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -30622, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Disposition_01", "TableFilterTypes": null, "FriendlyName": "DispoStatus", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "WXFER", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30621, "Name": "XFER Closer", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-05T12:40:24.65", "ComponentCount": 4}