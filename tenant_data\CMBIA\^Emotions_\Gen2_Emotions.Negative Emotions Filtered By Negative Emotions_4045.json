{"BucketId": "4045", "BucketFullname": "Categories.Emotions.Negative Emotions Filtered By Negative Emotions", "BucketDescription": "test of dependencies for <PERSON><PERSON><PERSON>", "UserEditable": false, "BucketName": "Negative Emotions Filtered By Negative Emotions", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Negative Emotions Filtered By Negative Emotions", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-01-29T11:28:47.81-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-01-29T11:28:48.68", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -23016, "Name": "Neg Emotion", "Description": null, "ActionString": "(emotions.negative emotion)", "UserEntry": "CAT:[Emotions.Negative Emotion] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -23016, "BucketID": 3981, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-01-29T11:28:48.68", "ComponentCount": 1}