{"BucketId": "205", "BucketFullname": "Categories.C_Disclaimer.PBI_Disclaimer_6", "BucketDescription": "information on all of your options", "UserEditable": false, "BucketName": "PBI_Disclaimer_6", "SectionName": "C_Disclaimer", "SectionNameDisplay": "C_Disclaimer", "SectionId": 72, "RetroStartDate": "2024-10-07T00:00:00", "CategoryDisplay": "C_Disclaimer.PBI_Disclaimer_6", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-18T12:34:51.897-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-10-14T00:00:00", "EndDate": "2024-10-14T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -31275, "Name": "Disclaimer7.2", "Description": null, "ActionString": "<option%>{info%:0.2}$BEFORE:3$", "UserEntry": "[info*] BEFORE:3 option*", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31039, "Name": "v2", "Description": null, "ActionString": "<insurance><medicare>$OR$[all+your+info%:0.8][info%+option%:3]{information+options:0.5}$OR$$OR$$AFTER:5$", "UserEntry": "([information options] OR (\"info* option*\":3) OR (\"all your info*\")) AFTER:5 (medicare OR insurance)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31038, "Name": "v3", "Description": null, "ActionString": "<option%><info%>$OR$[to+get:1.5]$BEFORE:1.5$", "UserEntry": "\"to get\":1.5 BEFORE:1.5 (info* OR option*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-06T12:53:17.5", "ComponentCount": 3}