{"BucketId": null, "BucketFullname": "", "BucketDescription": "To identify calls that should not be scored for quality as no meaningful conversation or contact takes place on them, such as Ghost Calls, Voicemail, IVR, short calls with almost no words, etc.", "UserEditable": false, "BucketName": "Exclusions", "SectionName": "", "SectionNameDisplay": "", "SectionId": 58, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 3.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T17:50:56", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T17:50:56", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Audio Conferencing Centre", "Description": null, "ActionString": "<month%><address><got>$OR$$OR$[conference+id:1.5]$NOTNEAR:5$[please+enter+conference:2][audio+conferenc%+cent%:2]$OR$$OR$", "UserEntry": "\"audio conferenc* cent*\":2 \nOR \"please enter conference\":2 \nOR \"conference id\":1.5 NOT NEAR:5 (got|address|month*)", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "IVR", "Description": null, "ActionString": "[call+may+be+monitor%|recorded:3][to+hear+options+again:2][speak|reach+agent|representative+press:3][vpn+agent+number:2.5]{listen+carefully+available+options:3}[enter+agent|origin+number:2]<password>[please+enter+the+vdn|vpn|vdm:2]$NOTNEAR:3$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"please enter the vdn|vpn|vdm\":2 NOT NEAR:3 (password)\nOR \"enter agent|origin number\":2 \nOR [listen carefully available options]:3 \nOR \"vpn agent number\":2.5 OR \"speak|reach agent|representative press\":3 OR \"to hear options again\":2 OR \"call may be monitor*|recorded\":3", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Short Outgoing Calls", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Less Than 100 Words", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Ghost Calls", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T17:50:56", "ComponentCount": 5}