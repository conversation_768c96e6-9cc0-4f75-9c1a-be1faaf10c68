{"BucketId": "8", "BucketFullname": "Categories.Customer Experience.Speaker Separated Calls", "BucketDescription": "Removes Mono Calls", "UserEditable": false, "BucketName": "Speaker Separated Calls", "SectionName": "Customer Experience", "SectionNameDisplay": "Customer Experience", "SectionId": 34, "RetroStartDate": null, "CategoryDisplay": "Customer Experience.Speaker Separated Calls", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2023-09-15T07:23:33.213-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-09-15T07:23:33.43", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32703, "Name": "Speaker Separated Calls", "Description": null, "ActionString": "{%:0.2}=0$NOT$", "UserEntry": "-([*]=Mono)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-09-15T07:23:33.43", "ComponentCount": 1}