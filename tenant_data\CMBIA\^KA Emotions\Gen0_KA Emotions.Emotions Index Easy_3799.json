{"BucketId": "3799", "BucketFullname": "Categories.KA Emotions.Emotions Index Easy", "BucketDescription": "Words and phrases that indicates low effort on the part of the customer", "UserEditable": false, "BucketName": "Emotions Index Easy", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotions Index Easy", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:37:15.05-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-02-28T09:25:11.613", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25446, "Name": "Easy", "Description": null, "ActionString": "[can`t|cannot+believe+how+easy|simple:2.5][thank+you:0.8]<thanks>$OR$[really|pretty|very|real|so+simple|easy:1]$NEAR:5$[easier|simpler+than+thought|expecting|expected|thinking:1.8]$OR$$OR$", "UserEntry": "\"easier|simpler than thought|expecting|expected|thinking\":1.8 OR (\"really|pretty|very|real|so simple|easy\":1 NEAR:5 (thanks OR \"thank you\":.8)) OR \"can't|cannot believe how easy|simple\":2.5", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25445, "Name": "Not as difficult", "Description": null, "ActionString": "[thought+would+harder|difficult:1.8][not|wasn`t|isn`t+as+difficult|hard:1]$OR$", "UserEntry": "\"not|wasn't|isn't as difficult|hard\":1 OR \"thought would harder|difficult\":1.8", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25444, "Name": "Painless", "Description": null, "ActionString": "<effortless><painless>$OR$", "UserEntry": "painless|effortless", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25443, "Name": "Quick and easy", "Description": null, "ActionString": "{easy|easily+quick|quickly:1}", "UserEntry": "[easy|easily quick|quickly]:1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-02-28T09:25:11.613", "ComponentCount": 4}