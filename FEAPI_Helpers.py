import os,sys,time
import re
import requests
import getpass
import json
from openpyxl import load_workbook
import pandas as pd
from requests.auth import AuthBase


class CMAuth(AuthBase):
    """Attaches HTTP CM Authorization to the given Request object."""

    def __init__(self, token):
        self.token = token

    def __call__(self, r):
        r.headers['Authorization'] = 'JWT ' + self.token
        return r
    
class FEAPI_Helper:

    token = None

    def __init__(self, name,logger, config):
        self.name = name
        self.logger = logger
        self.config = config
        self.API = config.API
        self.Tenant = config.Tenant
        self.headers = {'content-type': 'application/json'}
        self.token = ''

       
        

    def _get_token_API10(self):  # etc. See APIRequest.cs in AutoCorrelate
        '''
        Obtain a token using the security API with a username and password
        :return: JWT token
        '''

        # Get username and password from environment variables if config values are blank or not set
        username = self.config.username if hasattr(self.config, 'username') and self.config.username else os.getenv('FEAPI_username')
        password = self.config.password if hasattr(self.config, 'password') and self.config.password else os.getenv('FEAPI_pwd')

        # Set default SecurityAPI if not configured
        security_api = self.config.security_api if hasattr(self.config, 'security_api') and self.config.security_api else 'https://sapi.callminer.net/'

        if not username or not password:
            self.logger.error("Username or password not found in config or environment variables (FEAPI_username, FEAPI_pwd)")
            return False

        token_uri = 'security/getToken'
        success = False
        payload = {'apikey': self.config.Tenant, 'username': username, 'password': password}
        params = {'authtypeid': '32'}
        r = requests.post(security_api + token_uri, data=payload, params=params)
        if r.status_code == 200:
            self.token = r.text.strip('"')
            success = True
        else:
            self.logger.error(f"Failed to get token. Status code: {r.status_code}. Response: {r.text}")
            exit(1)
        return success

    def UpdateTokenFromResponse(self, api_response):  
        """  
        Updates the token attribute from the API response if a new token is provided.  
    
        This method attempts to extract the 'auth-token-updated' header from the given  
        API response and updates the instance's token if it differs from the current token.  
    
        Parameters:  
        - api_response: The response object from an API call. It is expected to have a  
        'headers' attribute which is a dictionary containing HTTP headers.  
    
        Error Handling:  
        - If 'api_response' is None or doesn't have a 'headers' attribute, an AttributeError  
        is caught, and a message is printed indicating the issue.  
        - If the 'auth-token-updated' header is not present in the 'headers', a KeyError  
        is caught, and a message is printed indicating that the header is missing.  
        """  
        try:  
            # Attempt to retrieve the updated token from the headers  
            newtoken = api_response.headers['auth-token-updated']  
            
            # Check if the new token is different from the current token  
            if newtoken != self.token:  
                self.token = newtoken  
                
        except AttributeError:  
            # Handle the case where api_response is None or doesn't have headers  
            print("Invalid API response: 'api_response' is None or missing 'headers'")  
            
        except KeyError:  
            # Handle the case where the 'auth-token-updated' header is missing  
            print("Invalid API response: 'auth-token-updated' header is missing")  



    #  MISC FUNCTIONS ------------------------------------------------------------------------------------------
    @staticmethod
    def show_all_attributes_and_values(obj):
        """
        Shows all attributes of an object (class or instance) and their values.

        Args:
            obj: The object (class or instance).
        """
        lines=[]
        for attr_name in dir(obj):
            if not attr_name.startswith("__") : # Exclude special attributes
                try:
                    attr_value = getattr(obj, attr_name)
                    if type(attr_value) is bool:
                        lines.append(f"{attr_name}={attr_value}")
                    elif not attr_value.startswith("<function"):
                        lines.append(f"{attr_name}={attr_value}")
                except AttributeError:
                    donothing=0 #print(f"{attr_name}: (AttributeError - likely a method)")
        return lines

    @staticmethod
    def UpdateObjectA_WithB(a,b, ShowUpdateNote:bool=False):

        # Get the attributes of both instances as dictionaries
        attrs_a = vars(a)
        attrs_b = vars(b)

        # Loop through attributes of A and copy from B if they match
        for akey in attrs_a.keys():
            if not akey.startswith("__"):  # Exclude special attributes
                try:
                    attr_value = getattr(a,akey)
                    if akey in attrs_b:
                        if ShowUpdateNote:
                                print(f"Updating {akey}={attrs_b[akey]}")
                        setattr(a, akey, attrs_b[akey])
                except AttributeError:
                    donothing=0 #print(f"{attr_name}: (AttributeError - likely a method)")
        return a

    @staticmethod
    def get_windows_user_id():
        #Gets the current Windows user ID (username).
        try:
            return getpass.getuser()
        except Exception as e:
            print(f"Error getting username: {e}")
            return None

    @staticmethod
    def ask_to_continue(prompt:str="Do you want to continue? (y/n): "):
        sys.stdout.flush()
        time.sleep(2)
        while True:
            response = input(prompt).strip().lower()
            if response in ['yes', 'y']:
                print("Continuing...")
                return True
            elif response in ['no', 'n']:
                print("Exiting...")
                return False
            else:
                print("Invalid input. Please enter 'y' or 'n'.")

    @staticmethod
    def MatchesAnyRegex(regexlist, lookfor):
        # Split the regexlist string on commas to get individual regex patterns
        regex_patterns = regexlist.split(',')

        # Compile each pattern into a regex object
        regex_objects = [re.compile(pattern.strip()) for pattern in regex_patterns]

        # Check if lookfor matches any of the regex objects
        for regex in regex_objects:
            if regex.search(lookfor):
                return True

                # Return False if no match is found
        return False

    def cleanfilename(self,dirtyfilename: str, replacement:str="_"):
        """
        Cleans a string to be a valid Windows file path by replacing characters
        not allowed in file or folder names with a specified replacement character.

        Args:
            path_string: The string to clean.
            replacement: The character to replace invalid characters with.  Defaults to "_".

        Returns:
            The cleaned string.  Returns an empty string if the input is None or empty.
        """

        if not dirtyfilename:  # Check for None or empty string
            return ""

        invalid_chars = r'[<>:"/\\|?*]'  # Regular expression for invalid characters
        cleaned_string = re.sub(invalid_chars, replacement, dirtyfilename)

        # Check for reserved names (case-insensitive) and append an underscore if found.
        reserved_names = ["CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
                          "LPT1", "LPT2", "LPT3", "LPT4"]

        if cleaned_string.upper() in reserved_names:
            cleaned_string += "_"  # Append underscore to avoid conflicts

        # Check if the name is just a space or period
        if cleaned_string.strip() == "." or cleaned_string.strip() == "":
            cleaned_string = replacement  # Replace with replacement char

        return cleaned_string

    def load_json_from_file(self, filename):
        try:
            with open(filename, 'r') as file:
                data = json.load(file)
                return data
        except FileNotFoundError:
            self.logger.error(f"Error: The file '{filename}' was not found.")
        except json.JSONDecodeError:
            self.logger.error(f"Error: The file '{filename}' is not a valid JSON file.")
        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {e}")

    #  LOGGER FUNCTIONS ------------------------------------------------------------------------------------------

    def show_error_file_contents(self, handler, handlername):
        logger = self.logger
        filename = handler.baseFilename
        if os.path.exists(filename):
            try:
                with open(filename, "r") as f:
                    contents = f.read()
                    logger.info(
                        "---------------------------------------------------------------------------------------")
                    if contents == "":
                        logger.info(f"No {handlername} ENCOUNTERED")
                    else:
                        logger.info(f"{handlername} ENCOUNTERED - Saved to {handler.baseFilename}")
                        logger.info(
                            ".......................................................................................")
                        logger.info(contents)
                        logger.info(
                            "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^")

            except FileNotFoundError:
                print(f"{handlername} File not found: {filename}")
            except Exception as e:
                logger.error(f"An error occurred while reading the {handlername} file: {e}")
        else:
            logger.error(f"{handler} File {filename} does not exist.")

    #  EXCEL FUNCTIONS ------------------------------------------------------------------------------------------


    def move_last_sheet_to_front(self,file_path):
        # Load the Excel workbook
        workbook = load_workbook(file_path)

        # Get the names of all sheets in the workbook
        sheet_names = workbook.sheetnames

        # Get the last sheet and its index
        last_sheet = workbook[sheet_names[-1]]
        last_sheet_index = workbook.sheetnames.index(sheet_names[-1])

        # Move the last sheet to the front
        workbook.move_sheet(last_sheet, offset=-last_sheet_index)

        # Save the modified workbook
        workbook.save(file_path)


    def GetNextSheetName(self, xls_filename:str, sheet_name):
        # Load the Excel file
        excel_file = load_workbook(xls_filename)

        # Get the list of sheet names
        sheet_names = excel_file.sheetnames
        i=0
        while sheet_name in sheet_names:
            sheet_name = f"{sheet_name[:28]}{i}"
            i+=1
            if i>=100:
                self.logger.error(f"Unable to get unique sheetname for Category ({sheet_name})")
                exit(1)

        return sheet_name


    def AppenddataframeAsSheetToExcel(self,xls_filename:str, sheet_name, df):
        try:
            sheet_name = sheet_name[:31]
            #Make sure sheet doesn't already exist
            sheet_name = self.GetNextSheetName(xls_filename,sheet_name)

            # Try to open the Excel file in append mode
            with pd.ExcelWriter(xls_filename, mode='a', engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                self.logger.info(
                    f"Appended DataFrame as sheet '{sheet_name}' to existing Excel file '{xls_filename}'.")
        except FileNotFoundError:
            # If the Excel file does not exist, create a new file and write the DataFrame as the first sheet
            df.to_excel(xls_filename, sheet_name=sheet_name, index=False)
            self.logger.info(f"Created Excel file '{xls_filename}' with DataFrame as sheet '{sheet_name}'.")

    import pandas as pd

    def AppendDataframeToExistingSheet(self, xls_filename: str, sheet_name: str, df: pd.DataFrame):
        try:
            # Ensure the sheet name does not exceed 31 characters
            sheet_name = sheet_name[:31]

            # Load the existing Excel file
            with pd.ExcelFile(xls_filename, engine='openpyxl') as xls:
                # Check if the sheet exists
                if sheet_name in xls.sheet_names:
                    # Read existing data from the sheet
                    existing_df = pd.read_excel(xls, sheet_name=sheet_name)
                    # Concatenate the existing data with the new DataFrame
                    combined_df = pd.concat([existing_df, df], ignore_index=True)
                else:
                    # If the sheet doesn't exist, use the new DataFrame as the combined DataFrame
                    combined_df = df

                    # Write the combined DataFrame back to the same sheet
            with pd.ExcelWriter(xls_filename, mode='a', engine='openpyxl', if_sheet_exists='replace') as writer:
                combined_df.to_excel(writer, sheet_name=sheet_name, index=False)
                self.logger.info(f"Appended DataFrame to existing sheet '{sheet_name}' in Excel file '{xls_filename}'.")

        except FileNotFoundError:
            # If the Excel file does not exist, create a new file and write the DataFrame as the first sheet
            df.to_excel(xls_filename, sheet_name=sheet_name, index=False)
            self.logger.info(f"Created Excel file '{xls_filename}' with DataFrame as sheet '{sheet_name}'.")



    def append_filterrow_to_excel(self,bucket_fullname:str, search_component_name, filter_string, filename='filters.xlsx', filterstringcolumnname = "FilterString"):
        """Appends a row to an Excel file with specified columns."""
        # Define the column names
        columns = ['BucketFullname', 'SearchComponentName', filterstringcolumnname]

        # Create a DataFrame with the new data
        new_data = pd.DataFrame([[bucket_fullname, search_component_name, filter_string]], columns=columns)

        # Check if the file exists and read the existing data
        if os.path.exists(filename):
            existing_data = pd.read_excel(filename)
            # Concatenate the existing data with the new data
            updated_data = pd.concat([existing_data, new_data], ignore_index=True)
        else:
            # If the file does not exist, use the new data as the DataFrame
            updated_data = new_data

            # Write the updated DataFrame back to the Excel file, including headers
        updated_data.to_excel(filename, index=False, header=True)

    def LogFilter(self, bucketfullname:str, searchcomponent, filterstring, xls_filename:str):
        # add filter to output
        self.append_filterrow_to_excel(bucketfullname, searchcomponent, filterstring, xls_filename)

    def LogEQLDependency(self,bucketfullname:str,searchcomponent,depliststring,xlsfilename:str):
        # add filter to output
        self.append_filterrow_to_excel(bucketfullname,searchcomponent,depliststring, xlsfilename, "DependsOn")


    # CATEGORY and FILTER FUNCTIONS ----------------------------------------------------------------------------------------------

    def FilenameInOutputFolder(self, json_output_dir, filename):
        return os.path.join(json_output_dir, filename)


    def GetFilterDependencyTables(self, outputfolder, ignore_alerts:bool=False):
        self.df_categories = self.GetAllCategories()
        self.df_sections = self.API_GetAllSections()
        self.df_folders = self.API_GetAllFolders()
        allscoresjson, self.df_scores = self.API_GetAllScores()

        self.AppenddataframeAsSheetToExcel(self.FilenameInOutputFolder(outputfolder,"ALLCategories.xlsx"), "ALL Categories", self.df_categories)
        self.AppenddataframeAsSheetToExcel(self.FilenameInOutputFolder(outputfolder,"ALLsections.xlsx"), "ALL Sections", self.df_sections)
        self.AppenddataframeAsSheetToExcel(self.FilenameInOutputFolder(outputfolder,"ALLfolders.xlsx"), "ALL Folders", self.df_folders)
        self.AppenddataframeAsSheetToExcel(self.FilenameInOutputFolder(outputfolder,"ALLscores.xlsx"), "ALL Scores",self.df_scores)

        if not ignore_alerts:
            self.df_alerts = self.API_GetAllAlerts()
            self.AppenddataframeAsSheetToExcel(self.FilenameInOutputFolder(outputfolder,"ALLalerts.xlsx"), "ALL Alerts", self.df_alerts)


    def GetCategoryFullname(self,BucketID:int,df_categories):
        #Get BucketFullname from df
        try:
            result = df_categories.loc[df_categories['BucketId'] == BucketID, 'BucketFullname'].iloc[0]
            return result
        except IndexError:  # No matching row found.
            return f"BucketId_NOT_FOUND"
        except KeyError as e:  # bucketID or BucketFullname column doesn't exist.
            return f"BucketId or BucketFullname Column missing {e}"

    # def GetFolderName(self, ID:int, df):
    #     #Get BFolderName from df
    #     try:
    #         result = df.loc[df['FolderID'] == ID, 'FolderName'].iloc[0]
    #         return result
    #     except IndexError:  # No matching row found.
    #         return f"FolderID_NOT_FOUND"
    #     except KeyError as e:  # FolderID or Foldername column doesn't exist.
    #         return f"FolderID or FolderName Column missing {e}"

    def GetAlertName(self,ID:int,df):
        #Get AlertName from df
        try:
            result = df.loc[df['AlertID'] == ID, 'Name'].iloc[0]
            return result
        except IndexError:  # No matching row found.
            return f"ID_NOT_FOUND"
        except KeyError as e:
            return f"ID or Name Column missing {e}"

    def GetSectionName(self,ID:int,df):
        #Get Name for this ID from df
        try:
            result = df.loc[df['SectionID'] == ID, 'SectionName'].iloc[0]
            return result
        except IndexError:  # No matching row found.
            return f"ID_NOT_FOUND"
        except KeyError as e:
            return f"ID or Name Column missing {e}"

    def GetScoreName(self,ID:int,df):
        #Get Name for this ID from df
        try:
            result = df.loc[df['Id'] == ID, 'Name'].iloc[0]
            return result
        except IndexError:  # No matching row found.
            return f"ID_NOT_FOUND"
        except KeyError as e:
            return f"ID or Name Column missing {e}"

    def GetBucketID(self,BucketFullname:str,df_categories):
        #Return BucketID for this Fullname
        try:
            result = df_categories.loc[df_categories['BucketFullname'] == BucketFullname, 'BucketId']
            if not result.empty:
                result = int(result.iloc[0])
                return result
            self.logger.warning(f"BucketId_NOT_FOUND for BucketFullname={BucketFullname}")
            return None
        except Exception as e:  # No matching row found.
            self.logger.warning(f"BucketId_NOT_FOUND for BucketFullname={BucketFullname}\tException = {e}")
            return None

    def GetNewBucketIdAndName(self, oldbucketid, df_oldbuckets, df_newbuckets):
        # Step 1: Look up BucketFullname in df_oldbuckets
        old_bucket_fullname = df_oldbuckets.loc[df_oldbuckets['BucketId'] == oldbucketid, 'BucketFullname']

        if old_bucket_fullname.empty:
            return None, f"Old BucketId {oldbucketid} not found in df_oldbuckets."

        # Extract the BucketFullname
        old_bucket_fullname_value = old_bucket_fullname.values[0]

        # Step 2: Look up BucketId in df_newbuckets using the found BucketFullname
        new_bucket_id = df_newbuckets.loc[df_newbuckets['BucketFullname'] == old_bucket_fullname_value, 'BucketId']

        if new_bucket_id.empty:
            return None,old_bucket_fullname_value
            #raise ValueError(f"BucketFullname '{old_bucket_fullname_value}' not found in df_newbuckets.")

            # Return the first matching new BucketId
        return int(new_bucket_id.values[0]), old_bucket_fullname_value

    #old legacy interface for ExportCategory
    def ParseFilters(self, filtersjson, df_categories):
        result,deplist = self.ParseFilterJson(filtersjson)
        return result, deplist["bucket"]

    def ParseFilterJson(self, filtersjson):
        # Extracting and formatting the output
        formatted_output = []
        dep_list = {
            'bucket': [],
            'alert': [],
            'section': [],
            'score': []
        }

        for f in filtersjson:
            column_name = f['ColumnName']
            operator = f['Operator']
            operand1 = f['Operand1']
            operand2 = f['Operand2']
            t = f"{column_name} {operator} '{operand1}'"
            if operand2 != "":
                t = t + f" and {operand2}"

            if column_name is None:
                sectionid = f['SectionID']
                scoreid = f['ScoreID']
                alertid = f['AlertID']
                bucketid = f['BucketID']

                if bucketid is not None:
                    bucketfullname = self.GetCategoryFullname(bucketid, self.df_categories)
                    t = f"Filtered by Bucketid({f['BucketID']} = {bucketfullname})"
                    dep_list['bucket'].append(bucketid)
                elif sectionid is not None:
                    fullname = self.GetSectionName(sectionid, self.df_sections)
                    t = f"Filtered by SectionID({sectionid} = {fullname})"
                    dep_list['section'].append(sectionid)
                elif scoreid is not None:
                    fullname = self.GetScoreName(scoreid, self.df_scores)
                    t = f"Filtered by ScoreID({scoreid} = {fullname})"
                    dep_list['score'].append(scoreid)
                elif alertid is not None:
                    fullname = self.GetAlertName(alertid, self.df_scores)
                    t = f"Filtered by AlertID({alertid} = {fullname})"
                    dep_list['alert'].append(alertid)

            formatted_output.append(t)

            # Joining the output into a single string
        result = ' AND '.join(formatted_output)
        return result, dep_list

    def ParseAlertSearchComponentFilters(self, scJSON):
        # Extracting and formatting the output
        formatted_output = []
        deplist = {}

        for sc in scJSON:
            filterjson = sc["Filters"]
            filterstring, filter_deplist = self.ParseFilterJson(filterjson)

            # Merging the dictionaries by extending lists
            for key in filter_deplist:
                if key in deplist:
                    deplist[key].extend(filter_deplist[key])
                else:
                    deplist[key] = filter_deplist[key]

            formatted_output.append(filterstring)

            #Get EQL dependencies
            eql_depstring, eql_deplist = self.ParseEQL(sc['UserEntry'],self.df_categories)
            formatted_output.append(eql_depstring)
            deplist["bucket"].extend(eql_deplist)



            # Joining the output into a single string
        result = ' AND '.join(formatted_output)
        return result, deplist





    def ParseEQL(self, text, df_categories):
        # Get Dependencies from EQL USerEntry
        if text ==None or text =="":
            return "",[]

        #Look for CAT language
        pattern = r"CAT:\[[^\]]+\]"  # Raw string for regex
        try:
            matches = re.findall(pattern, text)
        except Exception as e:
            self.logger.error(f"Unable to search text({text}) for Categories. Exception = {e}")
            return "",[]

        #Otherwise continue
        bucketidlist = []

        for m in matches:
            #Get BucketID
            bucketid = self.GetBucketID(m[5:-1], df_categories)
            if bucketid is not None:
                bucketidlist.append(bucketid)
            else:
                self.logger.warning(f"Unable to find the BucketID for Match({m}) in UserEntry({text})")

        return ",".join(matches), bucketidlist


    def ParseSearchComponent(self, scJSON, df_categories):
        # Extracting and formatting the output
        formatted_output = []
        deplist = []

        for sc in scJSON:
            bucketid = sc['BucketID']
            if bucketid is not None:
                deplist.append(bucketid)
                bucketfullname = self.GetCategoryFullname(bucketid,df_categories)
                formatted_output += f"Category({bucketfullname})"
            else:
                columnname = sc['ColumnName']
                formatted_output += f"Column({columnname})"

            # Joining the output into a single string
        result = ' AND '.join(formatted_output)
        return result,deplist



    # API FUNCTIONS ---------------------------------------------------------------------------------------------

    # Get token from file
    def GetTokenFromFile(self):
        try:
            with open("token.txt", "r") as file:
                self.token = file.read().strip()
        except FileNotFoundError:
            self.logger.error("Error: 'token.txt' file not found.")
            exit(1)

        return self.token

    # Update the token file with a new token
    def write_token_to_file(self, newtoken:str):
        if newtoken != self.token:
            self.token = newtoken
            try:
                with open("token.txt", "w") as file:
                    file.write(self.token)
                    #print("Token written to token.txt successfully.")
            except Exception as e:
                self.logger.error(f"Error writing token to file: {e}")

    # retrieve json from API or handle error
    def ApiGetJson(self,url: str, exitonerror:bool=True, defaultresponseonerror=None):
        responsejson = None

        # obtain token
        if self.token != '':
            token_obtained = True
        else:
            token_obtained = self._get_token_API10()

        self.logger.info('Token obtained? {0}'.format(token_obtained))
        #print('Token obtained? {0}'.format(token_obtained))


        if token_obtained:
            my_auth = CMAuth(self.token)

            api_response = requests.get(url, headers=self.headers, auth=my_auth) #headers={'Authorization': f'JWT {self.token}'})
            status_code = api_response.status_code
            if status_code == 200:
                self.UpdateTokenFromResponse(api_response)
                responsejson = api_response.json()
            else:
                self.logger.error(f"ApiGet FAILED with statuscode({status_code}) = {api_response.content})")
                if exitonerror:
                        exit(1)
                else:
                    return defaultresponseonerror
        else:
            self.logger.error(f"ApiGet FAILED with statuscode({status_code}) = {api_response.content})")
            exit(1)
        
        return responsejson


    # retrieve json from API or handle error
    def ApiPostJson(self,url: str, payload, exitonerror:bool=True, defaultresponseonerror=None):
        responsejson = None

        # obtain token
        if self.token != '':
            token_obtained = True
        else:
            token_obtained = self._get_token_API10()

        if token_obtained:
            my_auth = CMAuth(self.token)

            # Make the POST request
            api_response = requests.post(url, headers=self.headers, auth=my_auth, json=payload)
            status_code = api_response.status_code
            if status_code == 200:
                self.UpdateTokenFromResponse(api_response)
                responsejson = api_response.json()
            else:
                self.logger.error(f"ApiPost FAILED with statuscode({status_code}) = {api_response.content})")
                if exitonerror:
                    exit(1)
                else:
                    return defaultresponseonerror
        return responsejson

    # retrieve json from API or handle error
    def ApiPutJson(self,url: str, payload, exitonerror:bool=True, defaultresponseonerror=None):
        responsejson = None

        # obtain token
        if self.token != '':
            token_obtained = True
        else:
            token_obtained = self._get_token_API10()

        if token_obtained:
            my_auth = CMAuth(self.token)

            # Make the PUT request
            api_response = requests.put(url, headers=self.headers, auth=my_auth, json=payload)
            status_code = api_response.status_code
            if status_code == 200:
                self.UpdateTokenFromResponse(api_response)
                responsejson = api_response.json()
            else:
                self.logger.error(f"ApiPut FAILED with statuscode({status_code}) = {api_response.content})")
                if exitonerror:
                    exit(1)
                else:
                    return defaultresponseonerror
        return responsejson

    def API_GetAllCategories(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/categories')
        return pd.json_normalize(responsejson)

    def API_GetAllAlertsJSON(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/alerts')

        df_allAlerts = pd.json_normalize(responsejson)

        df_allAlerts = df_allAlerts.merge(self.df_folders[['FolderID', 'FolderName']], on='FolderID',
                                          suffixes=('_A', '_B'))
        columns = ['FolderName'] + [col for col in df_allAlerts.columns if col != 'FolderName']
        df_allAlerts = df_allAlerts[columns]

        #Add AlertFullname
        df_allAlerts["AlertFullname"] = df_allAlerts["FolderName"]+"/"+df_allAlerts["Name"]
        columns = ['AlertFullname'] + [col for col in df_allAlerts.columns if col != 'AlertFullname']
        df_allAlerts = df_allAlerts[columns]

        self.df_allAlerts = df_allAlerts

        return responsejson, df_allAlerts

    def ParseFolderFullName(self, nameprefix, folderjson):
        folderdict={}
        for f in folderjson:
            foldername = nameprefix+f["FolderName"]
            folderdict[f["FolderID"]] = foldername
            if f["Children"]:
                childrendict = self.ParseFolderFullName(foldername+"/",f["Children"])
                folderdict.update(childrendict)
        return folderdict

    def Update_df_folders(self):
        self.df_folders = pd.DataFrame(list(self.folderdict.items()), columns=['FolderID', 'FolderName'])
        return self.df_folders

    def API_GetAllFolders(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/Folders')

        self.folderdict = self.ParseFolderFullName("",responsejson)

        return self.Update_df_folders()

    def GetFoldernameFromID(self, folderid, df_allFolders):
        # Get Foldername from ID
        # Retrieve the FolderName for the specified FolderID
        folder_name = df_allFolders.loc[df_allFolders['FolderID'] == folderid, 'FolderName']

        # Check if any result is found and print it
        result = "NOT_FOUND"
        if not folder_name.empty:
            result = folder_name.iloc[0]
        return result

    def find_keys_by_value(self, d:dict, target_value):
        keys = [key for key, value in d.items() if value == target_value]
        return keys

    def CreateFolderChain(self, FolderFullName):
        # create folder
        folders = FolderFullName.split("/")
        folderid = None
        folderfullname  =""
        for foldername in folders:
            #Check if Folder already exists
            folderfullname += foldername
            if folderfullname in self.folderdict.values():
                folderid = self.find_keys_by_value(self.folderdict,folderfullname)[0]
            else:
                #Create folder
                folderid = self.API_CreateFolder(foldername,folderid)
                #Add folder to folderdict
                self.folderdict[folderid]=folderfullname
            folderfullname+="/"

        self.Update_df_folders()

        return folderid



    def API_CreateFolder(self,foldername,folderparentid):
        newfolder = {
              "Gen": 0,
              "FolderID": None,
              "FolderName": foldername,
              "ParentFolderID": folderparentid,
              "FolderPath": None,
              "OwnerName": None,
              "Children": None,
              "GroupName": None,
              "GroupID": None,
              "CreateDate": None,
              "LastModified": None,
              "ContainsAlert": None,
              "ContainsScore": None,
              "ContainsSearch": None,
              "ContainsCategorySection": None,
              "ContainsTagSection": None,
              "SlnInstallationID": None
            }
        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/folders'

        # Define the payload with the loaded category data
        payload = newfolder #json.dumps(newfolder)

        # Make the POST request
        api_response = self.ApiPostJson(url, payload)

        folderid = api_response
        self.logger.info(f"Folder {foldername} created as folderid={folderid}")
        return folderid



    def API_GetAllSections(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/Sections')
        return pd.json_normalize(responsejson)

    def API_GetAllAlerts(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/Alerts')
        return pd.json_normalize(responsejson)



    def API_GetGenerations(self):
        # Get Generations dataframe from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/categories/generation')
        df_generations = pd.DataFrame(responsejson)
        df_generations.rename(columns={'BucketID': 'BucketId'}, inplace=True)
        df_generations.drop(columns=['SearchID'], inplace=True)
        return df_generations


    def API_GetAllScores(self):
        self.API_GetAllFolders()
        # Get list of all scores adn their data from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/scores')

        df_allScores = pd.json_normalize(responsejson)
        df_allScores["FolderID"] = df_allScores["FolderId"]
        df_allScores = df_allScores.merge(self.df_folders[['FolderID', 'FolderName']], on='FolderID', how='left',
                                          suffixes=('_A', '_B')).fillna({'FolderName':''})
        columns = ['FolderName'] + [col for col in df_allScores.columns if col != 'FolderName']
        df_allScores = df_allScores[columns]

        # Add AlertFullname
        df_allScores["ScoreFullname"] = df_allScores.apply(
            lambda row: row["FolderName"] + "/" + row["Name"] if row["FolderName"]!="" else row["Name"], axis=1  )
        columns = ['ScoreFullname'] + [col for col in df_allScores.columns if col != 'ScoreFullname']
        df_allScores = df_allScores[columns]

        self.df_allScores = df_allScores

        return responsejson, df_allScores

    def GetAllCategories(self):
        # Retrieve the Generations so the Categories can be ordered correctly
        df_generations = self.API_GetGenerations()
        # helper.AppenddataframeAsSheetToExcel(xls_filename,"Generations",df_generations)

        # Retrieve the list of all categories
        df_AllCategories = self.API_GetAllCategories()
        # Remove "Categories." from the "BucketFullNames" column
        df_AllCategories['BucketFullname'] = df_AllCategories['BucketFullname'].str.replace('Categories.', '')

        #Add USEdin
        df_AllCategories['UsedIn'] = ""
        col = df_AllCategories.pop('UsedIn')
        df_AllCategories.insert(2, 'UsedIn', col)

        #Adds dependsOn
        df_AllCategories['DependsOn'] = ""
        col = df_AllCategories.pop('DependsOn')
        df_AllCategories.insert(3, 'DependsOn', col)

        # Add Generations to AllCats
        df_AllCategories = pd.merge(df_generations[['BucketId', 'Generation']], df_AllCategories, on='BucketId',
                                    how='right')
        # Sort the DataFrame by col1 in a case-insensitive manner
        df_AllCategories['bfn_lower'] = df_AllCategories[
            'BucketFullname'].str.lower()  # Create a new column with lowercase values of col1
        df_AllCategories = df_AllCategories.sort_values(by='bfn_lower', ignore_index=True)
        # Drop the temporary lowercase column
        df_AllCategories = df_AllCategories.drop(columns='bfn_lower')

        return df_AllCategories

    def API_GetAllSections(self):
        # Get list of all categories from API
        responsejson = self.ApiGetJson(f'https://{self.API}.callminer.net/api/v2/sections/category')
        #remove Groups
        for s in responsejson:
            s['Groups'] =""
        return pd.json_normalize(responsejson)

    def API_CheckTenantName(self, targettenant:str):
        #Get Tenant Name
        tenant=""
        responsejson = self.ApiGetJson(f"https://{self.API}.callminer.net/api/v2/profile")
        print(responsejson)
        if responsejson != None:
            #Get Tenant name
            tenant = responsejson["TenantName"]
        if tenant.lower() != targettenant.lower() and targettenant!="":
            self.logger.error(f"Target Tenant ({targettenant}) and tenant from API token ({tenant}) DO NOT MATCH - Exiting...")
            exit(1)
        return tenant

    def API_GetCategory(self,categoryFullname):
        #Get all the categories
        df_AllCategories = self.API_GetAllCategories()

        #Find the Category ID of the row in df_categories that matches on categoryfullname
        matching_rows = df_AllCategories.loc[df_AllCategories['BucketFullname'] == categoryFullname, 'BucketId']

        if matching_rows.empty:
            # Category not found, return 0
            catid = 0
        else:
            catid = matching_rows.iloc[0]

        return catid


    def API_UpdateCategory(self,category_data):
        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/categories'

        # Define the payload with the loaded category data
        payload = category_data

        # Make the POST request
        api_response = self.ApiPutJson(url, payload)

        catid = api_response
        bucketname = category_data["BucketName"]
        self.logger.info(f"Category {bucketname} updated as catid={catid}")
        return catid

    def API_GetCategoryJSON(self, catid):
        # Get individual category JSON data by category ID
        requesturl = f'https://{self.API}.callminer.net/api/v2/categories/{catid}?components=true'
        category_data = self.ApiGetJson(requesturl)
        return category_data
    
    def API_RenameCategory(self,old_categoryFullname, new_categoryName):
        # Retrieve the Category with this fullname from the FEAPI endpoint
        # Update the retrieved JSON with the new Categoryname. Do not update the section
        #Update the Category with the new name using the API_UpdateCategory function
        catid = self.API_GetCategory(old_categoryFullname)
        if catid==0:
            self.logger.error(f"Category {old_categoryFullname} not found")
            return 0
        category_data = self.API_GetCategoryJSON(catid)
        category_data["BucketName"] = new_categoryName
        catid = self.API_UpdateCategory(category_data)
        self.logger.info(f"Category catid={catid} with Fullname={old_categoryFullname} renamed as {new_categoryName}")
        return catid


    def API_CreateCategory(self,category_data):
        # token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
        # swagger found here: http://feapi.callminer.net
        # once logged in to your tenant, copy the JWT token from the URL line into the token variable

        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/categories'

        # Define the payload with the loaded category data
        payload = category_data

        # Make the POST request
        api_response = self.ApiPostJson(url, payload)

        catid = api_response
        bucketname = category_data["BucketName"]
        self.logger.info(f"Category {bucketname} created as catid={catid}")
        return catid

    def API_UpdateScore(self, score_data):
        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/scores'

        # Define the payload with the loaded category data
        payload = score_data

        # Make the POST request
        api_response = self.ApiPutJson(url, payload)

        if api_response == None:
            return None
        id = api_response["Id"]
        name = score_data["Name"]
        self.logger.info(f"Score {name} updated as id={id}")
        return id

    def API_GetScore(self, scoreFullname):
        # Get all the scores
        _, df_AllScores = self.API_GetAllScores()

        # Find the Score ID of the row in df_scores that matches on scorefullname
        # Score fullname format is typically "FolderName.ScoreName"
        matching_rows = df_AllScores.loc[df_AllScores['Name'] == scoreFullname, 'Id']

        if matching_rows.empty:
            # Score not found, return 0
            scoreid = 0
        else:
            scoreid = matching_rows.iloc[0]

        return scoreid

    def API_GetScoreJSON(self, scoreid):
        # Get individual score JSON data by score ID
        requesturl = f'https://{self.API}.callminer.net/api/v2/scores/{scoreid}'
        score_data = self.ApiGetJson(requesturl)
        return score_data

    def API_RenameScore(self, old_scoreFullname, new_scoreName):
        # Retrieve the Score with this fullname from the FEAPI endpoint
        # Update the retrieved JSON with the new Score name
        # Update the Score with the new name using the API_UpdateScore function
        scoreid = self.API_GetScore(old_scoreFullname)
        if scoreid == 0:
            self.logger.error(f"Score {old_scoreFullname} not found")
            return 0
        score_data = self.API_GetScoreJSON(scoreid)
        score_data["Name"] = new_scoreName
        scoreid = self.API_UpdateScore(score_data)
        self.logger.info(f"Score scoreid={scoreid} with Fullname={old_scoreFullname} renamed as {new_scoreName}")
        return scoreid

    def API_CreateScore(self, score_data):
        # token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
        # swagger found here: http://feapi.callminer.net
        # once logged in to your tenant, copy the JWT token from the URL line into the token variable

        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/scores'

        # Define the payload with the loaded category data
        payload = score_data

        # Make the POST request
        api_response = self.ApiPostJson(url, payload)
        if api_response == None:
            return None
        id = api_response["Id"]
        name = score_data["Name"]
        self.logger.info(f"Score {name} created as id={id}")
        return id

    def API_CreateSection(self,sectionname, Importer):
        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/sections/category'

        # Define the payload with the loaded category data
        ownername = Importer
        if Importer == "null":
            ownername = None
        payload = {
            "SectionName": sectionname,
            "OwnerName": ownername,
            "VisibilityMode": "All",
            "Groups": []
        }

        # Make the POST request
        api_response = self.ApiPostJson(url, payload)
        if api_response != None:
            sectionid = api_response
            self.logger.info(f"Section {sectionname} created as sectionid={sectionid}")
        else:
            self.logger.info(f"Section CREATE FAILED")
            sectionid = None

        return sectionid

    def API_CreateAlert(self, alertname, alert_json):

        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/alerts'

        # Define the payload with the loaded category data
        payload = alert_json

        # Make the POST request
        api_response = self.ApiPostJson(url, payload)
        if api_response == None:
            return None
        id = api_response["AlertID"]
        self.logger.info(f"Score {alertname} created as id={id}")
        return id

    def API_UpdateAlert(self, alertname, alertid, alert_json):
        # Define the URL and headers
        url = f'https://{self.API}.callminer.net/api/v2/alerts/{alertid}'

        # Define the payload with the loaded category data
        payload = alert_json

        # Make the POST request
        api_response = self.ApiPutJson(url, payload)

        alertid = api_response["AlertID"]
        self.logger.info(f"Category {alertname} updated as alertid={alertid}")
        return alertid



    def GetUsedInString(self,bucketid:int, API:str):
        UsedInList = []
        usedinstring = ""

        try:
            requesturl = f'https://{API}.callminer.net/api/v2/categories/{bucketid}/dependencies'
            responsePayload = self.ApiGetJson(requesturl)
            for d in responsePayload:
                if d['Type'] == "ScoreComponent" or d['Type'] =="CateogryComponent":
                    usedin = f"({d['Type']}: {d['DependenciesContentName']}.{d['Name']})"
                else:
                    usedin = f"({d['Type']}: {d['Name']})"
                if usedin !="" and usedin!= None and usedin not in UsedInList:
                    UsedInList.append(usedin)
            if len(UsedInList)>0:
                usedinstring = ",".join(UsedInList)
        except Exception as e:
            self.logger.error(f"ERROR retrieving dependencies for Bucketid({bucketid}).  Exception = {e}")

        return usedinstring



