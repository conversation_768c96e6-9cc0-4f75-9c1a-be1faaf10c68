{"BucketId": "3981", "BucketFullname": "Categories.Emotions.Negative Emotion", "BucketDescription": "Identifies when negative language occurs on a call.", "UserEditable": false, "BucketName": "Negative Emotion", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Negative Emotion", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2023-05-04T11:03:25.26-04:00", "Creator": "<EMAIL>", "Version": 5, "LanguageTag": "en", "EffectiveDate": "2025-03-25T16:02:43.263", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -21955, "Name": "Anger", "Description": "", "ActionString": "(emotions.anger)", "UserEntry": "CAT:[Emotions.Anger] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21954, "Name": "Disappointment", "Description": "", "ActionString": "(emotions.disappointment)", "UserEntry": "CAT:[Emotions.Disappointment] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21953, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Description": "", "ActionString": "(emotions.disgust)", "UserEntry": "CAT:[Em<PERSON>s.Disgust] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21952, "Name": "Fear", "Description": "", "ActionString": "(emotions.fear)", "UserEntry": "CAT:[Emotions.Fear] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-03-25T16:02:43.263", "ComponentCount": 4}