'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''

import FEAPI_Helpers

import json
import os,sys
import pandas as pd
import re
pd.set_option('io.excel.xlsx.writer', 'openpyxl')
import argparse
import configparser
#Setup Timestamps
from datetime import datetime

# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token file.
# the URL will look like this: https://feapi.callminer.net//swagger/ui/index?JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9anjOeIiPmLGX_q5DqTme12Gmx2L-ztYMgFwg66b4vI
# eveyrthing after JWT= is what should be copied into token.txt

class config:



    # Default values
    '''
    [FEAPI]
    '''
    API = "feapi"
    DataRoot = "tenant_data"
    LogRoot = "logs"
    DEBUG = False
    LoggerLevel = "DEBUG"

    '''
    [GetCategory]
    '''
    Tenant = "specify target tenant"
    BucketFullnameFilter = "specify filter regex"
    OutputPrefix = ""
    GetDependencies = True
    IgnoreFilterDependencies = False



    API_timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    json_output_dir = ""
    @staticmethod
    def FilenameInOutputFolder(filename):
        return os.path.join(config.json_output_dir, filename)

    @staticmethod
    def xls_filename():
        return config.FilenameInOutputFolder ( f"{config.OutputPrefix}.Exported_Categories.xlsx")

    @staticmethod
    def AllCategories_xls_filename():
        return config.FilenameInOutputFolder (  f"AllCategories.xlsx")

    @staticmethod
    def PyFilename():
        os.path.basename(sys.argv[0])

    @staticmethod
    def SetOutputfolder(BucketFullnameFilter):
        # Create handlers and log folders
        if not os.path.exists(config.DataRoot):
            os.makedirs(config.DataRoot)

        # Check if the directory exists and append timestamp if it does
        if config.OutputPrefix == "" or config.OutputPrefix == None:
            config.OutputPrefix = helper.cleanfilename(BucketFullnameFilter)
        json_output_dir = f"{config.DataRoot}/{config.Tenant}/{config.OutputPrefix}"
        if os.path.exists(json_output_dir):
            logger.info(
                f"Output Folder ( {json_output_dir} ) already exists - creating copy as {json_output_dir}_{config.timestamp}")
            json_output_dir = f"{json_output_dir}_{config.timestamp}"
        config.json_output_dir = json_output_dir


    @staticmethod
    def read_ini_file(file_path:str=""):
        if file_path=="":
            file_path = f"{os.path.basename(sys.argv[0])}.INI"
        if os.path.exists(file_path):
            config.strip_lines_with_triple_single_quotes(file_path,file_path)
            configparse = configparser.ConfigParser()
            configparse.read(file_path)

            config.DEBUG = configparse.get('FEAPI', 'DEBUG').strip('"')  == 1
            config.LoggerLevel = configparse.get('FEAPI', 'LoggerLevel').strip('"')
            config.API = configparse.get('FEAPI', 'API').strip('"')
            config.Tenant = configparse.get('FEAPI', 'Tenant').strip('"')
            config.LogRoot = configparse.get('FEAPI', 'LogRoot').strip('"')
            config.DataRoot = configparse.get('FEAPI', 'DataRoot').strip('"')
        else:
            print(f"ERROR: INI file at {file_path} does not exist **************************")

    @staticmethod
    def strip_lines_with_triple_single_quotes(input_file: str, output_file: str):
        with open(input_file, 'r') as file:
            lines = file.readlines()
            # Filter out lines that are just ''' (including any whitespace)
        cleaned_lines = [line for line in lines if line.strip() != "'''"]
        # Write the cleaned lines to the output file
        with open(output_file, 'w') as file:
            file.writelines(cleaned_lines)

config.read_ini_file()

import logging
# Set up logging configuration
logger = logging.getLogger(config.PyFilename())
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

#reset logger level if passed in args
level_string = config.LoggerLevel.upper()  # Ensure case-insensitivity
level = logging.getLevelName(level_string)
if isinstance(level, int):  # Check if a valid level was found
    logging.getLogger().setLevel(level)
    print(f"Logging level set to: {logging.getLevelName(level)}")
else:
    print(f"Invalid logging level string passed as config: {level_string}")

# Create handlers and log folders
if not os.path.exists(config.LogRoot):
    os.makedirs(config.LogRoot)

logprefix = f"{config.LogRoot}/{config.PyFilename()}-{config.timestamp}"
file_handler = logging.FileHandler(f'{logprefix}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'{logprefix}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'{logprefix}.warnings.txt')  # Log to errors.txt

console_handler = logging.StreamHandler()  # Log to console


# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)



#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, config)


#------------------------------------------------------------------------------------------------------------








def GetCategory(bucketrow, df_AllCategories, xls_filename):
    deplist_forthisCategory = []
    bucketid = bucketrow.BucketId.iloc[0]
    gen = bucketrow.Generation.iloc[0]
    bucketfullname = bucketrow.BucketFullname.iloc[0]

    #Get who depends on this cat
    usedinstring =  helper.GetUsedInString(bucketid,config.API)

    # Get category information
    requesturl = f'https://{config.API}.callminer.net/api/v2/categories/{bucketid}?components=true'
    responsePayload = helper.ApiGetJson(requesturl)
    responsePayload['BucketId'] = f'{bucketid}'

    bucketfilename = f"Gen{gen}_{bucketfullname}_{bucketid}"  # Use a meaningful name for the sheet
    bucketfilename = helper.cleanfilename(bucketfilename, "_")
    json_filename = os.path.join(config.json_output_dir, f"{bucketfilename}.json")
    # Write JSON object to a file
    with open(json_filename, 'w') as json_file:
        json.dump(responsePayload, json_file, indent=4)  # `indent=4` for pretty-logger.infoing

    # Assuming responsePayload is defined and contains your data
    df_SearchComps = pd.json_normalize(responsePayload, record_path=['SearchComponents'],
                                       meta=['BucketId', 'BucketFullname'])
    # Add new columns
    df_SearchComps['FilterString'] = ""
    df_SearchComps['DependsOn'] = ""
    # Rearrange columns to put the meta columns first
    meta_columns = ['BucketId', 'BucketFullname']  # List of meta columns
    new_columns_order = meta_columns + [col for col in df_SearchComps.columns if col not in meta_columns]
    # Reorder the DataFrame
    df_SearchComps = df_SearchComps[new_columns_order]

    #GetCategory as JSON
    data = responsePayload

    # Get Dependencies in SearchComponent Filters
    # Update SearchComponent keys
    for sc in data['SearchComponents']:
        deplist_forthisSC = []
        sc_name = sc["Name"]
        filterstring, filterdeplist = helper.ParseFilters(sc['Filters'],df_AllCategories)
        # Update Filterstring
        df_SearchComps.loc[
            df_SearchComps['Id'] == sc['Id'], 'FilterString'] = filterstring

        for dep in filterdeplist:
            if config.IgnoreFilterDependencies==False:
                logger.warning(f"Adding Filter Dependency on BucketID({dep}={helper.GetCategoryFullname(dep,df_AllCategories)}) from FILTER of SearchComponent ({bucketfullname}.{sc_name})")
                #add BucketID to deplist if not alreayd added
                if dep not in deplist_forthisSC:
                    deplist_forthisSC.append(dep)
            else:
                logger.warning(
                    f"IGNORING Filter Dependency on BucketID({dep}={helper.GetCategoryFullname(dep, df_AllCategories)}) from FILTER of SearchComponent ({bucketfullname}.{sc_name})")

        # Add Filters for this search component to the output file
        filterfilename = config.FilenameInOutputFolder(f"Filters.xlsx")
        if filterstring != "":
            helper.LogFilter(bucketfullname, sc['Name'], filterstring, filterfilename)
            logger.info(
                f"Saving Filters for SearchComponent {bucketfullname}.{sc_name} = {filterstring}")

        # Check for Category Dependency
        matches,founddeplist = helper.ParseEQL(sc['UserEntry'], df_AllCategories)
        if matches !="":
            logger.warning(f"Found Dependencies ({matches}) in UserEntry of SearchComponent ({bucketfullname}.{sc_name})")

            eqldependfilename = config.FilenameInOutputFolder(f"EQLDependencies.xlsx")
            helper.LogEQLDependency(bucketfullname, sc['Name'], matches, eqldependfilename)
            logger.warning(
                f"Saving EQL Dependencies found for SearchComponent:{bucketfullname}.{sc['Name']}.\tEQLDependencies = {matches}")

        for dep in founddeplist:
            #add BucketID to deplist if not alreayd added
            if dep not in deplist_forthisSC:
                deplist_forthisSC.append(dep)

        filtered_df = df_AllCategories[df_AllCategories['BucketId'].isin(deplist_forthisSC)]
        deplist_string = filtered_df['BucketFullname'].str.cat(sep=',')
        if deplist_string != "":
            logger.info(
                f"Adding cumulative DependsOn and FilterString for this SearchComponent {bucketfullname}.{sc_name}\tDependsOn={deplist_string} \tFilterString={filterstring}")

        df_SearchComps.loc[df_SearchComps['Id'] == sc['Id'], 'FilterString'] = filterstring
        df_SearchComps.loc[df_SearchComps['Id'] == sc['Id'], 'DependsOn'] = deplist_string

        #add deplist for this SC to deplistfor this Category
        for dep in deplist_forthisSC:
            if dep not in deplist_forthisCategory:
                deplist_forthisCategory.append(dep)

    helper.AppendDataframeToExistingSheet(xls_filename,"All SearchComponents",df_SearchComps)
    helper.AppenddataframeAsSheetToExcel(xls_filename,bucketfilename, df_SearchComps)

    return deplist_forthisCategory, usedinstring



#------------------------------------------------------------------------------------------------------------


def GetCategories():
    # Dump the json and xls of each category with its components
    xls_filename = config.xls_filename()

    #Getall categories with Genrations added
    df_AllCategories = helper.GetAllCategories()

    # Filter the Categories by the FilterString
    regex = re.compile(config.BucketFullnameFilter)
    CatsToGet = df_AllCategories[df_AllCategories['BucketFullname'].str.match(regex)]['BucketId'].astype(int).tolist()

    #Show categories and confirm export
    df_CatsToGet = df_AllCategories[df_AllCategories['BucketId'].isin(CatsToGet)]
    i=1
    logger.info(f"Exporting these ({len(CatsToGet)} Categories:")
    logger.info("-------------------------------------------------------------------------")
    for value in df_CatsToGet['BucketFullname']:
        logger.info(f"{i}. {value}")
        i +=1

    #Confirm Export
    if helper.ask_to_continue("\nIs this the intended list for export?") == False:
        exit(1)



    #Loop Thru each Category, retrieving it and outputting it to JSON
    i = 0
    extradeplist = []
    while i < len(CatsToGet):
        BucketID = CatsToGet[i]

        BucketRow = df_AllCategories[df_AllCategories['BucketId'] == BucketID]
        bucketfullname = BucketRow.BucketFullname.iloc[0]
        logger.info("-------------------------------------------------------------------------------------------")
        logger.info(f"RETRIEVING ({i+1}/{len(CatsToGet)}) - Bucketid={BucketID} - {bucketfullname}")

        # Process this Category, and get any Dependencies Found
        deplist_foundinthisCategory, usedin = GetCategory(BucketRow,df_AllCategories, xls_filename)

        #Update UsedIn
        df_AllCategories.loc[df_AllCategories['BucketId'] == BucketID, 'UsedIn'] = usedin

        for depcatid in deplist_foundinthisCategory:
            # Add Any Dependencies not already in the list
            if config.GetDependencies:
                if depcatid not in CatsToGet:
                    logger.info(f"Adding Dependency on BucketID ({depcatid}={helper.GetCategoryFullname(depcatid,df_AllCategories)}) to list of Categories to retrieve ")
                    CatsToGet.append(depcatid)
                    extradeplist.append(depcatid)
                else:
                    logger.info(f"Already exporting Dependency on BucketID ({depcatid}={helper.GetCategoryFullname(depcatid, df_AllCategories)})")
            else:
                logger.info(
                    f"Ignoring Dependency on BucketID ({depcatid}={helper.GetCategoryFullname(depcatid, df_AllCategories)}) - GetDependencies set to False...")

        #Update the deplist_forthiscat
        filtered_df = df_AllCategories[df_AllCategories['BucketId'].isin(deplist_foundinthisCategory)]
        deplist_string = filtered_df['BucketFullname'].str.cat(sep=',')
        df_AllCategories.loc[df_AllCategories['BucketId'] == BucketID, 'DependsOn'] = deplist_string

        logger.info(f"EXPORT COMPLETE ( {i+1} / {len(CatsToGet)} ) - Category ( {bucketfullname} ) ")

        i +=1

    #df_AllCategories = df_AllCategories.sort_values(by=['Generation', 'BucketFullname'])
    helper.AppenddataframeAsSheetToExcel(xls_filename,"ALL Categories",df_AllCategories)
    helper.move_last_sheet_to_front(xls_filename)

    # Get the df of Categories Exported by filtering by extradeplist and export to excel
    df_filteredCats = df_AllCategories[df_AllCategories['BucketId'].isin(extradeplist)]
    helper.AppenddataframeAsSheetToExcel(xls_filename, "Extra DependsOn Categories", df_filteredCats)
    helper.move_last_sheet_to_front(xls_filename)

    #Get the df of Categories Exported by filtering by CatsToGet and export to excel as first sheet
    df_filteredCats= df_AllCategories[df_AllCategories['BucketId'].isin(CatsToGet)]
    helper.AppenddataframeAsSheetToExcel(xls_filename,  "Exported Categories",df_filteredCats)
    helper.move_last_sheet_to_front(xls_filename)

    #copy AllCategories sheet to AllCategories.xlsx
    helper.AppenddataframeAsSheetToExcel(config.AllCategories_xls_filename(), "ALL Categories", df_AllCategories)

def str_to_bool(s):
    if s.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif s.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')


def main():
    #get cmdline args and overwrite defaults from INI
    config.read_ini_file()

    parser = argparse.ArgumentParser(description='Argument Parser')
    parser.add_argument('--DEBUG', action='store_true', default=False,  help='Set DEBUG flag')
    parser.add_argument('--Tenant', type=str, help='Specify target tenant (must match tenant that token is for)')
    parser.add_argument('--API', nargs='?', type=str, default="feapi", help='API (feapi/feapif) (optional)')
    parser.add_argument('--OutputPrefix', type=str, help='Specify output prefix to use. Leave blank to use cleaned filter regex')
    parser.add_argument('--BucketFullnameFilter', type=str, help='Specify bucket fullname filter')
    parser.add_argument('--GetDependencies',  type=str_to_bool, default=False,  help='Set GetDependencies flag, default false')
    parser.add_argument('--IgnoreFilterDependencies',  type=str_to_bool, default=False,  help='Ignore Dependencies found in filters, default False')

    args = parser.parse_args()
    logger.info(f"ARGUMENTS PASSED:-------------------------------------------")
    logger.info("\n".join(FEAPI_Helpers.FEAPI_Helper.show_all_attributes_and_values(args)))

    # Loop through attributes of A and copy from B if they match
    FEAPI_Helpers.FEAPI_Helper.UpdateObjectA_WithB(config,args)

    # Global Helper functions ------------------------------------------------------------------------------
    global helper
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, config)

    # Check if the tenant matches
    logger.info(f"Checking if API tenant matches target Config.Tenant ({config.Tenant})")
    config.tenant = helper.API_CheckTenantName(config.Tenant)

    # Create a directory for JSON files
    config.SetOutputfolder(config.BucketFullnameFilter)
    logger.info(f"Creating outputfolder ({config.json_output_dir})")
    os.makedirs((config.json_output_dir))

    logger.info(f"CONFIG: -----------------------------------------------------")
    logger.info("\n".join(FEAPI_Helpers.FEAPI_Helper.show_all_attributes_and_values(config)))

    logger.info(f"Retrieving Content from Tenant( {config.tenant}) into {config.json_output_dir}")
    GetCategories()
    

if __name__ == '__main__':
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")

