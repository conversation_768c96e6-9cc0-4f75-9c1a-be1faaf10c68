{"BucketId": "4278", "BucketFullname": "Categories.Emotions.Happiness", "BucketDescription": "Captures language indicating the customer is expressing Happiness.", "UserEditable": false, "BucketName": "Happiness", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Happiness", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-03-25T16:02:30.843-04:00", "Creator": "<EMAIL>", "Version": 5, "LanguageTag": "en", "EffectiveDate": "2025-03-25T16:02:39.56", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -21970, "Name": "Happiness Alert", "Description": "", "ActionString": "[very|so|absolutely|extra|especially|quite|really|super|extremely|highly+good|great|plesant|compatible|happy|happiest|happier|happiness|adorable|positive|goodness|greatness|attractive|greatest|neat|hopeful|relieved|cool|well|perfect|glad:0.5]", "UserEntry": "\"very|so|absolutely|extra|especially|quite|really|super|extremely|highly good|great|plesant|compatible|happy|happiest|happier|happiness|adorable|positive|goodness|greatness|attractive|happy|greatest|neat|hopeful|relieved|cool|well|perfect|glad\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21969, "Name": "positive_sentiment-cant blame", "Description": "", "ActionString": "[can`t|cannot|won’t|ain’t+hate|blame|beat|complain|deny|spain|plain:1][wasn`t|not+your+fault:1.2][nobod%+fault:1]$OR$$OR$", "UserEntry": "((\"nobod* fault\":1) OR (\"wasn't|not your fault\":1.2) OR (\"can't|cannot|won’t|ain’t hate|blame|beat|complain|deny|spain|plain\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21968, "Name": "positive_sentiment-friendly", "Description": "", "ActionString": "<happiness><eager><energetic><talkative>$OR$$OR$$OR$", "UserEntry": "(talkative|energetic|eager|happiness)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21967, "Name": "positive_sentiment-grateful", "Description": "", "ActionString": "<cared><healed><awarded><grateful><fortunate>$OR$$OR$$OR$$OR$", "UserEntry": "(fortunate|grateful|awarded|healed|cared)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21966, "Name": "positive_sentiment-nice", "Description": "", "ActionString": "<not><nice><kind>$OR$$NOTAFTER:0.5$<delightful><pleasant><lucky><sweet%>$OR$$OR$$OR$$OR$", "UserEntry": "((sweet*|lucky|pleasant|delightful) OR ((kind|nice) NOT AFTER:.5 (not)))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21965, "Name": "positive_sentiment-professional-organized", "Description": "", "ActionString": "<decisive><concise><organize%><meticulous><attentive><anal>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(anal|attentive|meticulous|organize*|concise|decisive)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21964, "Name": "positive_sentiment-professional-polite", "Description": "", "ActionString": "<respect%><sympathies><empathy><sympathy><mannered><tolerance><thoughtful><apologetic><polite><accommodating><courteous><gracious>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(gracious|courteous|accommodating|polite|apologetic|thoughtful|tolerance|mannered|sympathy|empathy|sympathies|respect*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21963, "Name": "positive_sentiment-thankful(empathy)", "Description": "", "ActionString": "(acknowledgement.empathy)<thank%><appreciate%>$OR$$AFTER:10$[certainly+appreciate:1.5]$OR$", "UserEntry": "((\"certainly appreciate\":1.5) OR ((appreciate*|thank*) AFTER:10 (CAT:[Acknowledgement.Empathy])))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21962, "Name": "positive_sentiment-great", "Description": "", "ActionString": "[alrighty+sounds:1][sound%+like+plan:1.2][perfectly+fine:1][greatest+thing:1][excellent+job|service|impression:1]$AND$[supper|sounds|alright%|great+great|perfect|terrific:1][wonderful|supper|supple+wonderful:1]<perfectly><excellent><ecstatic><greatness><awesome><fantastic>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((fantastic|awesome|greatness|ecstatic|excellent|perfectly) OR (\"wonderful|supper|supple wonderful\":1) OR (\"supper|sounds|alright*|great great|perfect|terrific\":1) OR (\"excellent job|service|impression\":1) (\"greatest thing\":1) OR (\"perfectly fine\":1) OR (\"sound* like plan\":1.2) OR (\"alrighty sounds\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21961, "Name": "positive_sentiment-very good", "Description": "", "ActionString": "[really|very|too|quite|real|always|lawyers+nice|clear|lucky|good|happy|glad:1.2]", "UserEntry": "(\"really|very|too|quite|real|always|lawyers nice|clear|lucky|good|happy|glad\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21960, "Name": "positive_sentiment-rejuvenated", "Description": "", "ActionString": "<rejuvenat%>", "UserEntry": "(rejuvenat*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21959, "Name": "positive_sentiment-deserving", "Description": "", "ActionString": "<deserve><assured><assure>$OR$$OR$", "UserEntry": "(assure|assured|deserve)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21958, "Name": "positive_sentiment-feeling", "Description": "", "ActionString": "<loving><feeling>$OR$", "UserEntry": "(feeling|loving)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21957, "Name": "positive_sentiment-happy", "Description": "", "ActionString": "<not><haven`t><wasn`t>$OR$$OR$<happy><glad>$OR$$NOTAFTER:1$", "UserEntry": "((glad|happy) NOT AFTER:1 (wasn't|haven't|not))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21956, "Name": "positive_sentiment-helping", "Description": "", "ActionString": "<doesn`t><didn`t><couldn`t><wasn`t><not><isn`t>$OR$$OR$$OR$$OR$$OR$[was|real%|super|very|incredibly|most|extremely+help%:1.5]$NOTNEAR:1$[continue+helping:1]$OR$", "UserEntry": "((\"continue helping\":1) OR ((\"was|real*|super|very|incredibly|most|extremely help*\":1.5) NOT NEAR:1 (isn`t|not|wasn't|couldn't|didn't|doesn't)))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-03-25T16:02:39.56", "ComponentCount": 15}