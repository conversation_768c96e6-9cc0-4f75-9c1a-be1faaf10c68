{"BucketId": null, "BucketFullname": "", "BucketDescription": "Identify questions that are used to gather additional information, clarify issues, or delve deeper into a customer`s concern or inquiry.", "UserEditable": false, "BucketName": "Probing Questions", "SectionName": "", "SectionNameDisplay": "", "SectionId": 2308, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 5.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T12:01:53", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T12:01:53", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Anything Changed?", "Description": null, "ActionString": "[work%+different%:1.4]=1[something+happen%:1.2]=1[something+odd|od:1.4]=1[there+something+else|different+about:2.2]=1[show%+different|unusual:1.8]=1[something+else+happened:1.6]=1[longer+than+normal|usual:0.8]=1[no+longer+same:1.5]=1[different+then|than+before:1.8]=1[has+change%|been+different:1.8]=1[totally|high|quite|big|huge+differen%:1.5]=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[you+said:1][did|has|have+something|recently|completely|totally+chang%:2.2]=1$NOTNEAR:5$$OR$", "UserEntry": "(\"did|has|have something|recently|completely|totally chang*\":2.2)=agent NOT NEAR:5 (\"you said\":1) \nOR (\"totally|high|quite|big|huge differen*\":1.5 \nOR \"has change*|been different\":1.8 \nOR \"different then|than before\":1.8 \nOR \"no longer same\":1.5 \nOR \"longer than normal|usual\" \nOR \"something else happened\":1.6 \nOR \"show* different|unusual\":1.8 \nOR \"there something else|different about\":2.2 OR \n\"something odd|od\":1.4 \nOR \"something happen*\":1.2 \nOR \"work* different*\":1.4)=agent", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Are You Getting Sensors On Prescription?", "Description": null, "ActionString": "[you+receiv%+suppl%+mail|pharmacy:1.7]=1[get%|obtain%|receiv%+prescription%:1]=1$OR$[provid%+you%:1][top+up:1]$OR$[of|you%|are|the|long|in|from|one|yes|it|some|i`m|your|on|our|and+prescription%|description%:1]=1$NOTNEAR:5$[sensor%|centre%|session%|sense%|dentist%|transfer%|have|has|device|reader|sample%|sender%|sentence%|census|server%|them|a|it|product%+prescription%|description%|reception%:2]$OR$$OR$", "UserEntry": "(\"sensor*|centre*|session*|sense*|dentist*|transfer*|have|has|device|reader|sample*|sender*|sentence*|census|server*|them|a|it|product* prescription*|description*|reception*\":2 \nOR (\"of|you*|are|the|long|in|from|one|yes|it|some|I'm|your|on|our|and prescription*|description*\":1)=agent \nNOT NEAR:5 (\"top up\":1 OR \"provid* you*\":1)) \nOR (\"get*|obtain*|receiv* prescription*\":1\nOR \"you receiv* suppl* mail|pharmacy\":1.7)=agent", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Clarifying Customer Symptoms", "Description": null, "ActionString": "[have+you%+experience%:1.5]=1[what|where|what`s|where`s+happ%:1.4]=1[what|where+problem%|error%|message%|miss%:1.8]=1$OR$[first|last+name:0.5]=1[postal%|postcode|post|portal%|order|address%|delivery|dispatch|else|question%|verification:0.2]=1$OR$[what+problem%|issue%:1.5]=1[what+seems+to+be:1.2]=1[have+you+got|been:1.2]=1[what+did+you+do:1.2]=1$OR$$OR$$OR$$NOTNEAR:10$[order%|address|dispatch|help|name:0.2]=1[have+been+day%|system|libra:1.2]=1[you+have+been|using|system|libra:2]=1$OR$$NOTNEAR:5$[order|address|deliver%:0.2]=1[did+you%|he|she+ask%|try|tried|find|found|save%|make|made|take|took|work|lose|think|expect|intend|depend|pretend|lie|discover|wait|leave|look|move|change|replace|repair|exchange|remove|unplug|press|select|plug%|slide|scroll|chose|choose|apply|eat|ate|received|error:1.4]=1$NOTNEAR:2$$OR$$OR$$OR$$OR$", "UserEntry": "(((\"did you*|he|she ask*|try|tried|find|found|save*|make|made|take|took|work|lose|think|expect|intend|depend|pretend|lie|discover|wait|leave|look|move|change|replace|repair|exchange|remove|unplug|press|select|plug*|slide|scroll|chose|choose|apply|eat|took|ate|try|tried|received|error\":1.4) NOT NEAR:2 \"order|address|deliver*\")\nOR ((\"you have been|using|system|libra\":2 OR \"have been day*|system|libra\":1.2) NOT NEAR:5 \"order*|address|dispatch|help|name\")\nOR ((\"what did you do\":1.2 OR \"have you got|been\":1.2 OR \"what seems to be\":1.2 OR \"what problem*|issue*\":1.5) NOT NEAR:10 (\"postal*|postcode|post|portal*|order|address*|delivery|dispatch|else|question*|verification\" OR \"first|last name\"))\nOR (\"what|where  problem*|error*|message*|miss*\":1.8 OR \"what|where|what's|where's happ*\":1.4) OR \"have you* experience*\":1.5)=agent", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Environment", "Description": null, "ActionString": "[sleeping+taking+shower:1.5]!10-95%[may+i+ask|know:1.5]!10-95%[how+you+doing+that:2]!10-95%[are+you+like:1]!10-95%[why+your|the+sensor%:1.5]!10-95%[what|are|were|where|you+are|you|were+doing|taking:1]!10-95%[when+encounter%|receiv%:1]!10-95%$OR$$OR$$OR$$OR$$OR$[watch%+tv:1]!10-95%<wrestling>!10-95%<television>!10-95%<standing>!10-95%<zumba>!10-95%<yoga>!10-95%<active>!10-95%<inactice>!10-95%<bathing>!10-95%<bath>!10-95%<sitting>!10-95%<seating>!10-95%<shower%>!10-95%<dress%>!10-95%<sleep%>!10-95%<exercis%>!10-95%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$AFTER:30$[happened+the+same+day:2]=1!10-95%[where|were+you+doing+time|anything|anyhow:2]!10-95%[+describe|tell+me|what+happen%|doing:2]!10-95%[doing|did|anything+particular:1.5]=1!10-95%[door+frame:1.3]=1!10-95%[period+of+time:1.5]=1!10-95%[appl%+follow%+instructions%:0.8]=1!10-95%[appl%+proper+procedure:1.3]=1!10-95%<jumper>=1!10-95%[changing+clothes|closing:1.3]=1!10-95%[what+activity:1]=1!10-95%[type|kind|specific|some|any|particular+activit%:1.5]=1!10-95%[doing+any|some|kind|type|specific|particular+activit%:2.8]=1!10-95%[fell|fall|came|come|sign+off|of+%expected%:2]=1!10-95%[remember|know|was+reason+fell|fall|came|come|sign+off|of:2.5]=1!10-95%[maybe+bump|bond+against|again:2]=1!10-95%[bump|bond+against|again+something|wall|door:2]=1!10-95%[damage%+or+expose%:1.2]=1!10-95%[accident%+drop%:1.5]=1!10-95%[expos%|export%+to|in|and+moisture:1.3]=1!10-95%[fell|fall|same|came+during|due|in+summer|heat%:2]=1!10-95%[under|drop%+water|washer:1.2]=1!10-95%[getting+out+bath:1.5]=1!10-95%[going|getting+to|for+bed:1.5]=1!10-95%[during+you%+%sleep%:1.8]=1!10-95%[fell|fall|came|come|sign+off|of+shower|bed|home|sleep:2.3]=1!10-95%[fell|fall|came|come|sign+off|of+dressed|exercising|swimming|sleeping|walking|bathing|dressing:2]=1!10-95%[how+did+fell|fall|came|come|sign+off|of:2]=1!10-95%[where+you%|he|she+happen%|went|came|come|fell|fall|sign:2.5]=1!10-95%[were|was+you%|patient+sleep%|shower%|exercis%:2.5]=1!10-95%[where|were|was+you%|he|she|they+specific:2.5]=1!10-95%[doing+at+the|that+moment:2]=1!10-95%[what+exactly+you%|patient+doing:2.5]=1!10-95%[what+were|was|are|is+you%|patient|they+doing:2.5]=1!10-95%[how+often+happen%:2]=1!10-95%[how+fell|fall|came|come|sign+off|of:1.5]=1!10-95%[where|when+exactly+happen%|went|wrong:1.5]=1!10-95%[investigate|explain|tell|where|when+where|when+happen%|went|wrong:2.5]=1!10-95%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((\"investigate|explain|tell|where|when where|when happen*|went|wrong\":2.5 \nOR \"where|when exactly happen*|went|wrong\":1.5 \nOR \"how fell|fall|came|come|sign off|of\":1.5 \nOR \"how often happen*\":2 \nOR \"what were|was|are|is you*|patient|they doing\":2.5 \nOR \"what exactly you*|patient doing\":2.5 \nOR \"doing at the|that moment\":2 \nOR \"where|were|was you*|he|she|they specific\":2.5 \nOR \"were|was you*|patient sleep*|shower*|exercis*\":2.5 \nOR \"where you*|he|she happen*|went|came|come|fell|fall|sign\":2.5 \nOR \"how did fell|fall|came|come|sign off|of\":2 \nOR \"fell|fall|came|come|sign off|of dressed|exercising|swimming|sleeping|walking|bathing|dressing\":2 \nOR \"fell|fall|came|come|sign off|of shower|bed|home|sleep\":2.3 \nOR \"during you* *sleep*\":1.8 \nOR \"going|getting to|for bed\":1.5 \nOR \"getting out bath\":1.5 \nOR \"under|drop* water|washer\":1.2 \nOR \"fell|fall|same|came during|due|in summer|heat*\":2 \nOR \"expos*|export* to|in|and moisture\":1.3 \nOR \"accident* drop*\":1.5 OR \"damage* or expose*\":1.2 \nOR \"bump|bond against|again something|wall|door\":2 \nOR \"maybe bump|bond against|again\":2 \nOR \"remember|know|was reason fell|fall|came|come|sign off|of\":2.5                                                                                                                                                                                                                      \nOR \"fell|fall|came|come|sign off|of *expected*\":2 \nOR \"doing any|some|kind|type|specific|particular activit*\":2.8 \nOR \"type|kind|specific|some|any|particular activit*\":1.5 \nOR \"what activity\":1 OR \"changing clothes|closing\":1.3 \nOR jumper OR \"appl* proper procedure\":1.3 \nOR \"appl* follow* instructions*\" \nOR \"period of time\":1.5 \nOR \"door frame\":1.3 \nOR \"doing|did|anything particular\":1.5)=agent                                                                                                                                                                                                                                                                   \nOR \" describe|tell me|what happen*|doing\":2 OR \"where|were you doing time|anything|anyhow\":2 OR (\"happened the same day\":2)=agent                                                                                              \nOR ((exercis*|sleep*|dress*|shower*|seating|sitting|bath|bathing|inactice|active|yoga|zumba|standing|television|wrestling) OR \"watch* tv\":1) AFTER:30 (\"when encounter*|receiv*\":1 OR \"what|are|were|where|you are|you|were doing|taking\":1 OR \"why your|the sensor*\":1.5 OR \"are you like\":1 OR \"how you doing that\":2 OR \"may i ask|know\":1.5)                                                                 \n OR \"sleeping taking shower\":1.5){10-95%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "OS Requested", "Description": null, "ActionString": "[what|which+phone+do|are+you+have|use:1.3][is+it|that+samsung|iphone|apple|android:1.5][what|which+kind|model+phone:1.5][os+version:1.2][use%+iphone|ios|android|samsung:1.5][software|release|for|soft|s+version|conversion|russian:1.5][updat%+operating+system:1.5][operating|official|windows|mac+system:1.8][system+status:0.5][last+option|section+about:2]$NOTNEAR:10$[system+status:0.5][click|club|go+about:1.3]$NOTNEAR:10$[system+status:0.5]{section|option+about+bottom:1.5}$NOTNEAR:10$[top|job+left+corner:2]{phone+version|kind|model:1.2}<cent%><sens%><reader>$OR$$OR$[what|which+phone+us%|have:2]$NOTNEAR:10$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((\"what|which phone us*|have\":2) NOT NEAR:10 (reader|sens*|cent*) \nOR [phone version|kind|model]:1.2 \nOR \"top|job left corner\":2 \nOR [section|option about bottom]:1.5 NOT NEAR:10 (\"system status\") \nOR \"click|club|go about\":1.3 NOT NEAR:10 (\"system status\") \nOR \"last option|section about\":2 NOT NEAR:10 (\"system status\") \nOR \"operating|official|windows|mac system\":1.8 \nOR \"updat* operating system\":1.5 \nOR \"software|software|release|for|soft|s version|conversion|russian\":1.5 \nOR \"use* iphone|ios|android|samsung\":1.5 OR \"OS version\":1.2 OR \"what|which kind|kind|model phone\":1.5 OR \"is it|that samsung|iphone|apple|android\":1.5 OR \"what|which phone do|are you have|use\":1.3)", "SpeakerList": "", "Status": true, "Weight": -1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Worked Previously?", "Description": null, "ActionString": "[problems%+before|previously:1.2]=1[work%+long+ago:1.8]=1[when+did|was+broke|break:1.8]=1[work%+yesterday|last+night:1.8]=1[fail%+morning|evening|night:1.8]=1[work%+moment%|second%|while|minut%|hour%+ago:1.6]=1[%n’t+work%+either|properly:1.5]=1[work%+before|previous%:1.2]=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"work* before|previous*\":1.2 \nOR \"*n’t work* either|properly\":1.5 \nOR \"work* moment*|second*|while|minut*|hour* ago\":1.6 \nOR \"fail* morning|evening|night\":1.8 \nOR \"work* yesterday|last night\":1.8 \nOR \"when did|was broke|break\":1.8 \nOR \"work* long ago\":1.8 \nOR \"problems* before|previously\":1.2)=agent", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Other", "Description": null, "ActionString": "<%n`t><not><no>$OR$$OR$[is|does+current|one+working|work:1.6]$NOTNEAR:3$[did|was|have|you+already+applied|apply:1.7][the+exact+message:1.2][what|what`s+last|previous+message%:1.6][did|have|was+compare|compared+finger:1.6][did|has+provide%|get|give+reading%:1.7][did|have+start%+after+minute%:2](quality automation_calls.pre questions asked)[when+did+fell|fall+off:2]$NOTNEAR:5$[what|which+number+dial%:2]{e-mail+medical+correction:1.8}{does|did|any+e-mail+callback|additional|information%:1.8}{letter+product+return%:2}[is+sensor+still+in|on+arm:1.2][have|has+you|she|he+taken+the+sensor+off:1.4]<saved><with>$OR$[do|does|is+you|he|day+still+have|wearing+sensor+on:1.4]$NOTNEAR:1$[did|have+you+receive%|get+letter|email|e-mail|notificat%:1.4][how+long:1.2][how+many+days:1.2]{name+of+medical|your+supplier:2}[who|who`s+medical|healthcare+supplier:1.4]<right><still><now>$OR$$OR$[are+you+wearing+it:1.2]$NEAR:2$[how+did|have+you+appl%+sensor%:1.4]<because><unable><never><if><not><%n`t>$OR$$OR$$OR$$OR$$OR$[were|are+able+scan+sensor:1.4]$NOTNEAR:1$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"were|are able scan sensor\":1.4 NOT NEAR:1 (*n't|not|if|never|unable|because) \nOR \"how did|have you appl* sensor*\":1.4 \nOR \"are you wearing it\":1.2 NEAR:2 (now|still|right) \nOR \"who|who's medical|healthcare supplier\":1.4 \nOR [name of medical|your supplier]:2 \nOR \"how many days\":1.2 \nOR \"how long\":1.2 \nOR \"did|have you receive*|get letter|email|e-mail|notificat*\":1.4 \nOR \"do|does|is you|he|day still have|wearing sensor on\":1.4 NOT NEAR:1 (with|saved) \nOR \"have|has you|she|he taken the sensor off\":1.4 \nOR \"is sensor still in|on arm\":1.2\nOR [letter product return*]:2\nOR [does|did|any e-mail callback|additional|information*]:1.8\nOR [e-mail medical correction]:1.8\nOR \"what|which number dial*\":2\nOR \"when did fell|fall off\":2 NOT NEAR:5 CAT:[Quality Automation_Calls.PRE Questions Asked]\nOR \"did|have start* after minute*\":2\nOR \"did|has provide*|get|give reading*\":1.7\nOR \"did|have|was compare|compared finger\":1.6\nOR \"what|what's last|previous message*\":1.6\nOR \"the exact message\":1.2\nOR \"did|was|have|you already applied|apply\":1.7\nOR \"is|does current|one working|work\":1.6 NOT NEAR:3 (no|not|*n't)", "SpeakerList": "1", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Autopass", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "test", "Description": null, "ActionString": "[what|which+phone+do|are+you+have|use:1.3]", "UserEntry": "\"what|which phone do|are you have|use\":1.3", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T12:01:53", "ComponentCount": 9}