{"BucketId": "3798", "BucketFullname": "Categories.KA Emotions.Emotions Index Dishonesty", "BucketDescription": "Words and phrases that indicate customer is hesitant or suspects dishonesty", "UserEditable": false, "BucketName": "Emotions Index Dishonesty", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotions Index Dishonesty", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:34:21.763-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-02-28T09:22:40.853", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25452, "Name": "Evasive", "Description": null, "ActionString": "[jerk%+around:1.4]<runaround><evasiv%>$OR$$OR$", "UserEntry": "(evasiv*|runaround) OR \"jerk* around\":1.4", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25451, "Name": "Hesitation", "Description": null, "ActionString": "{decision+difficult%|hard:1}[trouble|problem|difficulty|time+deciding:1.2][can`t|not|cannot+decide:1]<or>$AND$[very|little|kind|kinda|sort|sorta|bit|pretty|super+weary|wary:1.2]<not><don`t>$OR$<hesitat%>$NOTAFTER:1$<why>[wait+minute:1]$BEFORE:1$[no|now|wait+wait+minute:1.2]<interested><cancel>[call+back:0.8]$OR$$OR$[never+mind:0.8]$NEAR:3$<nervous%><reluctant%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(reluctant*|nervous*) OR (\"never mind\":.8 NEAR:3 (\"call back\":.8 OR cancel|interested )) OR \"no|now|wait wait minute\":1.2 OR (\"wait minute\":1 BEFORE:1 why) OR hesitat* NOT AFTER:1 (don't|not) OR \"very|little|kind|kinda|sort|sorta|bit|pretty|super weary|wary\":1.2 OR OR \"can't|not|cannot decide\":1 OR \"trouble|problem|difficulty|time deciding\":1.2 \nOR [decision difficult*|hard]:1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25450, "Name": "Lacks Transparency", "Description": null, "ActionString": "[not|no|don`t|doesn`t|isnt`|wasn`t|weren`t|more+transparen%:1]!10-max", "UserEntry": "\"not|no|don't|doesn't|isnt'|wasn't|weren't |more transparen*\":1{10-MAX}", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25449, "Name": "Lying", "Description": null, "ActionString": "[screw%+over:1][bugging|bugs+me:1.2][losing+money|customer%|patience|confidence:1.2][lie|lying|lied+to+me|us:1.4]<dishonest%><deceiv%><decep%>$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(decep*|deceiv*|dishonest*) OR (\"lie|lying|lied to me|us\":1.4) OR \"losing money|customer*|patience|confidence\":1.2 OR \"bugging|bugs me\":1.2 OR \"screw* over\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25448, "Name": "skeptical", "Description": null, "ActionString": "{about|stop%+nonsense:1}<dollar%><channel%>$OR$[all+this|that+nonsens%:1][this+is+nonsense:1][some|same|absolute%|complete|total+nonsens%:1]$OR$$OR$$NOTNEAR:2$[fleec%+public|me|customer%|people:1]<paranoid><skeptical><suspicious%><leery><offended%><offensive><strange><terribly><scam%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[don`t+want|need+surprises:1.2][no+surprises:1][god|heaven+forbid:1.5][god|heaven+for+bid|bed:1.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"god|heaven for bid|bed\":1.5 OR \"god|heaven forbid\":1.5 OR \"no surprises\":1 OR \"don't want|need surprises\":1.2 OR (scam*|terribly|strange|offensive|offended*|leery|suspicious*|skeptical|paranoid) OR \"fleec* public|me|customer*|people\":1 OR ((\"some|same|absolute*|complete|total nonsens*\":1  OR \"this is nonsense\":1 OR \"all this|that nonsens*\":1) NOT NEAR:2 (channel*|dollar*))  OR [about|stop* nonsense]:1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25447, "Name": "Tell Trust", "Description": null, "ActionString": "[under+impression:1][can’t|won’t|don’t|not+trust:0.9]!90-max[you`re+telling+me:1.2][you+kept|keep+saying|tellling:1][told+me:1][i+was|wasn`t+told:1][they`re|were|couldn`t|kept+tell%:1.5]$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"they`re|were|couldn`t|kept tell*\":1.5 OR \"i was|wasn't told\":1 OR \"told me\":1 OR \"you kept|keep saying|tellling\":1 OR \"you're telling me\":1.2 OR \"can’t|won’t|don’t|not trust\":0.9{90-MAX} OR \"under impression\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-02-28T09:22:40.853", "ComponentCount": 6}