{"BucketId": "119", "BucketFullname": "Categories.Quality Automation_Calls.Customer Exclusions", "BucketDescription": "To identify where the customer is going away from the phone or conversation for at least 5 seconds.", "UserEditable": false, "BucketName": "Customer Exclusions", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Customer Exclusions", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2024-01-04T13:11:23.903-05:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32436, "Name": "Customer Exclusions", "Description": null, "ActionString": "~silence:+5~[hold+off|on|sec:1.2][bear+with+me:1.2][let+me+see+here:1.3][let+me+get+my:1.2][i`ll+try:1.2][i+will+try:1.2][hold+on:1.2][wait|hold+sec%|moment%|minute%|bit:1.2][try%+find|look|search|get:1.2][i`ll+be+back:1.2][one|just+second|moment|minute+please:1.6][be+right+back:1.2][a+moment|minute|second:1.2][just+a+minute%|second%|moment%:1.2][give+me+a|one+minute%|sec%|moment%:1.5][let+me+look|find|search|see|check:1.2][please+wait|hold:1.2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$BEFORE:3$", "UserEntry": "(\"please wait|hold\":1.2 \nOR \"let me look|find|search|see|check\":1.2 \nOR \"give me a|one minute*|sec*|moment*\":1.5\nOR \"just a minute*|second*|moment*\":1.2 OR \"a moment|minute|second\":1.2\nOR \"be right back\":1.2 OR \"one|just second|moment|minute please\":1.6\nOR \"i'll be back\":1.2 \nOR \"try* find|look|search|get\":1.2 \nOR \"wait|hold sec*|moment*|minute*|bit\":1.2  OR \"hold on\":1.2\nOR \"i will try\":1.2 OR \"i'll try\":1.2  \nOR \"let me get my\":1.2 OR \"let me see here\":1.3\nOR \"bear with me\":1.2 \nOR \"hold off|on|sec\":1.2)  BEFORE:3 SIL:[+5]", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32436, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32436, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-15T09:27:29.357", "ComponentCount": 1}