{"BucketId": "4273", "BucketFullname": "Categories.Test2a.TEST1", "BucketDescription": "test of dependencies for <PERSON><PERSON><PERSON>", "UserEditable": false, "BucketName": "TEST1", "SectionName": "Test2a", "SectionNameDisplay": "Test2a", "SectionId": 2332, "RetroStartDate": null, "CategoryDisplay": "Test2a.TEST1", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-28T14:38:07.087-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-28T14:37:55", "SlnInstallationID": null, "SemanticType": "EQL", "IsOutreach": false, "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -22011, "Name": "testSC1_updated", "Description": null, "ActionString": "<test>", "UserEntry": "test", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -22011, "BucketID": 90, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-28T14:38:08.007", "ComponentCount": 1}