{"BucketId": "10", "BucketFullname": "Categories.Customer Experience.Contextual Understandability", "BucketDescription": "To identify confusion on the call where the customer does not understand the agent.", "UserEditable": false, "BucketName": "Contextual Understandability", "SectionName": "Customer Experience", "SectionNameDisplay": "Customer Experience", "SectionId": 34, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Customer Experience.Contextual Understandability", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 4.0, "HasPendingRetroRequest": false, "Created": "2023-09-18T06:07:32.977-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 30, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32700, "Name": "Complicated", "Description": null, "ActionString": "[difficult+concept:1.5][hard+to+grasp:1.5][it|it`s|its|that%|this+appear%|extremely|kind%|pretty|really|seems|sound%|very|is+complicated|convoluted:2]$OR$$OR$", "UserEntry": "\"it|it's|its|that*|this appear*|extremely|kind*|pretty|really|seems|sound*|very|is complicated|convoluted\":2 OR \"hard to grasp\":1.5 OR \"difficult concept\":1.5", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32700, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32700, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32699, "Name": "Confusion", "Description": null, "ActionString": "<confusion>!-70%[can|it`s|is|its|that`s|was|what|what`s+confusing:1.3]!-70%[bit|completely|definitely|just|kind|kinda|little|pretty|quite|really|so|totally|very+baffled|baffling:1.5]!-70%[confused+as+to:1.5]!-70%[confusing|confuse%+about|because|sorry|you|me|everything|thing|part|which|when|why:2]!-70%[bit|completely|definitely|getting|got|gets|just|kind|kinda|little|more|pretty|quite|really|so|sounds|too|two|very|this|that+confuse%|confusing%:2]!-70%[i`m|i+confuse%:1.5]!-70%$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"i'm|i confuse*\":1.5 OR \"bit|completely|definitely|getting|got|gets|just|kind|kinda|little|more|pretty|quite|really|so|sounds|too|two|very|this|that confuse*|confusing*\":2 OR \"confusing|confuse* about|because|sorry|you|me|everything|thing|part|which|when|why\":2 OR \"confused as to\":1.5 OR \"bit|completely|definitely|just|kind|kinda|little|pretty|quite|really|so|totally|very baffled|baffling\":1.5 OR \"can|It's|is|its|that's|was|what|what's confusing\":1.3 OR confusion){-70%}", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32699, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32699, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32698, "Name": "Doesn`t Make Sense", "Description": null, "ActionString": "[that+makes+no+sense:2][didn`t|doesn`t|don`t|try|trying|wouldn`t|not|can`t|cant|cannot+make|making+sense%:2.5]$OR$", "UserEntry": "\"didn't|doesn't|don't|try|trying|wouldn't|not|can't|cant|cannot make|making sense*\":2.5 OR \"that makes no sense\":2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32698, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32698, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32697, "Name": "Explain", "Description": null, "ActionString": "[there+was+not|no+explanation%|explain%:3][explan%+not|wasn`t+given|provide%|shared|said|told:2][i|i`m+misunderstood|misunderstand%:2.5][would+you+explain+to+me:2][can|could|will|would+you+explain:2][if+you+can|could|will|would+explain:2]<or>$AND$[can|could|will|would+you+explain+again|everything|how|it|once|one|more|that|this|what|why:2][can`t|cant|cannot|couldn`t+explain+to+me:2][can|could+not+explain+to+me:2][would+you+be+able+explain:2][go+over+it|this|that+again:2.5][explain|tell+once+more:2][repeat|tell+one+more+time:2][explain|tell+again:1.5][apologize|apologies|please|sorry+explain|give|tell+again:2][explain+best|better:1.5][best|better+able+explain:2][explain+over|you+again:2.5][sorry+explain+again:2][try|trying|tried+explain|explaining:2][wasn`t+able+explain:2][can|could+not+explain:2][can`t|cant|cannot|couldn`t+explain:2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"can't|cant|cannot|couldn't explain\":2 OR \"can|could not explain\":2 OR \"wasn't able explain\":2 OR \"try|trying|tried explain|explaining\":2 OR \"sorry explain again\":2 OR \"explain over|you again\":2.5 OR \"best|better able explain\":2 OR \"explain best|better\":1.5 OR \"apologize|apologies|please|sorry explain|give|tell again\":2 OR \"explain|tell again\":1.5 OR \"repeat|tell one more time\":2 OR \"explain|tell once more\":2 OR \"go over it|this|that again\":2.5 OR \"would you be able explain\":2 OR \"can|could not explain to me\":2 OR \"can't|cant|cannot|couldn't explain to me\":2 OR  \"can|could|will|would you explain again|everything|how|it|once|one|more|that|this|what|why\":2 OR OR \"if you can|could|will|would explain\":2 OR \"can|could|will|would you explain\":2 OR \"would you explain to me\":2 OR \"i|i'm misunderstood|misunderstand*\":2.5 OR \"explan* not|wasn't given|provide*|shared|said|told\":2 OR \"there was not|no explanation*|explain*\":3", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32697, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32697, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32696, "Name": "Not Following/Understanding", "Description": null, "ActionString": "<hearing><hear>$OR$[%n`t|not+understand+word+said:1.7][sorry+didn`t|not|can`t+understand:1.5][cannot|couldn`t|can`t|cant|didn`t|difficult%|doesn`t|don`t|impossible|trouble+understand%+you:1.5][not+quite|really|sure+understanding:1.5][am|could|can|did|do|does|i`m|really|still|she|she`s|he|he`s|we|we`re|they|they`re+not+understand%:1.5][unable|difficult|hard+follow|understand:2][not+following+you:2][cannot|cant|can`t|not+really+follow|following:2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTNEAR:2$", "UserEntry": "(\"cannot|cant|can't|not really follow|following\":2 OR \"not following you\":2 OR \"unable|difficult|hard follow|understand\":2 OR \"am|could|can|did|do|does|i'm|really|still|she|she's|he|he's|we|we're|they|they're not understand*\":1.5 OR \"not quite|really|sure understanding\":1.5 OR \"cannot|couldn't|can't|cant|cannot|didn't|difficult*|doesn't|don't|impossible|trouble understand* you\":1.5 OR \"sorry didn't|not|can't understand\":1.5 OR \"*n't|not understand word said\":1.7) NOT NEAR:2 (hear|hearing)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32696, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32696, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32695, "Name": "Repeat", "Description": null, "ActionString": "[would+you+explain:2][can+you+repeat|recap:2][do|did+you+need|want+me+repeat:2]$OR$$OR$", "UserEntry": "\"do|did you need|want me repeat\":2 OR \"can you repeat|recap\":2 OR \"would you explain\":2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32695, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32695, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32694, "Name": "Unclear", "Description": null, "ActionString": "[don`t|not|wouldn`t+know+what+mean%:2.2][not+sure+what+mean%:2.2][what+does+that|this|it+mean:2][what+do+they|you+mean:2][it+is:0.5][can`t|cant|cannot|couldn`t|don`t|doesn`t|didn`t|not|wasn`t+clear|follow|following|seeing|see|sure+what%:1.5]$NOTNEAR:1$[no+idea+what%+going+on:2][no+idea+what%+he`s|she`s|they|they`re|you|your|you`re+asking|doing|mean|meant|referring|saying|talking:2][it`s|that`s|very|really+not|un|um+clear:1][you|you`re+not+very+clear:2][you`re|your+not+being+clear:2][unclear+to+me:1.2][extremely|really|still|very+unclear:1.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"extremely|really|still|very unclear\":1.5 OR \"unclear to me\":1.2 OR \"you're|your not being clear\":2 OR \"you|you're not very clear\":2 OR \"it's|that's|very|really not|un|um clear\":1 OR \"no idea what* he's|she's|they|they're|you|your|you're asking|doing|mean|meant|referring|saying|talking\":2 OR \"no idea what* going on\":2 OR \"can't|cant|cannot|couldn't|don't|doesn't|didn't|not|wasn't clear|follow|following|seeing|see|sure what*\":1.5 NOT NEAR:1 (\"it is\") OR \"what do they|you mean\":2 OR \"what does that|this|it mean\":2 OR \"not sure what mean*\":2.2 OR \"don't|not|wouldn't know what mean*\":2.2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32694, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32694, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-15T09:36:42.78", "ComponentCount": 7}