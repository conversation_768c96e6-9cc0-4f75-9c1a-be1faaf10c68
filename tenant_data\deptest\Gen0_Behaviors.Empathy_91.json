{"BucketId": "91", "BucketFullname": "Categories.Behaviors.Empathy", "BucketDescription": null, "UserEditable": false, "BucketName": "Empathy", "SectionName": "Behaviors", "SectionNameDisplay": "Behaviors", "SectionId": 42, "RetroStartDate": null, "CategoryDisplay": "Behaviors.Empathy", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": null, "Creator": null, "Version": null, "LanguageTag": "Unknown", "EffectiveDate": "2015-10-14T13:33:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "CDE\\courtney.lawton", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -30449, "Name": "I apologize", "Description": null, "ActionString": "[i+apologize+i|we|i`ll|we`ll|i`m|we`re+can`t|don`t|didn`t|do|get|gonna|going|not|trying|was|will:1.1][apologize%+concern%|confusion|delay|error|hold|inconvenience|moment|pronouncing|typo|reason|response|wait:1.5][apologize+call+you+bad|that|work:1.5][apologize+advance:1][again+i+apologiz%:0.8][i+apologize+let|give+me:1.2][i+absolutely|certainly|definitely|do|have|really|truly|totally+apologize:1][i+apologize+about|again|but|cause|for|have|however|i|if|i`m|it|it`s|it`ll|ma`am|sir|so|sorry|that|this|thought|unfortunately:0.8]$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"i apologize about|again|but|cause|for|have|however|i|if|I'm|it|it's|it'll|ma'am|sir|so|sorry|that|this|thought|unfortunately\" OR \"I absolutely|certainly|definitely|do|have|really|truly|totally apologize\":1 OR \"I apologize let|give me\":1.2 OR \"again I apologiz*\" OR \"apologize advance\":1 OR \"apologize call you bad|that|work\":1.5 OR \"apologize* concern*|confusion|delay|error|hold|inconvenience|moment|pronouncing|typo|reason|response|wait\":1.5 OR \"i apologize i|we|i'll|we'll|i'm|we're can't|don't|didn't|do|get|gonna|going|not|trying|was|will\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30448, "Name": "Frustrated apologize", "Description": null, "ActionString": "[angry|annoy%|frustrat%|irritat%|unhappy+i|i`m|so+sorry|apologiz%:3][apologiz%|sorry+angry|annoy%|frustrat%|irritat%|unhappy:3]$OR$", "UserEntry": "(\"apologiz*|sorry angry|annoy*|frustrat*|irritat*|unhappy\":3 OR \"angry|annoy*|frustrat*|irritat*|unhappy i|I'm|so sorry|apologiz*\":3)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30447, "Name": "I`m sorry", "Description": null, "ActionString": "<not>[i`m|am+awfully|definitely|incredibly|so|really|terribly|very+sorry:1.2]$NOTNEAR:0$[am|i`m+sorry:0.6]$OR$", "UserEntry": "(\"am|i'm sorry\":0.6) OR (\"I'm|am awfully|definitely|incredibly|so|really|terribly|very sorry\":1.2) NOT NEAR:0 (not)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30446, "Name": "I can imagine", "Description": null, "ActionString": "[i+can|cant|cannot|can`t|could|couldn`t|not+imagine+being|how|that|that`s:2]", "UserEntry": "(\"i can|cant|cannot|can't|could|couldn't|not imagine being|how|that|that's\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30445, "Name": "I understand your concern/hesitation", "Description": null, "ActionString": "[understand|understands|understanding+concern|concerns|concerned|hesitant|hesitation:2]", "UserEntry": "(\"understand|understands|understanding concern|concerns|concerned|hesitant|hesitation\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30444, "Name": "Sorry for the confusion/delay", "Description": null, "ActionString": "<no><not><i`m>$OR$$OR$[sorry+confusion|confusing|confuse%|convenience|discrepanc%|inconvenience|misinformation|problem%|mistake%|mess|issue%|error%|trouble%:1.2]$NOTNEAR:0$", "UserEntry": "(\"sorry confusion|confusing|confuse*|convenience|discrepanc*|inconvenience|misinformation|problem*|mistake*|mess|issue*|error*|trouble*\":1.2) NOT NEAR:0 (i'm|not|no)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30443, "Name": "Understand you`re upset", "Description": null, "ActionString": "[understandably+so:0.7][absolutely|certainly|completely|definitely|perfectly|that|that`s+understandable:1.5][understand+your|you|you`re+anger|concern%|feel%|irritat%|ration:1.5][understand+your|you|you`re+angry|annoy%|frustrat%|irritat%|ticked|unhappy|upset:1]$OR$$OR$$OR$", "UserEntry": "(\"understand your|you|you're angry|annoy*|frustrat*|irritat*|ticked|unhappy|upset\":1 OR \"understand your|you|you're anger|concern*|feel*|irritat*|ration\":1.5 OR \"absolutely|certainly|completely|definitely|perfectly|that|that's understandable\":1.5 OR \"understandably so\":0.7) ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30442, "Name": "Must be difficult", "Description": null, "ActionString": "[must+be+annoying|difficult|frustrating|irritating|painful:1.2]", "UserEntry": "(\"must be annoying|difficult|frustrating|irritating|painful\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30441, "Name": "Unfortunately at this time", "Description": null, "ActionString": "<if>[unfortunately+are|am|can|could|did|do|does|has|have|he`s|is|ma`am|she`s|sir|that`s|there`s|was|will|would|i`m|we`re|they`re|your|you`re+not|no:1.2][unfortunately+have+to:1][unfortunately+order+to|for:1][unfortunately+at+this+time|point:1.5]$OR$$OR$$OR$$NOTNEAR:0$", "UserEntry": "(\"unfortunately at this time|point\":1.5 OR \"unfortunately order to|for\":1 OR  \"unfortunately have to\":1 OR \"unfortunately are|am|can|could|did|do|does|has|have|he's|is|ma'am|she's|sir|that's|there's|was|will|would|I'm|we're|they're|your|you're not|no\":1.2) NOT NEAR:0 (if)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30440, "Name": "Time is valuable", "Description": null, "ActionString": "[time%+valuable:0.8]", "UserEntry": "(\"time* valuable\":0.8)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30439, "Name": "I sympathize", "Description": null, "ActionString": "[my+sympathies:0.8][i+sympathize:0.8]$OR$", "UserEntry": "(\"i sympathize\":0.8 OR \"my sympathies\":0.8)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30438, "Name": "I understand situation", "Description": null, "ActionString": "<hear><hear>$OR$[sorry+about|that+apologize:1.5][sorry+you+feel+that+way:1.2][sorry+that|this+happened:1]$OR$$OR$$NOTNEAR:0$<haven`t><not><hardly><couldn`t><can`t><didn`t><barely>$OR$$OR$$OR$$OR$$OR$$OR$[sorry+to+hear|here+that:1]$NOTNEAR:0$$OR$", "UserEntry": "(\"sorry to hear|here that\":1) NOT NEAR:0 (barely|didn't|can't|couldn't|hardly|not|haven't) OR (\"sorry that|this happened\":1 OR \"sorry you feel that way\":1.2 OR \"sorry about|that apologize\":1.5) NOT NEAR:0 (hear|hear)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30437, "Name": "I wish I could", "Description": null, "ActionString": "[i+wish+i|we+could+do+something:1.2][i+wish+i|we+could+help|give|tell+you:1.2][i+wish+i|we+could+but|help:1][i+wish+i|we+could+be|do|help|give+more:1.2]$OR$$OR$$OR$", "UserEntry": "(\"i wish i|we could be|do|help|give more\":1.2 OR \"i wish i|we could but|help\":1 OR \"i wish i|we could help|give|tell you\":1.2 OR \"i wish i|we could do something\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30436, "Name": "Make sure I understand", "Description": null, "ActionString": "[be|make+sure+i+understand%|understood:0.9][make|be+sure+that+i+understand%:1.2][make|be+sure+i`m+understanding:1.5]$OR$$OR$<you`re><your><you>$OR$$OR$[make|be+sure+i|i`m+clear+understand%:1.5]$NOTNEAR:0$$OR$", "UserEntry": "(\"make|be sure i|i`m clear understand*\":1.5) NOT NEAR:0 (you|your|you`re) OR (\"make|be sure I`m understanding\":1.5 OR \"make|be sure that I understand*\":1.2 OR \"be|make sure i understand*|understood\":0.9)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30435, "Name": "Sorry that happened", "Description": null, "ActionString": "<hear><hear>$OR$[sorry+about|that+apologize:1.5][sorry+you+feel+that+way:1.2][sorry+that|this+happened:1]$OR$$OR$$NOTNEAR:0$<haven`t><not><hardly><couldn`t><can`t><didn`t><barely>$OR$$OR$$OR$$OR$$OR$$OR$[sorry+to+hear|here+that:1]$NOTNEAR:0$$OR$", "UserEntry": "(\"sorry to hear|here that\":1) NOT NEAR:0 (barely|didn't|can't|couldn't|hardly|not|haven't) OR (\"sorry that|this happened\":1 OR \"sorry you feel that way\":1.2 OR \"sorry about|that apologize\":1.5) NOT NEAR:0 (hear|hear)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30434, "Name": "I understand", "Description": null, "ActionString": "<not><didn`t><don`t>$OR$$OR$[i+understand+i+just|know|can|understand:1][i+absolutely|certainly|completely|definitely|fully|totally|perfectly|honestly|exactly+understand:1.2]$OR$$NOTNEAR:0$[absolutely|certainly|completely|definitely|fully|totally|perfectly|honestly|exactly+i+understand:1.2][i+do+understand:0.6][i+understand+hundred+percent:1.5][i+understand+let+me:1]$OR$$OR$$OR$$OR$", "UserEntry": "(\"i understand let me\":1 OR \"i understand hundred percent\":1.5 OR \"i do understand\":0.6 OR \"absolutely|certainly|completely|definitely|fully|totally|perfectly|honestly|exactly I understand\":1.2) OR (\"I absolutely|certainly|completely|definitely|fully|totally|perfectly|honestly|exactly understand\":1.2 OR \"i understand i just|know|can|understand\":1) NOT NEAR:0 (don't|didn't|not)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30433, "Name": "Unfortunately not able", "Description": null, "ActionString": "[i|am|i`m|im+sorry+not|wasn`t|wasnt+able|doing|sure|area|expertise|knowing|understand%|understood%:1.5][unfortunately+according|appears|aren`t|because|before|but|cannot|cant|can`t|cause|didn`t|doesn`t|don`t|depends|gonna|going|need|not|seems|since|still|wish|all|at|as|for|hasn`t|hadn`t|haven`t|isn`t|wasn`t|won`t|wouldn`t:1][unfortunately+i|i`m|we|we`re+unable:1.5][unfortunately+i|i`m|we|we`re+not+able:1.5]$OR$$OR$$OR$", "UserEntry": "(\"unfortunately i|i'm|we|we're not able\":1.5 OR \"unfortunately i|i'm|we|we're unable\":1.5 OR \"unfortunately according|appears|aren't|because|before|but|cannot|cant|cannot|can't|cause|didn't|doesn't|don't|depends|gonna|going|need|not|seems|since|still|wish|all|at|as|but|for|hasn't|hadn't|haven't|isn't|wasn't|won't|wouldn't\":1 OR \"i|am|i`m|im sorry not|wasn't|wasnt able|doing|sure|area|expertise|knowing|understand*|understood*\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2015-10-14T13:33:00", "ComponentCount": 17}