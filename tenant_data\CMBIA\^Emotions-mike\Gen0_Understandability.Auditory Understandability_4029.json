{"BucketId": "4029", "BucketFullname": "Categories.Understandability.Auditory Understandability", "BucketDescription": "Language indicating trouble hearing due to telecom quality or equipment.", "UserEditable": false, "BucketName": "Auditory Understandability", "SectionName": "Understandability", "SectionNameDisplay": "Understandability", "SectionId": 2281, "RetroStartDate": null, "CategoryDisplay": "Understandability.Auditory Understandability", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2023-07-17T12:56:44.697-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "en", "EffectiveDate": "2023-07-17T13:00:26.027", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -23158, "Name": "Bad connection", "Description": "Understandability Issues", "ActionString": "[have|really|very|you+bad|poor|terrible|awful+connection%|connecting:2]", "UserEntry": "(\"have|really|very|you bad|poor|terrible|awful connection*|connecting\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23157, "Name": "Can you hear me", "Description": "Understandability Issues", "ActionString": "[if+you+can|cant|can`t|cannot|unable+hear+me:1.5][you+able+hear+me:1.5][can|could|did|do+you+hear|here+me:1.5]$OR$$OR$", "UserEntry": "(\"can|could|did|do you hear|here me\":1.5 OR \"you able hear me\":1.5 OR \"if you can|cant|can't|cannot|unable hear me\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23156, "Name": "Trouble hearing", "Description": "Understandability Issues", "ActionString": "[hard|difficult+to+hear:1.5][hard+hearing:1.2][barely|hardly+hear|heard:1.5][difficult%|issue%|problem%|trouble%+hearing:2]$OR$$OR$$OR$", "UserEntry": "(\"difficult*|issue*|problem*|trouble* hearing\":2 OR \"barely|hardly hear|heard\":1.5 OR \"hard hearing\":1.2 OR \"hard|difficult to hear\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23155, "Name": "Call is breaking up", "Description": "Call is breaking up", "ActionString": "[you+are|really|keep+breaking+up:1.5][apologize|apologise|apologies|call%|mobile|cell%|conversation|connection%|kind|kinda|phone%|sorry|telephone%|voice%|your|you`re|are|you+broke|break|breaking+up:1.5][broke|breaking+up+again|bit|little|middle:1.5]$OR$$OR$", "UserEntry": "(\"broke|breaking up again|bit|little|middle\":1.5 OR \"apologize|apologise|apologies|call*|mobile|cell*|conversation|connection*|kind|kinda|phone*|sorry|telephone*|voice*|your|you're|are|you broke|break|breaking up\":1.5 OR \"you are|really|keep breaking up\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23154, "Name": "Can`t hear you", "Description": "Can`t hear you", "ActionString": "<wanna><want><from><back>$OR$$OR$$OR$[not|wasn`t+able+hear+you:1.5][i+don`t|can`t+hear+you:1.2][cant|can`t|cannot|couldn`t|didn`t|not|unable|weren`t+hear+you|what|said:1.5]$OR$$OR$$NOTNEAR:0$", "UserEntry": "((\"cant|can't|cannot|couldn't|didn't|not|unable|weren't hear you|what|said\":1.5 OR \"i don't|can't hear you\":1.2 OR \"not|wasn't able hear you\":1.5) NOT NEAR:0 (back|from|want|wanna))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23153, "Name": "You`re cutting out", "Description": "You`re cutting out", "ActionString": "[cut%|drop%+in+and+out:1.5][cut%+out+again:1.4][apologiz%|apologis%|call|calls|cell%|connection%|conversation|constantly|keep%|phone%|sorry|telephone%|voice%|were|we`re|your|you`re+cutting+out:1.5]$OR$$OR$", "UserEntry": "(\"apologiz*|apologis*|call|calls|cell*|connection*|conversation|constantly|keep*|phone*|sorry|telephone*|voice*|were|we're|your|you're cutting out\":1.5 OR \"cut* out again\":1.4 OR \"cut*|drop* in and out\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23152, "Name": "Anyone there", "Description": "Anyone there", "ActionString": "[anyone|someone+there|their|line+hello:1.5][hi|hello|hey+anyone|someone+in|on|the+line%:1.5][hello|hi|hey+anyone|someone+there|their:1.5]$OR$$OR$", "UserEntry": "(\"hello|hi|hey anyone|someone there|their\":1.5 OR \"hi|hello|hey anyone|someone in|on|the line*\":1.5 OR \"anyone|someone there|their|line hello\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23151, "Name": "Headset issue", "Description": "", "ActionString": "[headset+breaking|broken|crappy|faulty|sucks|rubbish:1.5][headset+going|isn`t|not|quit|stopped+bad|good|great|right|working:1.5][having+difficult%|issue%|problem%|trouble%+headset%:1.5]$OR$$OR$", "UserEntry": "(\"having difficult*|issue*|problem*|trouble* headset*\":1.5 OR \"headset going|isn't|not|quit|stopped bad|good|great|right|working\":1.5 OR \"headset breaking|broken|crappy|faulty|sucks|rubbish\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23150, "Name": "Interference", "Description": "", "ActionString": "[hear%|lot|lots|there%|sound%+background%+noise%:1.5][getting|hear%|sound%+bad|lot|lots|some+echo|static:1.5][echo|interference|static+on+the+call|line%|phone|telephone:1.5]$OR$$OR$", "UserEntry": "(\"echo|interference|static on the call|line*|phone|telephone\":1.5 OR \"getting|hear*|sound* bad|lot|lots|some echo|static\":1.5 OR \"hear*|lot|lots|there*|sound* background* noise*\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23149, "Name": "Can`t make out", "Description": "", "ActionString": "[can`t|cant|cannot|unable|couldn`t+make+out+said|saying:1.5]", "UserEntry": "(\"can't|cant|cannot|unable|couldn't make out said|saying\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23148, "Name": "What was that", "Description": "", "ActionString": "<again>[could+you+repeat+that:1.5][sorry+what+was+that:1.5]$AND$[didn`t|don`t+know+what+was+that:1.5]$OR$$NOTBEFORE:0.8$", "UserEntry": "((\"didn't|don't know what was that\":1.5 OR \"sorry what was that\":1.5 \"could you repeat that\":1.5) NOT BEFORE:0.8 (again))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23147, "Name": "Hard time hearing", "Description": "", "ActionString": "[hardly+hear:1][hard+of+hearing:1.2]$AND$[hard+time+hearing:1.2]$OR$", "UserEntry": "(\"hard time hearing\":1.2 OR \"hard of hearing\":1.2 \"hardly hear\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23146, "Name": "Muffled", "Description": "", "ActionString": "[bit|kinda|little|real%|very|extremely|voice|sound%+muffl%:1.2]", "UserEntry": "(\"bit|kinda|little|real*|very|extremely|voice|sound* muffl*\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-07-17T13:00:26.027", "ComponentCount": 13}