
'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''
import FEAPI_Helpers
import json
import os,sys
import pandas as pd
from pathlib import Path
from datetime import datetime
import configparser



class config:



    # Default values
    '''
    [FEAPI]
    '''
    API = "feapi"
    DataRoot = "tenant_data"
    LogRoot = "logs"
    DEBUG = False
    LoggerLevel = "DEBUG"

    '''
    [ImportAlert]
    '''
    Tenant = ""
    SourceFolderName = ""
    TargetAlertFolder = ""
    TargetNamePrefix = "Imported."
    RemoveFilters = False
    OverwriteExisting = False

    API_timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    json_output_dir = ""
    @staticmethod
    def FilenameInOutputFolder(filename):
        return os.path.join(config.json_output_dir, filename)

    @staticmethod
    def AllCategories_xls_filename():
        return config.FilenameInOutputFolder (  f"AllCategories.xlsx")

    @staticmethod
    def PyFilename():
        os.path.basename(sys.argv[0])

    @staticmethod
    def SetFolderName(foldername):
        config.SourceFolderName = f"{config.DataRoot}/{foldername}"

    @staticmethod
    def read_ini_file(file_path:str=""):
        if file_path=="":
            file_path = f"{os.path.basename(sys.argv[0])}.INI"
        if os.path.exists(file_path):
            config.strip_lines_with_triple_single_quotes(file_path,file_path)
            configparse = configparser.ConfigParser()
            configparse.read(file_path)

            config.DEBUG = configparse.get('FEAPI', 'DEBUG').strip('"')  == 1
            config.LoggerLevel = configparse.get('FEAPI', 'LoggerLevel').strip('"')
            config.API = configparse.get('FEAPI', 'API').strip('"')
            config.Tenant = configparse.get('FEAPI', 'Tenant').strip('"')
            config.LogRoot = configparse.get('FEAPI', 'LogRoot').strip('"')
            config.DataRoot = configparse.get('FEAPI', 'DataRoot').strip('"')
        else:
            print(f"ERROR: INI file at {file_path} does not exist **************************")

    @staticmethod
    def strip_lines_with_triple_single_quotes(input_file: str, output_file: str):
        with open(input_file, 'r') as file:
            lines = file.readlines()
            # Filter out lines that are just ''' (including any whitespace)
        cleaned_lines = [line for line in lines if line.strip() != "'''"]
        # Write the cleaned lines to the output file
        with open(output_file, 'w') as file:
            file.writelines(cleaned_lines)

config.read_ini_file()



# LOGGING --------------------------------------------------------------------------------------
import logging
# Set up logging configuration
logger = logging.getLogger(config.PyFilename())
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

#reset logger level if passed in args
level_string = config.LoggerLevel.upper()  # Ensure case-insensitivity
level = logging.getLevelName(level_string)
if isinstance(level, int):  # Check if a valid level was found
    logging.getLogger().setLevel(level)
    print(f"Logging level set to: {logging.getLevelName(level)}")
else:
    print(f"Invalid logging level string passed as config: {level_string}")

# Create handlers and log folders
if not os.path.exists(config.LogRoot):
    os.makedirs(config.LogRoot)

logprefix = f"{config.LogRoot}/{config.PyFilename()}-{config.timestamp}"
file_handler = logging.FileHandler(f'{logprefix}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'{logprefix}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'{logprefix}.warnings.txt')  # Log to errors.txt

console_handler = logging.StreamHandler()  # Log to console


# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)



#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, config)



#API info
# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# for FISUS, use feapif.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token variable


def PrepAlertJson(config, alertjson, alertname, alertFolderID):
    # Update the JSON so it cretes a new bucekt or updates an existing bucket
    current_date_time = config.API_timestamp

    alertjson["Name"] = alertname
    alertjson.pop('Id',None)
    alertjson["FolderID"] = alertFolderID
    # Remove leftover fields
    alertjson.pop('LastUpdated')
    alertjson.pop('LastUser')
    alertjson["MessageTypeID"] = alertjson["MessageType"]["MessageTypeID"]


    # Remove SC and Bin ID's
    for component in alertjson["SearchComponents"]:
        component["Id"] = None
        if config.RemoveFilters == True:
            component["Filters"] = []


    return alertjson



#-----------------------------------------------------------------------------------------------

def CreateAlert(config, json_file, alertfolderid, df_AllAlerts):
    # Load JSON data from the file
    alertjson = helper.load_json_from_file(json_file)
    if alertjson is not None:
        logger.info(f"Loaded JSON data for: {json_file}")
        # logger.info(json.dumps(data, indent=4))

    alertname = config.TargetNamePrefix + alertjson["Name"]
    alertfullname = f"{config.TargetAlertFolder}/{alertname}"
    alertjson = PrepAlertJson(config, alertjson, alertname, alertfolderid)
    currentalertid = None

    # Check to see if alertfullname already exists, if so overwrite it
    if alertfullname in df_AllAlerts['AlertFullname'].values:
        if config.OverwriteExisting == False:
            logger.error(f"Alert ({alertname}) already exists and config.OverwriteExisting = false.  Exiting...")
            exit(1)

        #Overwrite current alert
        currentalertid = df_AllAlerts.loc[df_AllAlerts['AlertFullname'] == alertfullname, 'AlertID'].iloc[0]


    # Dump the importable Category JSON
    # Write JSON object to a file
    json_for_import_filename = os.path.join(config.json_output_dir,
                                            f"{helper.cleanfilename(alertname)}.json")
    with open(f"{json_for_import_filename}", 'w') as json_file_import:
        try:
            json.dump(alertjson, json_file_import, indent=4)  # `indent=4` for pretty-printing
        except Exception as e:
            logger.error(f"Unable to save Import_json ({alertjson}) as ({json_for_import_filename}). Excpetion = {e}")

    if currentalertid != None:
        # Couldn't find score, create new one
        if config.DEBUG==False:
            logger.info(
                f"OVERWRITING OldAlertName: {alertfullname}, AlertID = {currentalertid} ----------------------")
            alertid = helper.API_UpdateAlert(alertname, currentalertid, alertjson)
            if alertid == None:
                logger.error(
                    f"FAILED OVERWRITING OldAlertName: {alertfullname}, AlertID = {currentalertid} ----------------------")


    else:
        if config.DEBUG == False:
            logger.info(f"CREATING NewAlertName: {alertfullname} ----------------------")
            alertid = helper.API_CreateAlert(alertname, alertjson)
            if alertid == None:
                logger.error(
                    f"FAILED CREATING NewAlertName: {alertfullname} ----------------------")

    return alertid








# ----------------------------------------------------------------------------------------------------
def CreateAlerts():



    # Create a Path object for the Imported JSON folder
    folder_path = Path(config.SourceFolderName)
    json_files = folder_path.glob('*.json')
    json_files = list(json_files)
    logger.info (f"Found ({len(json_files)}) json files in {folder_path}...")

    helper.GetFilterDependencyTables(config.json_output_dir)

    # Get current list of alerts
    json_allAlerts = helper.API_GetAllAlertsJSON()
    #Add FolderNames to Alerts JSON
    for a in json_allAlerts:
        folderid = a["FolderID"]
        a["AlertFullname"] = f"{helper.GetFoldernameFromID(folderid,helper.df_folders)}/{a['Name']}"

    df_allAlerts = pd.json_normalize(json_allAlerts)


    # Create new Alert Folder if needed
    alertfolderid = helper.CreateFolderChain(config.TargetAlertFolder)


    # Iterate through the generator of json files
    i=1
    for json_file in json_files:
        logger.info(f"Processing ( {i} / {len(json_files)} ) file = {json_file} -------------------------------------------------")
        i = i+1

        alertid = CreateAlert(config, json_file, alertfolderid, df_allAlerts)


#---------------------------------------------------------------------------------------

import argparse

def str_to_bool(s):
    if s.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif s.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')

def main():

    # get cmdline args and overwrite defaults from INI
    config.read_ini_file()

    parser = argparse.ArgumentParser(description='Script to process folder and section names')
    parser.add_argument('--API', nargs='?', type=str, default=config.API, help='API (feapi/feapif) (optional)')
    parser.add_argument('--Tenant', type=str,  required=True,help='Name of the tenant to double-check before importing')
    parser.add_argument('--SourceFolderName', type=str, required=True, help='Name of the folder which contains JSON files to import')
    parser.add_argument('--TargetAlertFolder', nargs='?',  required=True,type=str, default="", help='Name of the folder in which the alerts should be created')
    parser.add_argument('--TargetNamePrefix', nargs='?', required=True, type=str, default="",
                        help='PRefix to add to all alerts created')

    parser.add_argument('--RemoveFilters',  type=str_to_bool, default=config.RemoveFilters, help='RemoveFilters=True if you want to remove all filters from a score')
    parser.add_argument('--OverwriteExisting', type=str_to_bool, default=config.OverwriteExisting,
                        help='OverwriteExisting=True if you want to overwrite a alert that already exists with this name')

    args = parser.parse_args()
    logger.info(f"ARGUMENTS PASSED:-------------------------------------------")
    logger.info("\n".join(FEAPI_Helpers.FEAPI_Helper.show_all_attributes_and_values(args)))

    # Loop through attributes of A and copy from B if they match
    FEAPI_Helpers.FEAPI_Helper.UpdateObjectA_WithB(config, args)

    # Global Helper functions ------------------------------------------------------------------------------
    global helper
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, config)

    # Check if the tenant matches
    logger.info(f"Checking if API tenant matches target Config.Tenant ({config.Tenant})")
    config.tenant = helper.API_CheckTenantName(config.Tenant)

    # Check if FolderName exists
    config.SetFolderName(config.SourceFolderName)
    if os.path.exists(config.SourceFolderName) and os.path.isdir(config.SourceFolderName):
        logger.info(f"Found input folder ({config.SourceFolderName})")
        # Create a directory for JSON files
        config.json_output_dir = f"{config.SourceFolderName}/JSON_for_import_into_{config.Tenant}_at_{config.timestamp}"

        # Create a directory for JSON files
        logger.info(f"Creating JSON import files in {config.json_output_dir}")
        os.makedirs(config.json_output_dir, exist_ok=True)

    else:
        logger.error(f"FolderName ({config.SourceFolderName}) does not exist. Exiting...")
        exit(1)

    CreateAlerts()


if __name__ == "__main__":
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")



