{"BucketId": "301", "BucketFullname": "Categories.C_Recording_3.PBI_Recording_3", "BucketDescription": "calls that hit at least 1 of the 7 points", "UserEditable": false, "BucketName": "PBI_Recording_3", "SectionName": "C_Recording_3", "SectionNameDisplay": "C_Recording_3", "SectionId": 92, "RetroStartDate": "2024-10-15T00:00:00", "CategoryDisplay": "C_Recording_3.PBI_Recording_3", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-29T08:54:22.093-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-15T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31079, "Name": "Recording 3 - 1", "Description": null, "ActionString": "(recording 3.recording 3 1)", "UserEntry": "CAT:[Recording 3.Recording 3 1]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31078, "Name": "Recording 3 - 2", "Description": null, "ActionString": "(C_Recording_3.Recording 3 - 2)", "UserEntry": "CAT:[C_Recording_3.Recording 3 - 2]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31077, "Name": "Recording 3 - 3", "Description": null, "ActionString": "(C_Recording_3.Recording 3 - 3)", "UserEntry": "CAT:[C_Recording_3.Recording 3 - 3]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31076, "Name": "Recording 3 - 4", "Description": null, "ActionString": "(C_Recording_3.Recording 3 - 4)", "UserEntry": "CAT:[C_Recording_3.Recording 3 - 4]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31075, "Name": "Recording 3 - 5", "Description": null, "ActionString": "(C_Recording_3.Recording 3 - 5)", "UserEntry": "CAT:[C_Recording_3.Recording 3 - 5]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31074, "Name": "Recording 3 - 6", "Description": null, "ActionString": "(C_Recording_3.Recording 3 - 6)", "UserEntry": "CAT:[C_Recording_3.Recording 3 - 6]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31073, "Name": "Recording 3 - 7", "Description": null, "ActionString": "(C_Recording_3.Recording 3 - 7)", "UserEntry": "CAT:[C_Recording_3.Recording 3 - 7]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-06T13:04:20.217", "ComponentCount": 7}