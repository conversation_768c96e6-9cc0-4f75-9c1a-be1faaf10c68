{"BucketId": "129", "BucketFullname": "Categories.Quality Automation_Calls.Solution Provided", "BucketDescription": "Calls where agents offers a solution to customer.", "UserEditable": false, "BucketName": "Solution Provided", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-07-03T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Solution Provided", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2024-01-18T10:25:42.387-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-07-03T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32406, "Name": "Solution", "Description": null, "ActionString": "<%n`t><%not>$OR$<kit><box>$OR$[will|am|gonna|going+send%|receive|receiving|process%+refund:1.3]$NOTNEAR:3$$NOTAFTER:3$[you%+already|ready+return%+sens%|cent%|product%|it:2][get+touch+supplier%|provider|healthcare:2][contact%|call|talk+your|the+supplier%|provider|healthcare:2]$OR$[warranty+line:1.4]$NEAR:7$<disregard><reminder><automated><letter%><return%>$OR$$OR$$OR$$OR$[%n`t+worry+about:1.7]$NEAR:5$[i|will|i`ll|i`m|am+leave|leaving|write|writing|make+note%:2]{%regard|ignor%+mail%|letter%|automated|reminder:2}[give+call:1.2]!-30%[call+back:1]!-30%<callback%>!-30%$OR$$OR$[restart%|%install+app%|phone:1.3]!-30%[wait+minute%|redacted:1.6]!-30%$OR$$NEAR:3$(quality automation_calls.shipping method)[working|within|business+day%:1.5]=1!-50%<dispatch%>=1!-50%<placement>=1!-50%<replace%>=1!-50%<prepare>=1!-50%<confirm%>=1!-50%$OR$$OR$$OR$$OR$<postcode>=1!-50%[delivery+address:1]=1!-50%$OR$$NEAR:3$$OR$[can+process+replacement%:2.5]=1!-50%[%placement+will+deliver%:2]=1!-50%[chance+or+replacement:2]=1!-50%[date+of+dispatch:1.2]=1!-50%[need+replace+your+sensor:2.5]=1!-50%[receive+it|them+between:1.5]=1!-50%[provide+phone+number:2]=1!-50%[would|will+just+process+it:2]=1!-50%[prepaid|return+envelope|faulty:2]=1!-50%[customer+relations+phone+number:2.5]=1!-50%[send+it+address|you:2.5]=1!-50%[return%+it+back:2.5]=1!-50%[hotline|phone+number+it`s|is:2.5]=1!-50%[inform+you+that|we+are+replacing:2]=1!-50%[disregard|ignore+latter|letter:2]=1!-50%[send%+you+new%+centre|sensor|reader:2]=1!-50%[contact+healthcare+provider:2.5]=1!-50%[please+send+back:2]=1!-50%[recently+delivery:1.5]=1!-50%<message%>=1!-50%<error>=1!-50%$OR$$OR$[replac%|debating+sensor|session|centre|sense:2]=1!-50%$NOTNEAR:5$[evaluation|valuation+service|purposes:2]=1!-50%[contact+us+directly:2]=1!-50%[faulty+one|sensor|centre+back:2]=1!-50%{send%+replacement|placements|return%:2}=1!-50%[talk+with+our+special%:2]=1!-50%[to+our+special%:2]=1!-50%[replac%+for+you|your:2]=1!-50%[provide+brand|brains+new+sense|sensor|centre:2.2]=1!-50%[going+to+replac%:2]=1!-50%[inform|provide+you+sensor:2.5]=1!-50%[return+faulty+sensor|centre|sense:2.5]=1!-50%<error>=1!-50%<message>=1!-50%$OR$[replacement%+will+be:2.2]=1!-50%$NOTNEAR:3$[provide+replacement%:2.2]=1!-50%[free+of+charge:1.5]=1!-50%[send+a|the|old+back|pack:2.2]=1!-50%[send+new|old+one:2]=1!-50%[as+a+replacement:2]=1!-50%[send+out+assessment:2.3]=1!-50%[requir%+them+back:3]=1!-50%[system+did+not+request+you:3]=1!-50%[inform+you+that+place:2.2]=1!-50%[happy|like|glad+to+inform:2]=1!-50%[other+one+return%:2.5]=1!-50%{go|ahead|it|of+dispose:2.2}=1!-50%[gonna|will+replac%:1.5]=1!-50%[brand-new+replacement|handset|sensor|centre|sense|session:2.5]=1!-50%[send+back+senses|centre%|sensor%:2]=1!-50%[process+replacement:2]=1!-50%{replacement%+order%:2}=1!-50%[want%|call%|have+get|do+refund:2.5]=1!-50%[refund+money:1]=1!-50%[process%+refund:2]=1!-50%[proceed+with+refund:2]=1!-50%[you%+%phone+has|have+android|higher|version|software|ios:5]=1!-50%[pharmacy+change:1.2]=1!-50%[track+you%+order:1.5]=1!-50%[give|provid%|tell+number%|department%|call:2]=1!-50%[%connect%+again:1.5]=1!-50%[dry|drive+before+appl%|flight:1.2]=1!-50%[answer+%mail:1.2]=1!-50%[be+complete%|total%+gone:2]=1!-50%[will|could+download:1.2]=1!-50%[updat%+android:2]=1!-50%{download%+app|atore:1.2}=1!-50%[download|update|%install%+app|application|driver%|reader|meter|radar:3]=1!-50%[not+support%:1.2]=1!-50%[consult|ask|advise+doctor|nurse|profes%|healthcare:2]=1!-50%[phone+%n`t+support:2]=1!-50%[unfortunately+phone+%n`t:2]=1!-50%[restart+%phone:1.2]=1!-50%[issue+should+resolv%|solv%:2]=1!-50%[give+sometime:1.5]=1!-50%[so|for+the+replacement:2]=1!-50%[get+back+shortly:1.6]=1!-50%[give|ring+back|call:1.5]=1!-50%[investigate+issue:2]=1!-50%[need+evaluati%:1.2]=1!-50%[keep+sens%|centr%|them|kit:1.2]=1!-50%[get+back+to+you:1.2]=1!-50%[send|sent+upgrade:1.2]=1!-50%[that%|it%|should|now+work%:2]=1!-50%[call+landline:1.2]=1!-50%[provide+you%+with:1.5]=1!-50%[contact%+you|with:1.5]=1!-50%[health+care+professional%:2.2]=1!-50%[tried|try+scan|that|again%:2]=1!-50%[send|sent+you|back:1.2]=1!-50%[call|ring+back|us:1.2]=1!-50%[need|have+download|update|%install%:2]=1!-50%[delivery|shipping+address:1.5]=1!-50%[send+sensor:1.3]=1!-50%[plac%+order:1.2]=1!-50%[sensor+replacements:2]=1!-50%[reference+number:1.2]=1!-50%<dispatch%>=1!-50%<dispos%>=1!-50%<complain%>=1!-50%<escalat%>=1!-50%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((escalat*|complain*|dispos*|dispatch* \nOR \"reference number\":1.2 OR \"sensor replacements\":2\nOR \"plac* order\":1.2 OR \"send sensor\":1.3\nOR \"delivery|shipping address\":1.5 \nOR \"need|have download|update|*install*\":2 \nOR \"call|ring back|us\":1.2 \nOR \"send|sent you|back\":1.2 \nOR \"tried|try scan|that|again*\":2 \nOR \"health care professional*\":2.2 \nOR \"contact* you|with\":1.5 \nOR \"provide you* with\":1.5 \nOR \"call landline\":1.2 \nOR \"that*|it*|should|now work*\":2 \nOR \"send|sent upgrade\":1.2 \nOR \"get back to you\":1.2 \nOR \"keep sens*|centr*|them|kit\":1.2 \nOR \"need evaluati*\":1.2 \nOR \"investigate issue\":2 \nOR \"give|ring back|call\":1.5 \nOR \"get back shortly\":1.6 OR \"so|for the replacement\":2\nOR \"give sometime\":1.5 \nOR \"issue should resolv*|solv*\":2 \nOR \"restart *phone\":1.2 \nOR \"unfortunately phone *n't\":2 \nOR \"phone *n't support\":2 \nOR \"consult|ask|advise doctor|nurse|profes*|healthcare\":2 \nOR \"not support*\":1.2 \nOR \"download|update|*install* app|application|driver*|reader|meter|radar\":3 \nOR [download* app|atore]:1.2 \nOR \"updat* android\":2 \nOR \"will|could download\":1.2 \nOR \"be complete*|total* gone\":2 \nOR \"answer *mail\":1.2 \nOR \"dry|drive before appl*|flight\":1.2 \nOR \"*connect* again\":1.5 \nOR \"give|provid*|tell number*|department*|call\":2 \nOR \"track you* order\":1.5 \nOR \"pharmacy change\":1.2 \nOR \"you* *phone has|have android|higher|version|software|ios\":5\nOR \"proceed with refund\":2 \nOR \"process* refund\":2\nOR \"refund money\":1\nOR \"want*|call*|have get|do refund\":2.5\nOR [replacement* order*]:2 OR \"process replacement\":2\nOR \"send back senses|centre*|sensor*\":2 \nOR \"brand-new replacement|handset|sensor|centre|sense|session\":2.5 \nOR \"gonna|will replac*\":1.5\nOR [go|ahead|it|of dispose]:2.2 \nOR \"other one return*\":2.5 \nOR \"happy|like|glad to inform\":2 \nOR \"inform you that place\":2.2 \nOR \"system did not request you\":3 \nOR \"requir* them back\":3 \nOR \"send out assessment\":2.3 \nOR \"as a replacement\":2 \nOR \"send new|old one\":2 \nOR \"send a|the|old back|pack\":2.2 \nOR \"free of charge\":1.5 \nOR \"provide replacement*\":2.2 \nOR \"replacement* will be\":2.2 NOT NEAR:3 (message|error)\nOR \"return faulty sensor|centre|sense\":2.5 \nOR \"inform|provide you sensor\":2.5 \nOR \"going to replac*\":2 \nOR \"provide brand|brains new sense|sensor|centre\":2.2 \nOR \"replac* for you|your\":2 OR \"to our special*\":2 OR \"talk with our special*\":2\nOR [send* replacement|placements|return*]:2 \nOR \"faulty one|sensor|centre back\":2 OR \"contact us directly\":2\nOR \"evaluation|valuation service|purposes\":2 \nOR \"replac*|debating sensor|session|centre|sense\":2 NOT NEAR:5 ((error|message*) OR \"recently delivery\":1.5)\nOR \"please send back\":2 OR \"contact healthcare provider\":2.5\nOR \"send* you new* centre|sensor|reader\":2 OR \"disregard|ignore latter|letter\":2\nOR \"inform you that|we are replacing\":2 OR \"hotline|phone number it`s|is\":2.5 \nOR \"return* it back\":2.5 OR \"send it address|you\":2.5 OR \"customer relations phone number\":2.5\nOR \"prepaid|return envelope|faulty\":2 OR \"would|will just process it\":2 OR \"provide phone number\":2                                                                                  \nOR \"receive it|them between\":1.5 OR \"need replace your sensor\":2.5                                                                                \nOR \"date of dispatch\":1.2 OR \"chance or replacement\":2                                                                                                   \nOR \"*placement will deliver*\":2 OR \"can process replacement*\":2.5                                                                                                  \nOR ((\"delivery address\":1 OR postcode) NEAR:3 (confirm*|prepare|replace*|placement|dispatch*) \nOR \"working|within|business day*\":1.5)){-50%})=Agent OR CAT:[Quality Automation_Calls.Shipping Method]\nOR ((\"wait minute*|redacted\":1.6 OR \"restart*|*install app*|phone\":1.3) NEAR:3 (callback* OR \"call back\":1 OR \"give call\":1.2)){-30%} \nOR [*regard|ignor* mail*|letter*|automated|reminder]:2\nOR \"i|will|i'll|i'm|am leave|leaving|write|writing|make note*\":2 \nOR \"*n't worry about\":1.7 NEAR:5 (return*|letter*|automated|reminder|disregard) \nOR \"warranty line\":1.4 NEAR:7 (\"contact*|call|talk your|the supplier*|provider|healthcare\":2 OR \"get touch supplier*|provider|healthcare\":2)\nOR \"you* already|ready return* sens*|cent*|product*|it\":2\nOR (\"will|am|gonna|going send*|receive|receiving|process* refund\":1.3 NOT NEAR:3 (box|kit)) NOT AFTER:3 (*not|*n't)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32406, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32406, "BucketID": 158, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32406, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31886, "Name": "Autopass - Unnatural Call Endings", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31886, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31886, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31886, "BucketID": 158, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-10-01T05:58:16.267", "ComponentCount": 2}