{"BucketId": "519", "BucketFullname": "Categories.Emotions.-Effort - Emotions Index", "BucketDescription": "tuned for confusion. Removed IVR messages from Problems", "UserEditable": false, "BucketName": "-Effort - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.-E<PERSON>ort - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:15:56.403-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30179, "Name": "Headache", "Description": null, "ActionString": "[terrible|such|what+headache:1]", "UserEntry": "\"terrible|such|what headache\":1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30178, "Name": "Painful", "Description": null, "ActionString": "[really|very+painful:1]", "UserEntry": "\"really|very painful\":1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30177, "Name": "Chaotic", "Description": null, "ActionString": "[chaotic|hectic|frantic+enough:1][kind|kinda|little|bit|very|really|extremely|so|sounds+chaotic|hectic|messy|frantic:1]<convoluted><disorganized>$OR$$OR$$OR$", "UserEntry": "(disorganized|convoluted) OR \"kind|kinda|little|bit|very|really|extremely|so|sounds chaotic|hectic|messy|frantic\":1 OR \"chaotic|hectic|frantic enough\":1", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30176, "Name": "Inconvenient", "Description": null, "ActionString": "<apologize><sorry>$OR$<inconvenien%>$NOTNEAR:2$", "UserEntry": "inconvenien* NOT NEAR:2 (sorry|apologize)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30175, "Name": "Confusion", "Description": null, "ActionString": "[not|doesn`t|isn`t|didn`t+make|made|making+sense|scents|cents:1.5][don`t|didn`t|not+any|foggiest|slightest|faintest+idea|ideas:1.5][no+idea+what|how|why:1.2][no+idea+talking|saying|telling:2]$OR$$OR$$OR$", "UserEntry": "(\"no idea  talking|saying|telling\":2) OR (\"no idea what|how|why\":1.2) OR \"don't|didn't|not any|foggiest|slightest|faintest idea|ideas\":1.5 OR \"not|doesn't|isn't|didn't make|made|making sense|scents|cents\":1.5", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30174, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Description": null, "ActionString": "[why+difficult|hard:2]<impossible>[not+possible:1]<understand%><hear%>$OR$[hard%|difficult+time|part:1]$NOTBEFORE:2$[caus%+difficulty|trouble:1.2]$OR$$OR$$OR$$OR$", "UserEntry": "\"caus* difficulty|trouble\":1.2  OR \"hard*|difficult time|part\":1 NOT BEFORE:2 (hear*|understand*)  OR \"not possible\":1 OR impossible  OR \"why difficult|hard\":2", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30173, "Name": "Wasted Resources", "Description": null, "ActionString": "[such|big|huge+waste:1]<your>{wast%+time|money|day%|hour%:2}$NOTNEAR:0$$OR$", "UserEntry": "([wast* time|money|day*|hour*]:2 NOT NEAR:0 your) OR \"such|big|huge waste\":1", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30172, "Name": "Annoying", "Description": null, "ActionString": "<try><mean><doesn`t><don`t><free><please><want><apologize><trying><pardon><sorry>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<disrupting><disrupt><aggravating><aggravate><troubling><irritating><irritate><harrassing><disturbing><disturb><interrupting><bothering><annoying><annoy>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTAFTER:1$<try><mean><doesn`t><don`t><free><please><want><apologize><trying><pardon><sorry>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<disturbance%><interruption%><bothersome><annoyance%>$OR$$OR$$OR$$NOTAFTER:1.5$$OR$", "UserEntry": "(annoyance*|bothersome|interruption*|disturbance*) NOT AFTER:1.5 (sorry|pardon|trying|apologize|want|please|free|don't|doesn't|mean|try)\nOR (\n\t(annoy|annoying|bothering|interrupting|disturb|disturbing|harrassing|irritate|irritating|troubling|aggravate|aggravating|disrupt|disrupting) NOT AFTER:1 (sorry|pardon|trying|apologize|want|please|free|don't|doesn't|mean|try)\n)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30171, "Name": "Hard to deal with", "Description": null, "ActionString": "[make|making|made+hard|difficult:1.5][hard%|difficult%+deal%+with:1.5]$OR$", "UserEntry": "\"hard*|difficult* deal* with\":1.5 OR \"make|making|made hard|difficult\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30170, "Name": "Problem or Issue", "Description": null, "ActionString": "<no><not>$OR$<say><press>$OR$[having|experiencing+trouble|troubles|problem%|troubling|issue|issues:0.5]$NOTAFTER:5$$NOTNEAR:0.5$", "UserEntry": "( (\"having|experiencing trouble|troubles|problem*|troubling|issue|issues\") NOT AFTER:5 (press|say) )  NOT NEAR:0.5 (not|no)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29976, "Name": "<PERSON><PERSON>", "Description": null, "ActionString": "[i`m|i+annoyed|bothersome|bothered|disturbed:0.5][still|quit|stop|keep|constantly|always+fucking|harassing|bugging|bothering|annoying:0.5]$OR$", "UserEntry": "(\"still|quit|stop|keep|constantly|always fucking|harassing|bugging|bothering|annoying\")\nOR (\"i'm|i annoyed|bothersome|bothered|disturbed\")", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29975, "Name": "Interruption", "Description": null, "ActionString": "<try><mean><won`t><doesn`t><don`t><free><please><want><apologize><trying><pardon><sorry>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[interrupt|interrupting+you:0.5]$NOTAFTER:2$<free>[interrupt+me:0.5]$NOTAFTER:1$$OR$", "UserEntry": "((\"interrupt me\") NOT AFTER:1 (free) ) OR ((\"interrupt|interrupting you\") NOT AFTER:2 (sorry|pardon|trying|apologize|want|please|free|don't|doesn't|won't|mean|try))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29787, "Name": "bad time to call", "Description": null, "ActionString": "[o`clock|clock+in+the+morning:1.1][o`clock|clock+at+night:0.8]$OR$[calling+me:0.5]$NEAR:3$", "UserEntry": "\"calling me\" NEAR:3 (\"o'clock|clock at night\" OR \"o'clock|clock in the morning\")", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-22T17:51:27.65", "ComponentCount": 13}