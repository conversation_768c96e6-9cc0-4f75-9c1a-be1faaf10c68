{"BucketId": null, "BucketFullname": "", "BucketDescription": "Identifies calls where the agent has followed process for shipping, confirming shipping address, method, timeframe and asking if customer has backup way of checking blood sugar levels.", "UserEditable": false, "BucketName": "Shipping Method Process - New", "SectionName": "", "SectionNameDisplay": "", "SectionId": -99, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 2.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T16:37:22", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T16:37:22", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.5, "SearchComponents": [{"Id": null, "Name": "Address Confirmation", "Description": null, "ActionString": "(quality automation_calls.address confirmation)", "UserEntry": "CAT:[Quality Automation_Calls.Address Confirmation] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Shipping Method", "Description": null, "ActionString": "(quality automation_calls.shipping method)", "UserEntry": "CAT:[Quality Automation_Calls.Shipping Method] ", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Delivery Timeframe", "Description": null, "ActionString": "[business+day%:1.6]!-30%<day%>[it+should+arrive+within|in:1.2]$NEAR:3$[you+will+receive+replacement|package|shipment|order|deliver:1.4]<battery>[five|one%|two|three|redacted+business+day%:2]$NOTNEAR:4$<left><ago>$OR$[business+days+receiv%|deliver%|get:1.2][expect|receive|shiping|shipment|parcel|package|arrive|timeframe|next|getting|replacement|redacted|kit|address|standard|overnight+within|days|business:2.5]$OR$$NOTNEAR:2$$OR$$OR$$OR$$OR$", "UserEntry": "(\"expect|receive|shiping|shipment|parcel|package|arrive|receive|timeframe|next|getting|replacement|redacted|kit|address|standard|overnight within|days|business\":2.5 OR \"business days receiv*|deliver*|get\":1.2) NOT NEAR:2 (ago|left) OR \"five|one*|two|three|redacted business day*\":2 NOT NEAR:4 (battery) OR \"you will receive replacement|package|shipment|order|deliver\":1.4 OR \"it should arrive within|in\":1.2 NEAR:3 (day*) OR \"business day*\":1.6{-30%}", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T16:37:22", "ComponentCount": 3}