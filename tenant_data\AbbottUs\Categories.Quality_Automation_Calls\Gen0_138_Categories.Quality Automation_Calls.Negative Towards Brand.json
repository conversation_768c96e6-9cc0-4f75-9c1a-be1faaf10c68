{"BucketId": "138", "BucketFullname": "Categories.Quality Automation_Calls.Negative Towards Brand", "BucketDescription": "Stating negative or derrogative statements towards the brand.", "UserEditable": false, "BucketName": "Negative Towards Brand", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Negative Towards Brand", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 0.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2024-01-30T10:30:57.347-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 30, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32368, "Name": "Negative Towards Brand", "Description": null, "ActionString": "[there+were|are+quality+issues:1.5]=1[we+have+issues+with+product|sensor:2]=1[people|patient%+have+same+issue:1.5]=1[i`m+not+satisfied|happy+with+company:2]=1[new+product%:1.2]=1[we+had+issue:2]=1$NEAR:5$$OR$$OR$$OR$$OR$<apologi%>=1<sorry>=1<alright>=1$OR$$OR$<%other><abbott><company>$OR$[not+happy|satified:0.5]$NEAR:15$$NOTNEAR:3$$BEFORE:20$<apologi%>=1<sorry>=1$OR$[with+company:1.2][not+happy:0.5][not+satisfied:0.5]<unimpressed>$OR$$OR$$NEAR:5$$BEFORE:20$[why+colleague|agent+told+you+that:2]=1[agent|previous|contact+should+mentioned:2]=1$OR$[you|was|been|were|have|they+promised:1.5][you|they|she|they`ve|have|has|colleague|he`s|gentleman+told+me:1.5]=2{gentleman|lady|person|earlier|yeasterday|afternoon|colleague%+i+spoke+to:2}[your|my|our|a+colleague%:1.2][previous|%other|different+agent:1]$OR$$OR$$OR$$OR$[made+mistake:1]=1<don`t>=1[no+you+have+to:1.2]=1$NOTNEAR:1$[did+not+happen:1.2]=1[wrong|incorrect+information:1]=1[that+correct:1]=1<is>=1<person>=1<address>=1<one>=1$OR$$OR$$OR$$OR$[not+correct:0.6]=1$NOTNEAR:10$$OR$$OR$$OR$$OR$$AFTER:20$$OR$$OR$$OR$$OR$", "UserEntry": "((\"not correct\":0.6 NOT NEAR:10 ((one|address|person|is) OR \"that correct\":1)) \nOR \"wrong|incorrect information\":1 \nOR \"did not happen\":1.2 \nOR \"no you have to\":1.2 NOT NEAR:1 don't\nOR \"made mistake\":1)=Agent AFTER:20 (\"previous|*other|different agent\":1 \nOR \"your|my|our|a colleague*\":1.2\nOR [gentleman|lady|person|earlier|yeasterday|afternoon|colleague* i spoke to]:2 \nOR \"you|they|she|they`ve|have|has|colleague|he`s|gentleman told me\":1.5=Customer \nOR \"you|was|been|were|have|they promised\":1.5) \nOR (\"agent|previous|contact should mentioned\":2\nOR \"why colleague|agent told you that\":2)=AGENT \nOR ((unimpressed|\"not satisfied\"|\"not happy\") NEAR:5 \"with company\":1.2) BEFORE:20 (sorry|apologi*)=Agent \nOR ((\"not happy|satified\" NEAR:15 (company|abbott)) NOT NEAR *other) BEFORE:20 (alright|sorry|apologi*)=Agent \nOR (\"we had issue\":2 NEAR:5 \"new product*\":1.2\nOR \"i'm not satisfied|happy with company\":2\nOR \"people|patient* have same issue\":1.5 \nOR \"we have issues with product|sensor\":2 \nOR \"there were|are quality issues\":1.5)=Agent", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32368, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32368, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-17T09:08:27.23", "ComponentCount": 1}