{"BucketId": "518", "BucketFullname": "Categories.Emotions.-Neg Experience - Emotions Index", "BucketDescription": "tuned for IVR/voicemail and weighted for abuse", "UserEditable": false, "BucketName": "-Neg Experience - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.-Neg Experience - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:13:30.17-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30193, "Name": "Horrific", "Description": null, "ActionString": "<injury><plan><coverage>$OR$$OR$<catastroph%>$NOTNEAR:3$<accident><wreck>$OR$<horrif%>$NOTNEAR:1$$OR$", "UserEntry": "(horrif* NOT NEAR:1 (wreck|accident)) OR ( catastroph* NOT NEAR (coverage|plan|injury) )", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30192, "Name": "<PERSON><PERSON><PERSON>", "Description": null, "ActionString": "[they|you|stop|agent|kept|keep%|quit|try|trying|tried+pressur%+me:1.5][feeling|feel|felt+rush%|pressur%:1][forcing+me|us:1][gonna|going+force+me|us:1.5][was|am|be+forced+upon|cancel|assume|sign|contract|on|into|call:1.5]<not><wasn`t>$OR$[very|being|little|too|overly+pushy:1.2]$NOTAFTER:1$[agent%|rep|reps%|representative%+pushy:1.5]$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"agent*|rep|reps*|representative* pushy\":1.5 OR (\"very|being|little|too|overly pushy\":1.2 NOT AFTER:1 (wasn't|not)) OR \"was|am|be forced upon|cancel|assume|sign|contract|on|into|call\":1.5 OR \"gonna|going force me|us\":1.5 OR \"forcing me|us\":1 OR \"feeling|feel|felt rush*|pressur*\":1 OR \"they|you|stop|agent|kept|keep*|quit|try|trying|tried pressur* me\":1.5", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30191, "Name": "<PERSON><PERSON>", "Description": null, "ActionString": "[coming|come+across+rude:2][you+don`t|not+have|mean|need+rude:2.4]<not>[try%|you`re|are|sound%+rude:1.5]$NOTAFTER:1$<not>[that|that`s+rude|disrespectful:0.5]$NOTNEAR:0$$AND$$OR$$OR$", "UserEntry": "( (\"that|that's rude|disrespectful\") NOT NEAR:0 (not) )\n (\"try*|you're|are|sound* rude\":1.5 NOT AFTER:1 (not) )\n OR ( \"you don't|not have|mean|need rude\":2.4 )\n OR ( \"coming|come across rude\":2 )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30190, "Name": "Terrible Experience", "Description": null, "ActionString": "[mean|ugly|nasty+nasty|ugly:1][nasty|ugly+e-mail|email|message%|call:1.2]{terrible+experience|thing|event|for:1}[terrible+service|review%|wait%|agent|person:1]$OR$$OR$$OR$", "UserEntry": "( \"terrible service|review*|wait*|agent|person\":1 )\n OR ( [terrible experience|thing|event|for]:1 )\n OR ( \"nasty|ugly e-mail|email|message*|call\":1.2 )\n OR (\"mean|ugly|nasty nasty|ugly\":1 )", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30189, "Name": "Yelling", "Description": null, "ActionString": "[yell%|screaming|shouting+at+me:1.2][call+yell|shout|scream:1.5][stop+yelling|screaming|shouting:1]$OR$$OR$", "UserEntry": "\"stop yelling|screaming|shouting\":1 OR \"call yell|shout|scream\":1.5 OR \"yell*|screaming|shouting at me\":1.2", "SpeakerList": "", "Status": true, "Weight": 5.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30188, "Name": "Disagreement", "Description": null, "ActionString": "(emotions.ivr or voicemail)[no+way+no+how:1.2]<afford><cancel>$OR$[no+way:0.8]$NEAR:3$[gonna|going+do:1][doing+that:0.8]$OR$[no+way:0.8]$BEFORE:1$<paying><pay>$OR$[no+way:0.8]$NEAR:2$[don’t|never|won`t|not+agree:0.8]<disagree%>$OR$$OR$$OR$$OR$$OR$$NOTNEAR:3$", "UserEntry": "(disagree* OR \"don’t|never|won't|not agree\":.8 OR \"no way\":.8 NEAR:2 (pay|paying) OR \"no way\":.8 BEFORE:1 (\"doing that\":.8 OR \"gonna|going do\":1) OR \"no way\":.8 NEAR:3 (cancel|afford) OR \"no way no how\":1.2) NOT NEAR:3 (CAT:[Emotions.IVR or Voicemail] )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30187, "Name": "Failure to follow through", "Description": null, "ActionString": "(emotions.ivr or voicemail)<rejected>$NOTAFTER:1$[not+issued|notified|expected|filed|corrected|elected:1]<reflected><reject><infected><decline%><denied>$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(denied|decline*|infected|reject|reflected) OR \"not issued|notified|expected|filed|corrected|elected\":1 OR (rejected NOT AFTER:1 CAT:[Emotions.IVR or Voicemail] )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30186, "Name": "Offensive", "Description": null, "ActionString": "<not>[that|that`s+offensive|inappropriate|disrespectful|ignorant:0.5]$NOTNEAR:0$<won`t><not>$OR$[i|i`m+offended:0.5]$NOTNEAR:0$<didn`t><won`t><no>$OR$$OR$[take|find%|taking|taken+offense:1]$NOTNEAR:0.5$$OR$$OR$", "UserEntry": "(\"take|find*|taking|taken offense\":1  NOT NEAR:0.5 (no|won't|didn't )  )\n OR (\"i|i'm offended\" NOT NEAR:0 (not|won't ) )\n OR ( (\"that|that's offensive|inappropriate|disrespectful|ignorant\") NOT NEAR:0 (not) )", "SpeakerList": "", "Status": true, "Weight": 5.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30185, "Name": "Not reliable", "Description": null, "ActionString": "[can`t+rely+on:0.8][not|isn`t|wasn`t+reliable:1][it`s|they+unreliable|inconsistent|undependable|piece|shit|crap:0.5]$OR$$OR$", "UserEntry": "( \"it's|they unreliable|inconsistent|undependable|piece|shit|crap\" )\n OR (\"not|isn't|wasn't reliable\":1) OR (\"can't rely on\" )", "SpeakerList": "", "Status": true, "Weight": 5.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30184, "Name": "Overwhelmed-Stressed", "Description": null, "ActionString": "[stressed+out:0.5][lot+pressure|stress|anxiety|worry:1]<not>[little|too|bit|not|being|getting|it`s|so|very|just|was|i`m|am|is+overwhelm%|stressful|stressing|challenging:2.5]$NOTNEAR:0$[i|i`m+stressed|flustered|worried:0.5]$OR$$OR$$OR$", "UserEntry": "( \"i|i'm stressed|flustered|worried\" )\n OR ( ( \"little|too|bit|not|being|getting|it's|so|very|just|was|i'm|am|is overwhelm*|stressful|stressing|challenging\":2.5) NOT NEAR:0 (not) )\n OR ( \"lot pressure|stress|anxiety|worry\":1 )\n OR ( \"stressed out\" )", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30183, "Name": "Finding Fault", "Description": null, "ActionString": "<wasn`t><isn`t><not>$OR$$OR$[your|her|their|agent|agents|representative%|his|compan%|guy|guys|who`s+fault:1]$NOTAFTER:0.5$[not|isn`t+my|our+fault%:1]$OR$", "UserEntry": "\"not|isn't my|our fault*\":1 OR ( \"your|her|their|agent|agents|representative*|his|compan*|guy|guys|who's fault\":1)  NOT AFTER:0.5 (not|isn't|wasn't)", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30182, "Name": "Mistake", "Description": null, "ActionString": "[mixed|mix+up:0.6][he|she|someone|somebody|you|totally|they+screwed|screw+up:0.6]$OR$", "UserEntry": "\"he|she|someone|somebody|you|totally|they screwed|screw up\":0.6 OR \"mixed|mix up\":.6", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30181, "Name": "Not helpful", "Description": null, "ActionString": "<pay><medic%><bill%><grocer%><rent><state>$OR$$OR$$OR$$OR$$OR$[not|wasn`t|isn`t+helping|helped|helper|helpful:0.6][doesn`t|didn`t|couldn`t|wouldn`t|not|refus%+help:0.5]$OR$$NOTNEAR:3$", "UserEntry": "( ( \"doesn't|didn't|couldn't|wouldn't|not|refus* help\" ) OR ( \"not|wasn't|isn't helping|helped|helper|helpful\":0.6) ) NOT NEAR:3 (state|rent|grocer*|bill*|medic*|pay)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30180, "Name": "<PERSON><PERSON>-<PERSON>lue<PERSON>", "Description": null, "ActionString": "<were><am><i`m>$OR$$OR$<uninformed>$NOTBEFORE:1$[wasn`t|isn`t|not+knowledgeable|trained|intelligent|smart:0.8]<truck><dumb>$NOTBEFORE:1$[no+brains|wit|intelligence|smart%|clue:0.5][simple|dull|scatter+witted|mind|minded|brain%:0.5]<bonehead%><naive><ignorant><unintelligent><unreliable><clueless>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(clueless|unreliable|unintelligent|ignorant|naive|bonehead*) \n OR (\"simple|dull|scatter witted|mind|minded|brain*\")\n OR (\"no brains|wit|intelligence|smart*|clue\")\n OR (dumb NOT BEFORE:1 truck) \n OR \"wasn't|isn't|not knowledgeable|trained|intelligent|smart\":.8 \n OR (uninformed NOT BEFORE:1 (i'm|am|were))", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29972, "Name": "Really Rude", "Description": null, "ActionString": "[was|being+extremely|very|really|so|terribly|incredibly+rude|disrespectful|offensive:0.8]", "UserEntry": "\"was|being extremely|very|really|so|terribly|incredibly rude|disrespectful|offensive\"", "SpeakerList": "", "Status": true, "Weight": 5.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29843, "Name": "Understandability - Auditory", "Description": null, "ActionString": "(emotions.ivr or voicemail)(understandability.auditory understandability)$NOTNEAR:5$", "UserEntry": "CAT:[Understandability.Auditory Understandability] NOT NEAR:5 CAT:[Emotions.IVR or Voicemail] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29842, "Name": "Understandability -Speech", "Description": null, "ActionString": "(understandability.speech understandability)", "UserEntry": "CAT:[Understandability.Speech Understandability] ", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29788, "Name": "OMG-negative", "Description": null, "ActionString": "(emotions.-dissatisfaction - emotions index)[oh|ohh+my+god|lord|gosh|goodness|jesus:0.8]$NEAR:5$", "UserEntry": "\"oh|ohh my god|lord|gosh|goodness|jesus\" NEAR:5 CAT:[Emotions.-Dissatisfaction - Emotions Index] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29786, "Name": "Stop Calling me", "Description": null, "ActionString": "[stop|quit+calling+me|us:1.5]", "UserEntry": "\"stop|quit calling me|us\":1.5", "SpeakerList": "", "Status": true, "Weight": 5.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-22T17:51:17.687", "ComponentCount": 19}