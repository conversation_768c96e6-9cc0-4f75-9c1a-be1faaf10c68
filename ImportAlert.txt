ImportAlert.py - CallMiner Alert Import Utility
===============================================

OVERVIEW:
---------
ImportAlert.py is a comprehensive utility for importing CallMiner alerts and their configurations into the FEAPI system. This script provides robust import capabilities with validation, dependency resolution, and error handling to support migration, restoration, and bulk configuration workflows.

PRIMARY PURPOSE:
----------------
- Import alert configurations into CallMiner FEAPI
- Restore alerts from backup files
- Migrate alerts between environments
- Bulk create alerts from templates
- Validate and resolve alert dependencies
- Support configuration management workflows

KEY FEATURES:
-------------

1. FLEXIBLE INPUT FORMATS:
   - Import from Excel workbooks (exported by ExportAlert.py)
   - Import from JSON files with alert configurations
   - Import from CSV files with alert data
   - Support for individual alert files or bulk imports
   - Template-based alert creation

2. COMPREHENSIVE VALIDATION:
   - Pre-import validation of alert data
   - Trigger condition validation and syntax checking
   - Dependency validation and resolution
   - Duplicate detection and handling
   - Data integrity checks and corrections

3. DEPENDENCY RESOLUTION:
   - Automatic resolution of score dependencies
   - Handling of category dependencies in triggers
   - Missing dependency detection and reporting
   - Dependency order optimization for imports

4. FLEXIBLE IMPORT OPTIONS:
   - Create new alerts or update existing ones
   - Selective import with filtering capabilities
   - Dry-run mode for validation without changes
   - Rollback capabilities for failed imports
   - Incremental import support

COMMAND LINE INTERFACE:
-----------------------
The script supports extensive command-line configuration:

Basic Usage:
python ImportAlert.py --InputFile "Alerts_backup.xlsx"

Common Parameters:
--InputFile: Path to input file (Excel, JSON, or CSV)
--AlertFilter: Filter alerts to import by name pattern
--FolderFilter: Filter by folder name
--OutputPrefix: Customize output file naming
--LogRoot: Specify log file directory
--DryRun: Validate without making changes
--OverwriteExisting: Overwrite existing alerts
--CreateMissing: Create missing dependencies

Example Commands:
python ImportAlert.py --InputFile "backup.xlsx" --DryRun True
python ImportAlert.py --InputFile "alerts.json" --OverwriteExisting True
python ImportAlert.py --InputFile "data.csv" --AlertFilter "Quality.*"

CONFIGURATION:
--------------
Configuration is managed through INI files and command-line arguments:

INI File Structure (ImportAlert.py.INI):
[FEAPI]
API = feapi
Tenant = YourTenant
LogRoot = logs
DEBUG = False

[ImportAlert]
InputFile = 
AlertFilter = 
FolderFilter = 
OutputPrefix = Import
DryRun = False
OverwriteExisting = False
CreateMissing = True
ValidateOnly = False

MAIN FUNCTIONS:
---------------

1. LoadInputData(config):
   - Loads alert data from various input formats
   - Validates file format and structure
   - Converts data to standardized internal format
   - Handles encoding and format issues

2. ValidateAlerts(helper, config, alert_data):
   - Validates alert data integrity
   - Checks for required fields and valid values
   - Validates trigger conditions and syntax
   - Identifies potential conflicts and issues

3. ResolveDependencies(helper, config, alert_data):
   - Analyzes alert dependencies on scores and categories
   - Resolves dependency order for import
   - Identifies missing dependencies
   - Handles complex alert relationships

4. ImportAlerts(helper, config, validated_data):
   - Performs the actual alert import
   - Creates or updates alerts in FEAPI
   - Handles errors and rollback scenarios
   - Provides progress tracking and reporting

5. GenerateReport(helper, config, import_results):
   - Creates detailed import reports
   - Summarizes success and failure statistics
   - Documents any issues or warnings
   - Provides recommendations for resolution

INPUT FILE FORMATS:
-------------------

1. Excel Format (.xlsx):
   - Alerts worksheet with alert data
   - Dependencies worksheet with relationship data
   - Triggers worksheet with trigger conditions
   - Notifications worksheet with notification settings
   - Must match ExportAlert.py output format

2. JSON Format (.json):
   - Array of alert objects with complete configurations
   - Nested structure for triggers and notifications
   - Metadata and dependency information included

3. CSV Format (.csv):
   - Flat structure with alert properties
   - Separate files for dependencies and triggers
   - Header row with column definitions

VALIDATION PROCESS:
-------------------

1. STRUCTURE VALIDATION:
   - Verify required fields are present
   - Check data types and formats
   - Validate alert name conventions
   - Ensure proper folder structure

2. TRIGGER VALIDATION:
   - Check trigger condition syntax
   - Validate threshold values and operators
   - Verify score and category references
   - Ensure logical consistency

3. NOTIFICATION VALIDATION:
   - Validate notification recipients
   - Check notification method configurations
   - Verify template references
   - Ensure proper scheduling settings

4. DEPENDENCY VALIDATION:
   - Verify referenced scores exist
   - Check category references in triggers
   - Validate folder assignments
   - Ensure proper dependency relationships

IMPORT PROCESS:
---------------

1. PRE-IMPORT PHASE:
   - Load and validate input data
   - Resolve dependencies and ordering
   - Check for conflicts with existing data
   - Generate import plan and validation report

2. IMPORT EXECUTION:
   - Create alerts in dependency order
   - Handle errors and retry logic
   - Update existing alerts if specified
   - Track progress and maintain transaction log

3. POST-IMPORT VALIDATION:
   - Verify all alerts were created successfully
   - Validate alert configurations in FEAPI
   - Check trigger conditions and thresholds
   - Generate final import report

ALERT DATA ELEMENTS:
--------------------
The import handles comprehensive alert information:

- Basic Properties: Name, Description, Folder assignment
- Configuration: Active status, Priority, Severity
- Triggers: Conditions, Thresholds, Logic operators
- Notifications: Recipients, Methods, Templates
- Scheduling: Frequency, Time windows, Delays
- Dependencies: Referenced scores and categories
- Metadata: Version information, Generation data

ERROR HANDLING:
---------------
- Comprehensive error logging with detailed context
- Graceful handling of API failures and timeouts
- Rollback capabilities for partial failures
- Clear error messages with resolution guidance
- Validation warnings for potential issues

ROLLBACK CAPABILITIES:
----------------------
- Transaction logging for rollback support
- Backup of existing alerts before modification
- Selective rollback of failed imports
- Recovery from partial import failures
- Audit trail of all changes made

PERFORMANCE CONSIDERATIONS:
---------------------------
- Efficient batch processing for large imports
- Optimized API calls to minimize requests
- Memory-efficient processing of large datasets
- Progress indicators for long-running imports
- Configurable batch sizes and timeouts

SECURITY FEATURES:
------------------
- Secure credential management
- Validation of input data for security issues
- Proper authentication token handling
- Audit trail of import operations
- Access control validation

USE CASES:
----------

1. BACKUP RESTORATION:
   - Restore alerts from backup files
   - Disaster recovery scenarios
   - Environment restoration after failures

2. MIGRATION SUPPORT:
   - Migrate alerts between environments
   - Cross-tenant alert migration
   - Development to production deployment

3. BULK CONFIGURATION:
   - Mass creation of similar alerts
   - Template-based alert deployment
   - Standardization across environments

4. DEVELOPMENT WORKFLOWS:
   - Import test data for development
   - Configuration synchronization
   - Automated deployment processes

INTEGRATION:
------------
- Designed to work with ExportAlert.py output
- Integrates with other FEAPI utilities
- Supports automation and CI/CD workflows
- Compatible with configuration management tools

TROUBLESHOOTING:
----------------
Common issues and solutions:
- File format errors: Verify input file format and structure
- Trigger errors: Check trigger condition syntax and references
- Dependency errors: Verify all referenced scores and categories exist
- Notification errors: Check recipient and method configurations
- API errors: Verify credentials and permissions
- Validation failures: Review validation report for specific issues

This utility is essential for CallMiner administrators who need to import, migrate, or restore alert configurations across different environments and tenants.
