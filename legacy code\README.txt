Installing

1. Open pyCharm and Run SETUP.cmd to download all the libraries required into your current environment
2. Retrieve the JWT token from feapi.callmienr.net for the tenant you are targeting

# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token file.
# the URL will look like this: https://feapi.callminer.net//swagger/ui/index?JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9anjOeIiPmLGX_q5DqTme12Gmx2L-ztYMgFwg66b4vI
# eveyrthing after JWT= is what should be copied into token.txt

3. Change the target section or bucketfull name you wish to export. IT will select whatever buckets have this value as a prefix

# Default Section/Bucket Prefix to export
BucketFullnameFilter = 'Categories.Emotions'


Run code.  It should create the following:

generations.json = Categories listed in generational order
<tenant>.Dependencies_cache.json  (if dependencies between categories change, erase this file so it rebuilds)

<tenant> output folder
     <BucketFullnameFilter>
        <BucektFullnameFilter>.xlsx = excel file with category list, generations, dependencies, and category definitions all bundled together
        <Category>.json = json definition of category that can be re-imported into another tenant