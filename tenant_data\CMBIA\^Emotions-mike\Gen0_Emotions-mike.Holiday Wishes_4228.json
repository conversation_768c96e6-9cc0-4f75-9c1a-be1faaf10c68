{"BucketId": "4228", "BucketFullname": "Categories.Emotions-mike.<PERSON> Wishes", "BucketDescription": "Capturing Holiday wishes as positive language", "UserEditable": false, "BucketName": "Holiday Wishes", "SectionName": "Emotions-mike", "SectionNameDisplay": "Emotions-mike", "SectionId": 2326, "RetroStartDate": null, "CategoryDisplay": "Emotions-mike.<PERSON>", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T20:45:19.85-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "Unknown", "EffectiveDate": "2025-02-23T20:44:41", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -22324, "Name": "Holiday Greeting-Closing", "Description": null, "ActionString": "[happy|merry|wonderful|great|blessed|nice|safe|beautiful|favorite|amazing+holiday%|thanksgiving|christmas|year%|easter|fourth|july|memorial|labor|veterans:0.5]", "UserEntry": "\"happy|merry|wonderful|great|blessed|nice|safe|beautiful|favorite|amazing holiday*|thanksgiving|christmas|year*|easter|fourth|july|memorial|labor|veterans\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22323, "Name": "Happy Birthday", "Description": null, "ActionString": "[happy|belated+birthday:0.5]", "UserEntry": "\"happy|belated birthday\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T20:45:20.82", "ComponentCount": 2}