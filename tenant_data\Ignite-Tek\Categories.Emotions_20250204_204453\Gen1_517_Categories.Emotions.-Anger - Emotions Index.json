{"BucketId": "517", "BucketFullname": "Categories.Emotions.-Anger - Emotions Index", "BucketDescription": "Updated for false positive language (medical)", "UserEditable": false, "BucketName": "-Anger - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.-Anger - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:09:23.713-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30207, "Name": "Angry-Specific", "Description": null, "ActionString": "[absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that`s|very|was|were|i`m|we`re|more+pissed|ticked+off:1][pissed|ticked+about|already|because|but|that:0.5][piss%|tick%+me+off:0.8][hate+harsh:1.8][taken+aback:1][total%|complete+jerk:1]{not+try%+jerk:1.5}[am|was|little|bit|very|really+grouch%:1.5][i|i`m+furious:1.2]{sorry|apologize|attitude+bitchy|crabby:2}[i`m|am|so|very+bitchy|crabby:2]<excruciat%><exasperat%><perturb%><disgust%><agitat%><annoy><angry><aggravat%><irritat%><irate><livid><piss%><snappy><grumpy>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(grumpy|snappy|piss*|livid|irate|irritat*|aggravat*|angry|annoy|agitat*|disgust*|perturb*|exasperat*|excruciat*) OR (\"i'm|am|so|very bitchy|crabby\":2) OR ([sorry|apologize|attitude bitchy|crabby]:2) OR \"i|i'm furious\":1.2 OR \"am|was|little|bit|very|really grouch*\":1.5  OR [not try* jerk]:1.5 OR \"total*|complete jerk\":1 OR  \"taken aback\":1 OR \"hate harsh\":1.8 OR \"piss*|tick* me off\":0.8 OR \"pissed|ticked about|already|because|but|that\":0.5 OR \"absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that's|very|was|were|i'm|we're|more pissed|ticked off\":1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30206, "Name": "Complaint Language", "Description": null, "ActionString": "[i+want|need|wish|like+to+complain:1.1][raise|lodge|express|report|voice|submit|escalate|highlight|register|assert|document|initiate+complain|complaint|concern|issue|grievance:0.5]$OR$", "UserEntry": "(\"Raise|Lodge|Express|Report|Voice|Submit|Escalate|Highlight|Register|Assert|Document|Initiate complain|complaint|concern|issue|grievance\") OR (\"i want|need|wish|like to complain\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30205, "Name": "Frustration", "Description": null, "ActionString": "<freshened><frustrat%>$OR$[fresh+ration%:1][fresh+traded:1]$OR$$OR$", "UserEntry": "\"fresh traded\":1 OR \"fresh ration*\":1 OR (frustrat*|freshened)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30204, "Name": "Signs of Anger", "Description": null, "ActionString": "<swear%><cursing><argu%>$OR$$OR$", "UserEntry": "(argu*|cursing|swear*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30202, "Name": "Really Angry-specific", "Description": null, "ActionString": "[extremely|totally|utterly|exceptionally|remarkably|intensely|unbelievably|incredibly|absolutely|deeply|profoundly|highly|significantly|overwhelmingly|exceedingly|really+grumpy|furious|mad|upset|incensed|fuming|snappy|piss%|livid|irate|irritat%|aggravat%|angry|annoy|agitat%|disgust%|perturb%|exasperat%:1.2]", "UserEntry": "\"Extremely|Totally|Utterly|Exceptionally|Remarkably|Intensely|Unbelievably|Incredibly|Absolutely|Deeply|Profoundly|Highly|Significantly|Overwhelmingly|Exceedingly|really grumpy|furious|mad|upset|incensed|fuming|snappy|piss*|livid|irate|irritat*|aggravat*|angry|annoy|agitat*|disgust*|perturb*|exasperat*\":1.2", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30201, "Name": "Shocked", "Description": null, "ActionString": "[skipped+a+beat:0.8][blown+away|water:0.8]<flummoxed><shell-shocked>[in+disbelief+:0.8]<flummoxed><thunderstruck><over><bowled>$AND$<overwhelmed><aback><taken>$AND$<speechless><bewildered><baffled><dumbfounded><astounded><amazed><shockedastonished><stunned><flabbergasted>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(flabbergasted|stunned|shockedAstonished | Amazed | Astounded | Dumbfounded | Baffled | Bewildered | Speechless | Taken aback | Overwhelmed | Bowled over | Thunderstruck | Flummoxed | \"In disbelief \"| Shell-shocked | Flummoxed) OR \"blown away|water\":.8 OR (\"skipped a beat\")", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30197, "Name": "Impatient_minus medical", "Description": null, "ActionString": "<insurance><plan><redactmoney><counter><copay><coutner><hospital><patient><doctor><outpatient><thank%><day><stays><inpatient>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<impatience><impatient>$OR$$NOTNEAR:3$[losing|lost|lose+patience|patients:1]$OR$", "UserEntry": "(\"losing|lost|lose patience|patients\":1) OR ((impatient|impatience) NOT NEAR (inpatient|stays|day|thank*|outpatient|doctor|patient|hospital|coutner|copay|counter|redactmoney|plan|insurance))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30196, "Name": "Upset", "Description": null, "ActionString": "<not>[upset+about:1]$NOTAFTER:1$[how+upset+would:2][that%+upsetting:1.2]<not>[pretty|little|listener%|customer%|very|quite|so|really|getting|becoming|going|gonna|why|just+upset%:1.8]$NOTNEAR:0$$OR$$OR$$OR$", "UserEntry": "(\"pretty|little|listener*|customer*|very|quite|so|really|getting|becoming|going|gonna|why|just upset*\":1.8 NOT NEAR:0 not) OR \"that* upsetting\":1.2  OR \"how upset would\":2 OR (\"upset about\":1 NOT AFTER:1 not)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30195, "Name": "Nonsense", "Description": null, "ActionString": "<channel%>[some|that|that`s|is|this|same|about|stop|no|put+nonsens%:1]$NOTNEAR:1$", "UserEntry": "\"some|that|that's|is|this|same|about|stop|no|put nonsens*\":1 NOT NEAR:1 channel*", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30194, "Name": "Unreasonable", "Description": null, "ActionString": "[it|it`s|that|that`s|very|little|which|really|getting|extremely|pretty+dishearten%|disconcert%|discourag%:1][that|that`s|it+isn`t|not+right:1][not|isn`t|wasn`t+fair:1]<obnoxious><unheard><ludicrous%><pointless%><unreasonable><unfair><absurd>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(absurd|unfair|unreasonable|pointless*|ludicrous*|unheard|obnoxious) OR \"not|isn't|wasn't fair\":1 OR \"that|that's|it isn't|not right\":1 OR \"it|it's|that|that's|very|little|which|really|getting|extremely|pretty dishearten*|disconcert*|discourag*\":1", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30015, "Name": "fed up", "Description": null, "ActionString": "[can`t+wait+any|longer:0.8][i|have|i`m+had+enough:1][fed+up:0.5][wit`s+end:0.5]$OR$$OR$$OR$", "UserEntry": "(\"wit's end\"|\"fed up\"|\"i|have|i'm had enough\":1|\"can't wait any|longer\")", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30014, "Name": "out of patience", "Description": null, "ActionString": "[out+of+patience:2][patience+worn|wore|end:3]<doctor>[losing|lost|lose|no+patience|patients:1]$NOTNEAR:3$$OR$$OR$", "UserEntry": "((\"losing|lost|lose|no patience|patients\":1) NOT NEAR:3 doctor) OR (\"patience worn|wore|end\":3) or (\"out of patience\":2)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30010, "Name": "Angry", "Description": null, "ActionString": "(emotions.anger words)", "UserEntry": "CAT:[Emotion<PERSON>.Anger Words] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30009, "Name": "Really Angry", "Description": null, "ActionString": "(emotions.anger words)[really|very|so|quite|pretty:0.2]$BEFORE:2$(emotions.anger words)(emotions.emotion amplifiers)$BEFORE:1.5$$OR$", "UserEntry": "CAT:[Emotions.Emotion Amplifiers] BEFORE:1.5 CAT:[Emotions.Anger Words] \nOR ( (\"Really|Very|So|Quite|Pretty\") BEFORE:2 CAT:[Emotions.Anger Words] )", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30007, "Name": "don`t talk to me", "Description": null, "ActionString": "[don`t|dare+talk|speak|cuss|swear|scream|yelling|shout|rail|rant|rave+to+me:1.1]", "UserEntry": "(\"don't|dare talk|speak|cuss|swear|scream|yelling|shout|rail|rant|rave to me\")", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29892, "Name": "Continued Frustration", "Description": null, "ActionString": "[banging+head|wall:0.5][burned+out:0.5][lost+cause:0.5][never+end|ending|ended:0.5][will+it+end:0.8][endless+loop:0.5][running+circles:0.5]<drive%><transport%>$OR$[this+back+forth:0.8]$NOTNEAR:3$[sick+and+tired:0.8][still+dealing:0.5][out+of+patience:0.8][run|running+out+of+patience|it:1.1][wit`s+end:0.5][conversation+before:0.5][over+and+over+again:1.1][go+all+that+again:1.1][times+before:0.5][so+many+times:0.8][recurring+issue|problem:0.5][broken+record:0.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"broken record\" OR \"recurring issue|problem\" OR \"so many times\" OR \"times before\" OR \"go all that again\" OR \"over and over again\" OR \"conversation before\" OR \"wit's end\" OR ( \"run|running out of patience|it\" ) OR \"out of patience\"  OR \"still dealing\" OR \"Sick and tired\" OR (\"this back forth\"  NOT NEAR:3 (transport*|drive*) ) OR \"running circles\" OR \"endless loop\" OR \"will it end\" OR \"never end|ending|ended\" OR \"lost cause\" OR \"burned out\" OR \"banging head|wall\"", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29891, "Name": "Bad mood", "Description": null, "ActionString": "[bad|terrible|horrible|foul+mood|disposition|temper:0.5]", "UserEntry": "(\"bad|terrible|horrible|foul mood|disposition|temper\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-17T16:38:20.44", "ComponentCount": 17}