{"BucketId": "127", "BucketFullname": "Categories.Quality Automation_Calls.Agent Confidence", "BucketDescription": "Agent shows lack of confidence when giving instruction or providing resolution to the customer.", "UserEditable": false, "BucketName": "Agent Confidence", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-05-08T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Agent Confidence", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2024-01-17T11:38:09.633-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-05-08T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 30, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32344, "Name": "Lack of Confidence", "Description": null, "ActionString": "<profile>[have+no+idea+why|what:0.7]=1[i+don`t+know+which|why:0.7]=1$OR$$NEAR:5$<deliver%><provider><healthcare><guys><assist><else><anything><shall><everything><cutting><breaking><side><wash><catholic><mismatched><number><mobile><home><my><military><censoring><provide><room><department><blocking><clear><hear%><exactly><address><%mail>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[better+assist:1.2][forget+password:1.2][will+put+here:1.2][ask+you:1][you+have|mean|rather|other|mention%:1][you+talk:0.6][you+do+it:0.4][correct+department:0.7][other+question%:1.2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[i|am|i`m+not+sure|confident|certain+if|why|how:0.7]=1$NOTNEAR:5$$OR$", "UserEntry": "\"i|am|i'm not sure|confident|certain if|why|how\":0.7=agent NOT NEAR:5 (\"other question*\":1.2 OR \"correct department\":0.7 OR \"you do it\":0.4 OR \"you talk\":0.6 OR \"you have|mean|rather|other|mention*\":1 OR \"ask you\":1 OR \"will put here\":1.2 OR \"forget password\":1.2 OR \"better assist\":1.2 OR (*mail|address|exactly|hear*|clear|blocking|department|room|provide|censoring|military|my|home|mobile|number|mismatched|catholic|wash|side|breaking|cutting|everything|shall|anything|else|assist|guys|healthcare|provider|deliver*)) OR  (\"i don't know which|why\":0.7 OR \"have no idea why|what\":0.7)=agent NEAR:5 (profile)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32344, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32344, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-08-06T10:48:07.633", "ComponentCount": 1}