{"Id": null, "Name": "test_import_filters", "Description": "this is to test import/export for mike d", "UseWeightedAverage": 0, "Status": "Active", "LastUser": "<EMAIL>", "LastModified": "2025-03-04T21:26:37", "Type": "Raw", "RetroStartDate": null, "Filters": [{"SearchComponentID": -21998, "BucketID": 89, "SectionID": null, "AlertID": null, "ColumnName": "", "TableFilterTypes": null, "FriendlyName": "", "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "ScoreComponents": [{"Id": null, "Name": "testind_cat1", "DisplayDescriptionsInCoach": false, "Description": "test indicator1 based on category", "SearchComponentId": null, "BucketId": 92, "BucketMissScore": 0.0, "BucketMissColor": "", "ColumnName": "", "CurrentlyActive": true, "LastUser": "<EMAIL>", "LastModified": "2025-03-04T21:26:37", "Weight": 1.0, "Type": "Raw", "Bins": [{"BinId": 565, "ScoreComponentId": null, "BinNumber": 1, "BinMin": null, "BinMax": 33.33, "BinValue": null, "BinColor": "#d90429", "BinFriendlyName": "Poor", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}, {"BinId": 566, "ScoreComponentId": null, "BinNumber": 2, "BinMin": 33.33, "BinMax": 66.67, "BinValue": null, "BinColor": "#ec752c", "BinFriendlyName": "Satisfactory", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}, {"BinId": 567, "ScoreComponentId": null, "BinNumber": 3, "BinMin": 66.67, "BinMax": null, "BinValue": null, "BinColor": "#fbdb7b", "BinFriendlyName": "Excellent", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}], "Ranges": [{"ScoreComponentId": null, "RangeMinimum": null, "RangeMaximum": null, "PointsMinimum": 100.0, "PointsMaximum": 100.0, "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-03T14:12:00"}], "Filters": [{"SearchComponentID": -21997, "BucketID": 90, "SectionID": null, "AlertID": null, "ColumnName": "", "TableFilterTypes": null, "FriendlyName": "", "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "DisplayOrder": 0, "SubjectiveMin": null, "SubjectiveMax": null, "SlnInstallationID": null, "IsManualRequired": false, "AdditionalOptions": "None"}, {"Id": null, "Name": "testind_measure", "DisplayDescriptionsInCoach": false, "Description": "", "SearchComponentId": null, "BucketId": null, "BucketMissScore": null, "BucketMissColor": "", "ColumnName": "Agitation", "CurrentlyActive": true, "LastUser": "<EMAIL>", "LastModified": "2025-03-04T21:26:37", "Weight": 1.0, "Type": "Raw", "Bins": [{"BinId": 568, "ScoreComponentId": null, "BinNumber": 1, "BinMin": null, "BinMax": 33.33, "BinValue": null, "BinColor": "#d90429", "BinFriendlyName": "Poor", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}, {"BinId": 569, "ScoreComponentId": null, "BinNumber": 2, "BinMin": 33.33, "BinMax": 66.67, "BinValue": null, "BinColor": "#ec752c", "BinFriendlyName": "Satisfactory", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}, {"BinId": 570, "ScoreComponentId": null, "BinNumber": 3, "BinMin": 66.67, "BinMax": null, "BinValue": null, "BinColor": "#fbdb7b", "BinFriendlyName": "Excellent", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}], "Ranges": [], "Filters": [], "DisplayOrder": 1, "SubjectiveMin": null, "SubjectiveMax": null, "SlnInstallationID": null, "IsManualRequired": false, "AdditionalOptions": "None"}, {"Id": null, "Name": "testind_manual", "DisplayDescriptionsInCoach": false, "Description": "manual indicators test", "SearchComponentId": null, "BucketId": null, "BucketMissScore": 0.0, "BucketMissColor": "", "ColumnName": "", "CurrentlyActive": true, "LastUser": "<EMAIL>", "LastModified": "2025-03-04T21:26:37", "Weight": 1.0, "Type": "Raw", "Bins": [{"BinId": 571, "ScoreComponentId": null, "BinNumber": 1, "BinMin": null, "BinMax": 33.0, "BinValue": 16.5, "BinColor": "#d90429", "BinFriendlyName": "Bin 1", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}, {"BinId": 572, "ScoreComponentId": null, "BinNumber": 2, "BinMin": 33.0, "BinMax": 66.0, "BinValue": 49.5, "BinColor": "#ec752c", "BinFriendlyName": "Bin 2", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}, {"BinId": 573, "ScoreComponentId": null, "BinNumber": 3, "BinMin": 66.0, "BinMax": null, "BinValue": 83.0, "BinColor": "#fbdb7b", "BinFriendlyName": "Bin 3", "BinDescription": "", "Target": false, "AutoFail": false, "Id": null}], "Ranges": [], "Filters": [], "DisplayOrder": 2, "SubjectiveMin": 0.0, "SubjectiveMax": 100.0, "SlnInstallationID": null, "IsManualRequired": true, "AdditionalOptions": "None"}], "Bins": [{"BinId": 211, "ScoreId": null, "BinNumber": 1, "BinMin": null, "BinMax": 33.0, "BinColor": "#d90429", "BinFriendlyName": "Poor", "Target": true, "Id": null}, {"BinId": 212, "ScoreId": null, "BinNumber": 2, "BinMin": 33.0, "BinMax": 66.0, "BinColor": "#ec752c", "BinFriendlyName": "Satisfactory", "Target": false, "Id": null}, {"BinId": 213, "ScoreId": null, "BinNumber": 3, "BinMin": 66.0, "BinMax": null, "BinColor": "#fbdb7b", "BinFriendlyName": "Excellent", "Target": false, "Id": null}], "HasPendingRetroRequest": false, "Created": "2025-03-04T21:26:37", "Creator": "<EMAIL>", "Version": null, "LanguageTag": "Unknown", "CoachEnabled": false, "FolderId": null, "SlnInstallationID": null, "DateInactive": null, "DisplayDescriptionsInCoach": false, "EffectiveDate": "2025-03-04T21:26:37", "Group": {"GroupName": null, "GroupID": null}, "LastNDays": null, "TimeFrame": "Yesterday"}