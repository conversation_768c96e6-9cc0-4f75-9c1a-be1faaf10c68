{"BucketId": "552", "BucketFullname": "Categories.Emotions.I Love it", "BucketDescription": "language indication extreme like for something", "UserEditable": false, "BucketName": "I Love it", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.I Love it", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-07T22:20:39.063-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -29993, "Name": "i love it", "Description": null, "ActionString": "[help|assist|put:0.2][don`t|can`t|not:0.2][i|i`d|would+love|adore|delighted|thrilled|glad|cherish+to|it|that|this:1]$NOTNEAR:0$$NOTBEFORE:0.3$", "UserEntry": "( \"i|i'd|would love|adore|delighted|thrilled|glad|cherish to|it|that|this\":1 NOT NEAR:0 ( \"don't|can't|not\") ) NOT BEFORE:0.3 \"help|assist|put\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-07T22:20:39.41", "ComponentCount": 1}