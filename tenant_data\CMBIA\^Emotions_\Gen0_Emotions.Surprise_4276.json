{"BucketId": "4276", "BucketFullname": "Categories.Emotions.Surprise", "BucketDescription": "Detects when surprise is present on a call.", "UserEditable": false, "BucketName": "Surprise", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Surprise", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-03-25T16:02:08.517-04:00", "Creator": "<EMAIL>", "Version": 5, "LanguageTag": "en", "EffectiveDate": "2025-03-25T16:02:21.323", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -21988, "Name": "Surprise Alert", "Description": "", "ActionString": "[very|so|absolutely|extra|especially|quite|really|super|extremely|highly+quicker|better|higher|sooner|earlier|faster|surprised|pleased|impressed|thrilled|delighted|impress|thrill|thriller|thrilling:0.5]", "UserEntry": "\"very|so|absolutely|extra|especially|quite|really|super|extremely|highly quicker|better|higher|sooner|earlier|faster|surprised|pleased|impressed|thrilled|delighted|impress|thrill|thriller|thrilling\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21987, "Name": "positive_sentiment-agree", "Description": "", "ActionString": "<agree%>", "UserEntry": "(agree*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21986, "Name": "positive_sentiment-comfortable", "Description": "", "ActionString": "[feel%+comfortable|portable:1]<eagerly>$OR$", "UserEntry": "((eagerly) OR (\"feel* comfortable|portable\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21985, "Name": "positive_sentiment-confident", "Description": "", "ActionString": "<confiden%>", "UserEntry": "(confiden*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21984, "Name": "positive_sentiment-makes sense", "Description": "", "ActionString": "<doesn’t><no>$OR$[makes+sense:1]$NOTNEAR:1$", "UserEntry": "((\"makes sense\":1) NOT NEAR:1 (no|doesn’t))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21983, "Name": "positive_sentiment-understand", "Description": "", "ActionString": "[what+you%+saying:1.4][totally+agree:1][tell+you+truth:1.4]<understood><understand%>$OR$$OR$$OR$$OR$", "UserEntry": "((understand*|understood) OR (\"tell you truth\":1.4) OR (\"totally agree\":1) OR (\"what you* saying\":1.4))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21982, "Name": "positive_sentiment-very easy", "Description": "", "ActionString": "[really|pretty|very|real+simple|easy:1]", "UserEntry": "(\"really|pretty|very|real simple|easy\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21981, "Name": "positive_sentiment-accomplished", "Description": "", "ActionString": "<accomplish%>", "UserEntry": "(accomplish*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21980, "Name": "positive_sentiment-attractive", "Description": "", "ActionString": "<mild><feminine><soothing><appealing><elaborate><sophisticated>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(sophisticated|elaborate|appealing|soothing|feminine|mild)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21979, "Name": "positive_sentiment-no worries", "Description": "", "ActionString": "<thank%>[not+worried:1][size+fits:1][no+worries|points|doubt|clue|thoughts|matter|complaints|bother%|mats|comments:1]$OR$$OR$$NOTNEAR:2$", "UserEntry": "(((\"no worries|points|doubt|clue|thoughts|matter|complaints|bother*|mats|comments\":1) OR (\"size fits\":1) OR (\"not worried\":1)) NOT NEAR:2 (thank*))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21978, "Name": "positive_sentiment-positive", "Description": "", "ActionString": "<positive%>", "UserEntry": "(positive*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-03-25T16:02:21.323", "ComponentCount": 11}