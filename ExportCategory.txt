ExportCategory.py - CallMiner Category Export Utility
=====================================================

OVERVIEW:
---------
ExportCategory.py is a comprehensive utility for exporting CallMiner categories and their associated data from the FEAPI system. This script provides flexible export capabilities with filtering, dependency analysis, and multiple output formats including Excel and JSON.

PRIMARY PURPOSE:
----------------
- Export category configurations from CallMiner FEAPI
- Analyze category dependencies (scores, alerts, other categories)
- Generate detailed reports with filtering capabilities
- Create backup copies of category configurations
- Support migration and configuration management workflows

KEY FEATURES:
-------------

1. FLEXIBLE FILTERING:
   - Export all categories or filter by specific criteria
   - Support for category name patterns and wildcards
   - Section-based filtering
   - Folder-based organization
   - Custom filter expressions

2. DEPENDENCY ANALYSIS:
   - Identifies which scores reference each category
   - Tracks alert dependencies on categories
   - Maps inter-category relationships
   - Generates dependency matrices for impact analysis

3. MULTIPLE OUTPUT FORMATS:
   - Excel workbooks with multiple worksheets
   - JSON files for programmatic processing
   - Structured data exports for migration
   - Human-readable reports with formatting

4. COMPREHENSIVE DATA EXPORT:
   - Category metadata (names, IDs, descriptions)
   - Search components and criteria
   - Filter configurations
   - Generation and version information
   - Folder and section associations

COMMAND LINE INTERFACE:
-----------------------
The script supports extensive command-line configuration:

Basic Usage:
python ExportCategory.py

Common Parameters:
--CategoryFilter: Filter categories by name pattern
--SectionFilter: Filter by section name
--OutputPrefix: Customize output file naming
--LogRoot: Specify log file directory
--ExportDependencies: Include dependency analysis
--ExportJSON: Generate JSON output files

Example Commands:
python ExportCategory.py --CategoryFilter "Quality.*" --ExportDependencies True
python ExportCategory.py --SectionFilter "Customer Service" --OutputPrefix "CS_Export"

CONFIGURATION:
--------------
Configuration is managed through INI files and command-line arguments:

INI File Structure (ExportCategory.py.INI):
[FEAPI]
API = feapi
Tenant = YourTenant
LogRoot = logs
DEBUG = False

[ExportCategory]
CategoryFilter = 
SectionFilter = 
OutputPrefix = Categories
ExportDependencies = True
ExportJSON = False

MAIN FUNCTIONS:
---------------

1. GetCategories(helper, config):
   - Retrieves all categories from FEAPI
   - Applies filtering based on configuration
   - Returns filtered category dataset
   - Handles pagination for large datasets

2. GetCategory(helper, config, category_data):
   - Retrieves detailed data for specific categories
   - Fetches complete category JSON including search components
   - Processes filter configurations
   - Handles error cases for missing categories

3. ParseFilters(helper, config, df_categories):
   - Analyzes category filter configurations
   - Identifies dependencies on other entities
   - Generates dependency tracking data
   - Creates relationship matrices

4. ExportToExcel(helper, config, data_dict):
   - Creates comprehensive Excel workbooks
   - Multiple worksheets for different data types
   - Professional formatting and styling
   - Dependency analysis visualizations

OUTPUT FILES:
-------------

1. Excel Workbook (Categories_[timestamp].xlsx):
   - Categories worksheet: Main category data
   - Dependencies worksheet: Relationship analysis
   - Filters worksheet: Filter configuration details
   - Summary worksheet: Export statistics

2. JSON Files (if enabled):
   - Individual category JSON files
   - Bulk export JSON with all categories
   - Dependency mapping JSON
   - Filter analysis JSON

3. Log Files:
   - Main log: Detailed operation log
   - Error log: Error messages and stack traces
   - Warning log: Warning messages and issues

WORKFLOW PROCESS:
-----------------

1. INITIALIZATION:
   - Load configuration from INI file and command line
   - Initialize logging system
   - Create FEAPI helper instance
   - Validate tenant connection

2. DATA RETRIEVAL:
   - Fetch all categories from FEAPI
   - Apply filtering criteria
   - Retrieve detailed category data
   - Process search components and filters

3. DEPENDENCY ANALYSIS:
   - Analyze category filter configurations
   - Identify references to scores, alerts, other categories
   - Build dependency matrices
   - Calculate impact analysis data

4. EXPORT GENERATION:
   - Create Excel workbook with multiple worksheets
   - Generate JSON files if requested
   - Apply formatting and styling
   - Save files with timestamp naming

5. CLEANUP AND REPORTING:
   - Display export statistics
   - Show any errors or warnings
   - Provide file location information
   - Clean up temporary resources

ERROR HANDLING:
---------------
- Comprehensive error logging with stack traces
- Graceful handling of API timeouts and failures
- Validation of category data integrity
- Recovery from partial export failures
- Clear error messages for troubleshooting

PERFORMANCE CONSIDERATIONS:
---------------------------
- Efficient batch processing of categories
- Optimized API calls to minimize requests
- Memory-efficient processing of large datasets
- Progress indicators for long-running operations
- Configurable timeout and retry settings

SECURITY FEATURES:
------------------
- Secure credential management
- No sensitive data in log files (configurable)
- Proper authentication token handling
- Audit trail of export operations

USE CASES:
----------

1. BACKUP AND RECOVERY:
   - Regular backups of category configurations
   - Disaster recovery preparation
   - Configuration versioning

2. MIGRATION SUPPORT:
   - Export from source environment
   - Data preparation for imports
   - Cross-tenant migrations

3. ANALYSIS AND REPORTING:
   - Category usage analysis
   - Dependency impact assessment
   - Configuration auditing

4. DEVELOPMENT AND TESTING:
   - Development environment setup
   - Test data preparation
   - Configuration comparison

INTEGRATION:
------------
- Works seamlessly with ImportCategory.py for round-trip operations
- Integrates with other FEAPI utilities
- Supports automation and scripting workflows
- Compatible with CI/CD pipelines

TROUBLESHOOTING:
----------------
Common issues and solutions:
- API connection failures: Check credentials and network connectivity
- Large dataset timeouts: Adjust timeout settings and use filtering
- Memory issues: Process categories in smaller batches
- Permission errors: Verify API user has appropriate access rights

This utility is essential for CallMiner administrators who need to manage, backup, analyze, or migrate category configurations across environments.
