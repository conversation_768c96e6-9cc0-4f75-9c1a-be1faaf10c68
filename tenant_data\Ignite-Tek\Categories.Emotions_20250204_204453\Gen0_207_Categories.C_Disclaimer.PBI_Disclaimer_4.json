{"BucketId": "207", "BucketFullname": "Categories.C_Disclaimer.PBI_Disclaimer_4", "BucketDescription": "1 800 medicare", "UserEditable": false, "BucketName": "PBI_Disclaimer_4", "SectionName": "C_Disclaimer", "SectionNameDisplay": "C_Disclaimer", "SectionId": 72, "RetroStartDate": null, "CategoryDisplay": "C_Disclaimer.PBI_Disclaimer_4", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-18T12:41:23.85-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-12-19T07:33:26.24", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-10-14T00:00:00", "EndDate": "2024-10-14T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -31338, "Name": "Disclaimer 5", "Description": null, "ActionString": "<medica%><800>$AND$[one|won+%eight+hundred+medicare:3][medicare:0.2]<800><one>$AND$$BEFORE:1.5$$OR$$OR$", "UserEntry": "((One 800) BEFORE:1.5 \"medicare\") OR \"one|won *eight hundred medicare\":3 OR (800 medica*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31042, "Name": "1", "Description": null, "ActionString": "<800>[eight+hundred:1.5]{one+eight+hundred+medicare:1.1}[one|won|eight|hundred+medicare:0.5]$OR$$OR$$OR$", "UserEntry": "\"one|won|eight| hundred medicare\" OR [one eight hundred medicare] OR \"eight hundred\":1.5 OR 800", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-19T07:33:29.47", "ComponentCount": 2}