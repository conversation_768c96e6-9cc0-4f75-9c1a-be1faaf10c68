# INPUT: categoryimportfilename, NewSectionName, NewCategoryName
# IF BLANK, append "imported_DateTime" to current SectionName or CategoryName

# Check to see if SectionAlready Exists, if not create it
# Check to see if CategoryName already exists, if so, append "imported_Datetime" to CategoryName

#Load Category JSON from File
#Alter Section name and Categoryname
#Set BucketID = null so it creates a new category
#Remove unnecessary JSON components

#Call Category Create API

'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''
import FEAPI_Helpers
import json
import os,sys
import pandas as pd
from pathlib import Path
from datetime import datetime
import configparser



class config:



    # Default values
    '''
    [FEAPI]
    '''
    API = "feapi"
    DataRoot = "tenant_data"
    LogRoot = "logs"
    DEBUG = False
    LoggerLevel = "DEBUG"

    '''
    [CreateCategory]
    '''
    Tenant = ""
    FolderName = ""
    NewSectionName=""
    Importer=None
    UseExistingSection = False
    GetDependencies = True
    IgnoreFilterDependencies = False
    OverwriteExistingCategories = False

    API_timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    json_output_dir = ""
    @staticmethod
    def FilenameInOutputFolder(filename):
        return os.path.join(config.json_output_dir, filename)

    @staticmethod
    def AllCategories_xls_filename():
        return config.FilenameInOutputFolder (  f"AllCategories.xlsx")

    @staticmethod
    def PyFilename():
        os.path.basename(sys.argv[0])

    @staticmethod
    def SetFolderName(foldername):
        config.FolderName = f"{config.DataRoot}/{foldername}"

    @staticmethod
    def read_ini_file(file_path:str=""):
        if file_path=="":
            file_path = f"{os.path.basename(sys.argv[0])}.INI"
        if os.path.exists(file_path):
            config.strip_lines_with_triple_single_quotes(file_path,file_path)
            configparse = configparser.ConfigParser()
            configparse.read(file_path)

            config.DEBUG = configparse.get('FEAPI', 'DEBUG').strip('"')  == 1
            config.LoggerLevel = configparse.get('FEAPI', 'LoggerLevel').strip('"')
            config.API = configparse.get('FEAPI', 'API').strip('"')
            config.Tenant = configparse.get('FEAPI', 'Tenant').strip('"')
            config.LogRoot = configparse.get('FEAPI', 'LogRoot').strip('"')
            config.DataRoot = configparse.get('FEAPI', 'DataRoot').strip('"')
        else:
            print(f"ERROR: INI file at {file_path} does not exist **************************")

    @staticmethod
    def strip_lines_with_triple_single_quotes(input_file: str, output_file: str):
        with open(input_file, 'r') as file:
            lines = file.readlines()
            # Filter out lines that are just ''' (including any whitespace)
        cleaned_lines = [line for line in lines if line.strip() != "'''"]
        # Write the cleaned lines to the output file
        with open(output_file, 'w') as file:
            file.writelines(cleaned_lines)

config.read_ini_file()



# LOGGING --------------------------------------------------------------------------------------
import logging
# Set up logging configuration
logger = logging.getLogger(config.PyFilename())
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

#reset logger level if passed in args
level_string = config.LoggerLevel.upper()  # Ensure case-insensitivity
level = logging.getLevelName(level_string)
if isinstance(level, int):  # Check if a valid level was found
    logging.getLogger().setLevel(level)
    print(f"Logging level set to: {logging.getLevelName(level)}")
else:
    print(f"Invalid logging level string passed as config: {level_string}")

# Create handlers and log folders
if not os.path.exists(config.LogRoot):
    os.makedirs(config.LogRoot)

logprefix = f"{config.LogRoot}/{config.PyFilename()}-{config.timestamp}"
file_handler = logging.FileHandler(f'{logprefix}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'{logprefix}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'{logprefix}.warnings.txt')  # Log to errors.txt

console_handler = logging.StreamHandler()  # Log to console


# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)



#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, config)



#API info
# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# for FISUS, use feapif.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token variable







def LoadCategoryXLS(xls_filename):
    df_categories = None
    if os.path.exists(xls_filename):
        try:
            df_categories = pd.read_excel(xls_filename, sheet_name="ALL Categories")
            logger.info(f"Successfully loaded 'ALL categories' sheet from {xls_filename}")
            # You can now work with the df_categories DataFrame
            # print(df_categories.head()) # Example: print the first few rows
        except FileNotFoundError:  # redundant check, but good practice
            logger.error(f"File not found: {xls_filename}")
        except KeyError:
            logger.error(f"Sheet 'categories' not found in {xls_filename}")
        except Exception as e:
            logger.error(f"An error occurred while loading the file: {e}")
    else:
        logger.error(f"File {xls_filename} does not exist.")

    return df_categories


def GetNewBucketId(df_oldbuckets, df_newbuckets, oldbucketid):
    # Step 1: Look up BucketFullname in df_oldbuckets
    old_bucket_fullname = df_oldbuckets.loc[df_oldbuckets['BucketId'] == oldbucketid, 'BucketFullname']

    if old_bucket_fullname.empty:
        raise ValueError(f"Old BucketId {oldbucketid} not found in df_oldbuckets.")

        # Extract the BucketFullname
    old_bucket_fullname_value = old_bucket_fullname.values[0]

    # Step 2: Look up BucketId in df_newbuckets using the found BucketFullname
    new_bucket_id = df_newbuckets.loc[df_newbuckets['BucketFullname'] == old_bucket_fullname_value, 'BucketId']

    if new_bucket_id.empty:
        raise ValueError(f"BucketFullname '{old_bucket_fullname_value}' not found in df_newbuckets.")

        # Return the first matching new BucketId
    return new_bucket_id.values[0]


# # Example usage
# try:
#     old_id = 2  # Example old bucket ID
#     new_id = GetNewBucketId(old_id)
#     print(f"The new BucketId for old BucketId {old_id} is {new_id}.")
# except ValueError as e:
#     print(e)



def CreateCategory(config, json_file,  df_sections, df_AllCategories, df_oldbuckets, df_newbuckets):
    # Load JSON data from the file
    data = helper.load_json_from_file(json_file)
    if data is not None:
        logger.info(f"Loaded JSON data for: {json_file}")
        # logger.info(json.dumps(data, indent=4))


    # Print the section and category names
    if config.NewSectionName =="":
        newsectionname = data['SectionName']
    else:
        newsectionname = config.NewSectionName

    #Create Section if it doens't already exist
    oldbucketfullname = data["BucketFullname"].replace("Categories.","")
    newbucketfullname = newsectionname+"."+data['BucketName']



    sectionid = GetSectionID(newsectionname, df_sections)
    if sectionid == None:
        #Section does not exist, try to create it
        sectionid = helper.API_CreateSection(newsectionname,config.Importer)
        if sectionid == None:
            logger.error(f"Unable to create new section ({newsectionname} in order to create category ({newbucketfullname} ")
        else:
            df_sections = helper.API_GetAllSections()

    logger.info(f"CREATING OLD CategoryName ({oldbucketfullname}) ---> NewCategoryName: {newbucketfullname}  ----------------------")



    # Update the JSON so it cretes a new bucekt or updates an existing bucket
    current_date_time = config.API_timestamp

    data['SectionId'] = sectionid
    data['SectionName'] = ""
    data['SectionNameDisplay'] = ""
    data['CategoryDisplay'] = ""
    data['BucketFullname'] = ""  # f"Categories.{data['SectionName']}.{data['BucketName']}"
    data['RetroStartDate'] = None
    data['Created'] = current_date_time
    data['EffectiveDate'] = current_date_time
    data['LastModified'] = current_date_time
    data['LastNDays'] = None
    data['TimeFrame'] = "Yesterday"
    # Update owner
    data['Creator'] = config.Importer
    data['Username'] = config.Importer

    # Update SearchComponent keys
    for sc in data['SearchComponents']:
        sc['Id'] = None

        filterstring, filterdeplist = helper.ParseFilters(sc['Filters'], df_AllCategories)

        if sc['Filters'] != []:
            # Add Filters for this search component to the output file
            filterfilename = os.path.join(config.json_output_dir, f"Import_Filters.xlsx")
            helper.LogFilter(newbucketfullname, sc['Name'], filterstring, filterfilename)
            logger.warning(
                f"Saving Filters for SearchComponent:{newbucketfullname}.{sc['Name']}.\tFilterString = {filterstring}.")

        #Convert Filters
        newfilters =[]
        for f in sc['Filters']:
            sc["SearchComponentID"]=None
            oldbucketid = f['BucketID']
            if oldbucketid != None:
                try:
                    newbucketid = GetNewBucketId(df_oldbuckets,df_newbuckets,oldbucketid)
                    f['BucketID'] = int(newbucketid)
                    newfilters.append(f)
                except ValueError as e:
                    logging.error(f"Could not find matching New BucketID to filter by in {f}")

        #sc['Filters'] = []


        # Check for Category Dependency
        matches, eqldeplist = helper.ParseEQL(sc['UserEntry'], df_AllCategories)

        if matches != "":
            eqldependfilename = os.path.join(config.json_output_dir, f"Import_EQLDependencies.xlsx")
            helper.LogEQLDependency(newbucketfullname, sc['Name'], matches, eqldependfilename)
            logger.warning(
                f"Saving EQL Dependencies found for SearchComponent:{newbucketfullname}.{sc['Name']}.\tEQLDependencies = {matches}")
        #else:
            #logger.info(f"No EQL Dependencies found for SearchComponent:{bucketfullname}.{sc['Name']}.\tEQLDependencies = {matches}")

    # Dump the importable Category JSON
    # Write JSON object to a file
    json_for_import_filename = os.path.join(config.json_output_dir, f"{helper.cleanfilename(newbucketfullname)}.json")
    with open(f"{json_for_import_filename}", 'w') as json_file_import:
        try:
            json.dump(data, json_file_import, indent=4)  # `indent=4` for pretty-printing
        except Exception as e:
            logger.error(f"Unable to save Import_json ({data}) as ({json_file_import}).")

    #Check that Bucket doesnt already exist
    catresult=None
    if (df_newbuckets['BucketFullname'] == newbucketfullname).any():
        if config.OverwriteExistingCategories:
            logger.info(f"Bucket ({newbucketfullname}) ALREADY EXISTS. Updating....")
            data['BucketId'] = int(helper.GetBucketID(newbucketfullname, df_newbuckets))

            # Import the category into the newly created section
            if config.DEBUG == False:
                catresult = helper.API_UpdateCategory(data)
        else:
            logger.warning(f"Bucket ({newbucketfullname}) ALREADY EXISTS. Skipping....")
            catresult = None
    else:
        logger.info(f"Bucket ({newbucketfullname}) will be created ....")
        data['BucketId'] = None

        # Import the category into the newly created section
        if config.DEBUG == False:
            catresult = helper.API_CreateCategory(data)

        if catresult != None:
            #Add new cat to newbuckets
            df_newbuckets.loc[len(df_newbuckets)] = [catresult, newbucketfullname]
            df_newbuckets.loc[len(df_newbuckets)] = [catresult, oldbucketfullname]

    return catresult


def GetSectionID(SectionName, df_sections):
    sectionid = None
    if SectionName in df_sections['SectionName'].values:
        sectionid = int(df_sections.loc[df_sections['SectionName'] == SectionName, 'SectionID'].iloc[0])
    return sectionid




# ----------------------------------------------------------------------------------------------------
def CreateCategories():

    #Try to load Categories from inputfolder
    xls_filename = os.path.join(config.FolderName, "AllCategories.xlsx")

    df_XLSCategories = LoadCategoryXLS(xls_filename)
    if df_XLSCategories.empty:
        logger.error(f"Unable to load Exported Category info from ({xls_filename}) - Exiting")
        exit(1)


    #Check to see if section_name already exists, if it does then rename it to OLD_timestamp
    sectionid=None
    df_sections = helper.API_GetAllSections()

    if config.NewSectionName =="":
        #Use section names from imported categories
        logger.info(f"NewSectionName is blank.  Using original Section Names to import")
        sectionid = None

    elif config.NewSectionName in df_sections['SectionName'].values:
        if config.UseExistingSection:
            sectionid = GetSectionID(config.NewSectionName,df_sections)
            logger.info(f"Using existing section ({config.NewSectionName }) as SectionID = {sectionid}")
        else:
            logger.error(f"Section ({config.NewSectionName }) already exists, and UseExistingSection = false. Exiting")
            exit(1)
    else:
        #Create a brand new section and put all categories in it
        sectionid = helper.API_CreateSection(config.NewSectionName , config.Importer)
        logger.info (f"Successfully created ({config.NewSectionName } as SectionID = {sectionid}")
        df_sections = helper.API_GetAllSections()


    # Create a Path object for the Imported JSON folder
    folder_path = Path(config.FolderName)
    json_files = folder_path.glob('*.json')
    json_files = list(json_files)
    logger.info (f"Found ({len(json_files)}) json files in {folder_path}...")


    # Get old BucketID
    df_oldbuckets = df_XLSCategories[['BucketId', 'BucketFullname']].copy()
    # Get New Bucketids
    df_AllCategories = helper.GetAllCategories()
    df_newbuckets = df_AllCategories[['BucketId', 'BucketFullname']].copy()



    # Iterate through the generator of json files
    i=1
    for json_file in json_files:
        logger.info(f"Processing ( {i} / {len(json_files)} ) file = {json_file} -------------------------------------------------")
        i = i+1

        CreateCategory(config, json_file, df_sections, df_XLSCategories, df_oldbuckets, df_newbuckets)



#---------------------------------------------------------------------------------------

import argparse

def str_to_bool(s):
    if s.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif s.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')

def main():

    # get cmdline args and overwrite defaults from INI
    config.read_ini_file()

    parser = argparse.ArgumentParser(description='Script to process folder and section names')
    parser.add_argument('--Tenant', type=str, help='Name of the tenant to double-check before importing')
    parser.add_argument('--FolderName', type=str, help='Name of the folder')
    parser.add_argument('--NewSectionName', nargs='?', type=str, default="", help='Name of the section to be created. Leave blank to use old section names from import')
    parser.add_argument('--Importer', nargs='?', type=str, default="null", help='Importer name (optional) = use null if section is to be public')
    parser.add_argument('--API', nargs='?', type=str, default="feapi", help='API (feapi/feapif) (optional)')
    parser.add_argument('--UseExistingSection',   type=str_to_bool, default=False, help='UseExistingSection=True if you want to add existing section')
    parser.add_argument('--OverwriteExistingCategories',  type=str_to_bool, default=False, help='OverwriteExistingCategories=True if you want to update an existing section')

    args = parser.parse_args()
    logger.info(f"ARGUMENTS PASSED:-------------------------------------------")
    logger.info("\n".join(FEAPI_Helpers.FEAPI_Helper.show_all_attributes_and_values(args)))

    # Loop through attributes of A and copy from B if they match
    FEAPI_Helpers.FEAPI_Helper.UpdateObjectA_WithB(config, args)

    # Global Helper functions ------------------------------------------------------------------------------
    global helper
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, config)

    # Check for blank Importer
    if config.Importer == None or config.Importer=="":
        config.Importer = f"{FEAPI_Helpers.FEAPI_Helper.get_windows_user_id()}@callminer.com"

    # Check if the tenant matches
    logger.info(f"Checking if API tenant matches target Config.Tenant ({config.Tenant})")
    config.tenant = helper.API_CheckTenantName(config.Tenant)

    # Check if FolderName exists
    config.SetFolderName(config.FolderName)
    if os.path.exists(config.FolderName) and os.path.isdir(config.FolderName):
        logger.info(f"Found input folder ({config.FolderName})")
        # Create a directory for JSON files
        config.json_output_dir = f"{config.FolderName}/JSON_for_import_into_{config.Tenant}_at_{config.timestamp}"

        # Create a directory for JSON files
        logger.info(f"Creating JSON import files in {config.json_output_dir}")
        os.makedirs(config.json_output_dir, exist_ok=True)

    else:
        logger.error(f"FolderName ({config.FolderName}) does not exist. Exiting...")
        exit(1)

    CreateCategories()


if __name__ == "__main__":
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")



