{"BucketId": null, "BucketFullname": "", "BucketDescription": "To identify periods of silences on the calls that the agent should have filled, or instances of agents leaving customers on hold for longer than two minutes.", "UserEditable": false, "BucketName": "Silence", "SectionName": "", "SectionNameDisplay": "", "SectionId": 2317, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T16:41:34", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T16:41:34", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Hold Over Two Minutes", "Description": null, "ActionString": "(quality automation_calls.customer exclusions)~silence:+120~$NOTNEAR:30$", "UserEntry": "SIL:[+120] NOT NEAR:30 (CAT:[Quality Automation_Calls.Customer Exclusions])", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Short Silences Excludes Hold", "Description": null, "ActionString": "[gonna+put+whole:1.4]<bear><stay%><patient><time><patience><patiently><long><apolog><sorry><check><consult><back><right><specialist><connect><you><thank%><line><return><transfer><supervisor><manager><moment><minute%><hi><mrs><mr><transfer><star><hello><wait%><holding><holds><hold>(customer experience.can you hold)(quality automation_calls.customer exclusions)$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$~silence:10-60~$NOTNEAR:20$", "UserEntry": "SIL:[10-60] NOT NEAR:20 (CAT:[Quality Automation_Calls.Customer Exclusions] OR CAT:[Customer Experience.Can you hold] OR hold|holds|holding|wait*|hello|star|transfer|mr|mrs|hi|minute*|moment|manager|supervisor|transfer|return|line|thank*|you|connect|specialist|right|back|consult|check|sorry|apolog|long|patiently|patience|time|patient|stay*|bear OR \"gonna put whole\":1.4)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Autopass", "Description": null, "ActionString": "~silence:3.9-10.1~", "UserEntry": "SIL:[3.9-10.1]", "SpeakerList": "", "Status": true, "Weight": 0.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T16:41:34", "ComponentCount": 3}