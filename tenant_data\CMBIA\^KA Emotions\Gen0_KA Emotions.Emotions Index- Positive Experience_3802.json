{"BucketId": "3802", "BucketFullname": "Categories.KA Emotions.Emotions Index- Positive Experience", "BucketDescription": "Words and phrases that indicate customer experience is positive", "UserEditable": false, "BucketName": "Emotions Index- Positive Experience", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotions Index- Positive Experience", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:49:58.52-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-02-28T09:27:55.293", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25423, "Name": "Appreciation", "Description": null, "ActionString": "<couldn`t><wouldn`t><don`t><weren`t><can`t><wasn`t><not><didn`t>$OR$$OR$$OR$$OR$$OR$$OR$$OR$<appreciate%><genrous>$OR$$NOTAFTER:1$", "UserEntry": "(genrous|appreciate*) NOT AFTER:1 (didn't|not|wasn't|can't|weren't|don't|wouldn't|couldn't)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25422, "Name": "Comfort or Agreement", "Description": null, "ActionString": "[totally+agree|understand%:1][not+worried:0.5][size+fits:0.5][no+worries|points|doubt|clue|thoughts|matter|complaints|bother%|mats|comments:0.5]<doesn’t><no>$OR$[makes+sense:0.5]$NOTNEAR:1$<don`t><not>$OR$[feel%+comfortable|portable:1]$NOTAFTER:1$<confiden%>[does|did|it+not:0.8]<doesn`t>$OR$[made|make|making|makes+feel+better|good:2]$NOTAFTER:1$[i+feel+better:1.8]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"i feel better\":1.8 OR \"made|make|making|makes feel better|good\":2 NOT AFTER:1 (doesn't OR \"does|did|it not\":.8) OR confiden* OR \"feel* comfortable|portable\":1 NOT AFTER:1 (not|don't) OR \"makes sense\" NOT NEAR:1.0 (no|doesn’t)  OR \"no worries|points|doubt|clue|thoughts|matter|complaints|bother*|mats|comments\":0.5|\"size fits\"|\"not worried\" OR \"totally agree|understand*\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25421, "Name": "Grateful Agent <PERSON>ing", "Description": null, "ActionString": "{cared+problem:1.2}[truly|really|he|she+cared:1.5][been+fortunate:1]<dead>[i`m|would|really|so|very|i`d|we`re+grateful|greatful|fortunate:1.5]$NOTBEFORE:1$$OR$$OR$$OR$", "UserEntry": "(\"i'm|would|really|so|very|i'd|we're grateful|greatful|fortunate\":1.5 NOT BEFORE:1 dead)  OR \"been fortunate\":1 OR \"truly|really|he|she cared\":1.5 OR [cared problem]:1.2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25420, "Name": "Polite and Professional", "Description": null, "ActionString": "<weren`t><aren`t><didn`t><isn`t><wasn`t><not>$OR$$OR$$OR$$OR$$OR$<respect%><sympathies><empathy><sympathy><mannered><tolerance><thoughtful><apologetic><polite><accommodating><courteous><gracious>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTAFTER:1$", "UserEntry": "(gracious|courteous|accommodating|polite|apologetic|thoughtful|tolerance|mannered|sympathy|empathy|sympathies|respect*) NOT AFTER:1 (not|wasn't|isn't|didn't|aren't|weren't)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25419, "Name": "Thanks for Patience", "Description": null, "ActionString": "<customer%>{thank%|appreciate+patient|patience:2}$NOTNEAR:2.5$", "UserEntry": "[thank*|appreciate patient|patience]:2 NOT NEAR:2.5 (customer*)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-02-28T09:27:55.293", "ComponentCount": 5}