{"BucketId": "96", "BucketFullname": "Categories.Quality Automation_Calls.Contextual Understandability", "BucketDescription": "To identify confusion on the call where the customer does not understand the agent.", "UserEditable": false, "BucketName": "Contextual Understandability", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-06-21T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Contextual Understandability", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 4.0, "HasPendingRetroRequest": false, "Created": "2023-12-13T05:03:49.5-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-06-21T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-09-12T00:00:00", "EndDate": "2024-09-18T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -32507, "Name": "Complicated", "Description": null, "ActionString": "[can|could+you+simplify:1.4][difficult+concept|comprehend:1.5][hard+to+grasp:1.5][it|it`s|its|that%|this|process|system|you%+appear%|extremely|kind%|pretty|really|seems|sound%|very|is|too+complicated|convoluted|confusing:2]$OR$$OR$$OR$", "UserEntry": "\"it|it's|its|that*|this|process|system|you* appear*|extremely|kind*|pretty|really|seems|sound*|very|is|too complicated|convoluted|confusing\":2 \nOR \"hard to grasp\":1.5 \nOR \"difficult concept|comprehend\":1.5\nOR \"can|could you simplify\":1.4", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32507, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32507, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32499, "Name": "Confusion", "Description": null, "ActionString": "[fall|fell|take|took+off:1.6][why|how+%not|stop%|%n`t+work%|read%:2][why+that+is|happen%:1.4][what%+going+on:1.3][what%+happen%:1]<app%><high><low>$OR$$OR$$OR$$OR$$OR$$OR$$OR$[can|it`s|its|that`s|was|what|what`s|very+confusing:0.9]=2[bit|completely|definitely|just|kind|kinda|little|pretty|quite|really|so|totally|very+baffled|baffling:1.2]=2[confused+as+to:1.2]=2[confusing|confuse%+about|because|sorry|me|everything|thing|part|which|when|why:0.8]=2<not>=2<figure>=2$OR$[i`m|i+bit|completely|definitely|getting|got|gets|just|kind|kinda|little|more|pretty|quite|really|so|sounds|too|two|very|this|that+confuse%|confusing%:2]=2$NOTNEAR:3$$OR$$OR$$OR$$OR$$NOTNEAR:3$", "UserEntry": "((\"i'm|i bit|completely|definitely|getting|got|gets|just|kind|kinda|little|more|pretty|quite|really|so|sounds|too|two|very|this|that confuse*|confusing*\":2 NOT NEAR:3 (figure|not) \nOR \"confusing|confuse* about|because|sorry|me|everything|thing|part|which|when|why\":0.8 \nOR \"confused as to\":1.2 \nOR \"bit|completely|definitely|just|kind|kinda|little|pretty|quite|really|so|totally|very baffled|baffling\":1.2 \nOR \"can|It's|its|that's|was|what|what's|very confusing\":0.9)=customer) NOT NEAR:3 ((low|high|app*) OR \"what* happen*\":1 OR \"what* going on\":1.3 OR \"why that is|happen*\":1.4 OR \"why|how *not|stop*|*n't work*|read*\":2 OR \"fall|fell|take|took off\":1.6)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32499, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32499, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32498, "Name": "Doesn`t Make Sense", "Description": null, "ActionString": "[why+%not|stop%|%n`t+work%|read%:2]{why+that+is|happen%:1.4}[what%+going+on:1.3][what%+happen%:1]{low%|high%|inaccurate|inconsistent|irregular+reading%:2}$OR$$OR$$OR$$OR$[that|said+makes+no+sense:2]=2<phone>=2<sensor>=2<does>=2$OR$$OR$[didn`t|doesn`t|don`t|try|trying|not|can`t|cant|cannot+make|making+sense%:2.5]=2$NOTNEAR:3$$OR$$NOTNEAR:2$", "UserEntry": "((\"didn't|doesn't|don't|try|trying|not|can't|cant|cannot make|making sense*\":2.5 NOT NEAR:3 (does|sensor|phone) \nOR \"that|said makes no sense\":2)=customer) NOT NEAR:2 ([low*|high*|inaccurate|inconsistent|irregular reading*]:2 OR \"what* happen*\":1 OR \"what* going on\":1.3 OR [why that is|happen*]:1.4 OR \"why *not|stop*|*n't work*|read*\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32498, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32498, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32497, "Name": "Explain", "Description": null, "ActionString": "[there+was+not|no+explanation%|explain%:3][explan%+not|wasn`t+given|provide%|shared|said|told:2][i|i`ve+misunderstood:2][would+you+explain+to+me:2][can|could|will|would+you+explain+again:2]<allow><expedite>$OR$[if+you+can|could|will|would+explain:2]$NOTNEAR:2$[can|could|will|would+you+explain+again|everything|how|it|once|one|more|that|this|what|why:2][can`t|cant|cannot|couldn`t+explain+to+me:2][can|could+not+explain+to+me:2][would+you+be+able+explain:2][want|need+me+to:1.2][go+over+it|this|that+again:2.5]$NOTNEAR:3$[explain+once+more:2][explain+me+again:1.5]<lines><three>$OR$[apologize|apologies|please|sorry+explain+again:2]$NOTNEAR:3$[he|she|they+can:1][explain+best|better:1.5]$NOTNEAR:3$[best|better+able+explain:2][explain+over|you+again:2.5][sorry+explain+again:2][wasn`t+able+explain:2][can|could+not+explain:2][can`t|cant|cannot|couldn`t+explain:2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"can't|cant|cannot|couldn't explain\":2 \nOR \"can|could not explain\":2 \nOR \"wasn't able explain\":2 \nOR \"sorry explain again\":2 \nOR \"explain over|you again\":2.5 \nOR \"best|better able explain\":2 \nOR \"explain best|better\":1.5 NOT NEAR:3 \"he|she|they can\":1 \nOR \"apologize|apologies|please|sorry explain again\":2 NOT NEAR:3 (three|lines) \nOR \"explain me again\":1.5 \nOR \"explain once more\":2 \nOR \"go over it|this|that again\":2.5 NOT NEAR:3 \"want|need me to\":1.2\nOR \"would you be able explain\":2 \nOR \"can|could not explain to me\":2 \nOR \"can't|cant|cannot|couldn't explain to me\":2 \nOR \"can|could|will|would you explain again|everything|how|it|once|one|more|that|this|what|why\":2 \nOR \"if you can|could|will|would explain\":2 NOT NEAR:2 (expedite|allow)\nOR \"can|could|will|would you explain again\":2 \nOR \"would you explain to me\":2 \nOR \"i|i've misunderstood\":2 \nOR \"explan* not|wasn't given|provide*|shared|said|told\":2 \nOR \"there was not|no explanation*|explain*\":3", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32497, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32497, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32496, "Name": "Not Following/Understanding", "Description": null, "ActionString": "[why+%not|stop%|%n`t+work%|read%:2]{why+that+is|happen%:1.4}[what%+going+on:1.3][what%+happen%:1][say+again:1]<english><repeat><breaking><line><high><low><you><me><word><voice><accident><accent><ask%><saying><gonna><said><pardon><hearing><hear>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[%n`t|cannot+quite+understand%:2]=2[%n`t|cannot+understand%+what+mean|means:2.3]=2[sorry+didn`t|not|can`t+understand+what|that+mean%|is:1.5]=2<cants>=2<sensor%>=2<seen>=2<happened>=2<tell>=2$OR$$OR$$OR$$OR$[cannot|couldn`t|can`t|cant|didn`t|difficult%|doesn`t|don`t|impossible|trouble+understand%+that|what:1.2]=2$NOTNEAR:4$[not+quite|really|sure+understand%:1.5]=2<cants>=2<sensor%>=2<seen>=2<happened>=2<tell>=2$OR$$OR$$OR$$OR$[i+didn`t+understand+what|that:2]=2$NOTNEAR:4$<break%>=2<hear>=2<message>=2<phone>=2<line>=2$OR$$OR$$OR$$OR$[unable|difficult|hard+follow|understand:2]=2$NOTNEAR:5$<off>=2[not+following+you:1]=2$NOTNEAR:2$[cannot|cant|can`t|not+really+follow|following:2]=2$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTNEAR:5$", "UserEntry": "((\"cannot|cant|can't|not really follow|following\":2\nOR \"not following you\":1 NOT NEAR:2 (off)\nOR \"unable|difficult|hard follow|understand\":2 NOT NEAR:5 (line|phone|message|hear|break*)\nOR \"i didn't understand what|that\":2 NOT NEAR:4 (tell|happened|seen|sensor*|cants)\nOR \"not quite|really|sure understand*\":1.5\nOR \"cannot|couldn't|can't|cant|didn't|difficult*|doesn't|don't|impossible|trouble understand* that|what\":1.2 NOT NEAR:4 (tell|happened|seen|sensor*|cants)\nOR \"sorry didn't|not|can't understand what|that mean*|is\":1.5\nOR \"*n't|cannot understand* what mean|means\":2.3\nOR \"*n't|cannot quite understand*\":2)=customer) \nNOT NEAR:5 ((hear|hearing|pardon|said|gonna|saying|ask*|accent|accident|voice|word|me|you|low|high|line|breaking|repeat|english) OR \"say again\":1 OR \"what* happen*\":1 OR \"what* going on\":1.3 OR [why that is|happen*]:1.4 OR \"why *not|stop*|*n't work*|read*\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32496, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32496, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32494, "Name": "Unclear", "Description": null, "ActionString": "[what%+that+suppose%+be:2.2][not+sure+understand|understood:2][not+sure+i`m|i|am+following:2][confused+by+your+statement:2][not+following+you:2][i|i`ve+lost+track+saying|said|point|say:2.2][i`m|am+puzzled|baffled:1.5]<stage><library><number><code><error>$OR$$OR$$OR$$OR$[don`t|not|wouldn`t+know+what+mean%:2.2]$NOTNEAR:3$[not+sure+what+mean%:2.2][what+does+that|this|it+mean|means:2][what+do+they|you+mean%:2][no+idea+what%+going+on:2][no+idea+what%+he`s|she`s|they|they`re|you|your|you`re+asking|doing|mean|meant|referring:2]<pronunciation><enunaciation><breaking><connection><phone><line>$OR$$OR$$OR$$OR$$OR$[it`s|that`s|very|really+not|un|um+clear:1]$NOTNEAR:5$[you|you`re+not+very+clear:2][you`re|your+not+being+clear:2][unclear+to+me:1.2][extremely|really|still|very+unclear:1.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"extremely|really|still|very unclear\":1.5 \nOR \"unclear to me\":1.2 \nOR \"you're|your not being clear\":2 \nOR \"you|you're not very clear\":2 \nOR \"it's|that's|very|really not|un|um clear\":1 NOT NEAR:5 (line|phone|connection|breaking|enunaciation|pronunciation) \nOR \"no idea what* he's|she's|they|they're|you|your|you're asking|doing|mean|meant|referring\":2 \nOR \"no idea what* going on\":2 \nOR \"what do they|you mean*\":2 \nOR \"what does that|this|it mean|means\":2 \nOR \"not sure what mean*\":2.2 \nOR \"don't|not|wouldn't know what mean*\":2.2 NOT NEAR:3 (error|code|number|library|stage)\nOR \"i'm|am puzzled|baffled|\":1.5\nOR \"i|i've lost track saying|said|point|say\":2.2\nOR \"not following you\":2\nOR \"confused by your statement\":2\nOR \"not sure i'm|i|am following\":2\nOR \"not sure understand|understood\":2\nOR \"what* that suppose* be\":2.2", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32494, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32494, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-09-19T07:57:21.097", "ComponentCount": 6}