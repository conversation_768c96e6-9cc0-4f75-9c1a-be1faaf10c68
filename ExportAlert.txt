ExportAlert.py - CallMiner Alert Export Utility
===============================================

OVERVIEW:
---------
ExportAlert.py is a specialized utility for exporting CallMiner alerts and their configurations from the FEAPI system. This script provides comprehensive alert export capabilities with filtering, dependency analysis, and multiple output formats to support backup, migration, and analysis workflows.

PRIMARY PURPOSE:
----------------
- Export alert configurations from CallMiner FEAPI
- Analyze alert dependencies and trigger conditions
- Generate detailed reports with filtering capabilities
- Create backup copies of alert configurations
- Support migration and configuration management workflows
- Provide alert usage analysis and documentation

KEY FEATURES:
-------------

1. COMPREHENSIVE ALERT EXPORT:
   - Export all alerts or filter by specific criteria
   - Include complete alert configurations and metadata
   - Capture trigger conditions and thresholds
   - Export notification settings and recipients
   - Include scheduling and frequency information

2. FLEXIBLE FILTERING OPTIONS:
   - Filter by alert name patterns
   - Filter by folder organization
   - Filter by alert type or severity
   - Filter by active/inactive status
   - Custom filter expressions and conditions

3. DEPENDENCY TRACKING:
   - Identifies scores used in alert triggers
   - Tracks category dependencies in alert conditions
   - Maps relationships between alerts
   - Analyzes impact of configuration changes

4. MULTIPLE OUTPUT FORMATS:
   - Excel workbooks with detailed worksheets
   - JSON files for programmatic processing
   - CSV exports for data analysis
   - Human-readable documentation reports

COMMAND LINE INTERFACE:
-----------------------
The script supports extensive command-line configuration:

Basic Usage:
python ExportAlert.py

Common Parameters:
--AlertFilter: Filter alerts by name pattern
--FolderFilter: Filter by folder name
--OutputPrefix: Customize output file naming
--LogRoot: Specify log file directory
--ExportDependencies: Include dependency analysis
--ExportJSON: Generate JSON output files
--IncludeInactive: Include inactive/disabled alerts

Example Commands:
python ExportAlert.py --AlertFilter "Quality.*" --ExportDependencies True
python ExportAlert.py --FolderFilter "Customer Service" --OutputPrefix "CS_Alerts"
python ExportAlert.py --IncludeInactive False --ExportJSON True

CONFIGURATION:
--------------
Configuration is managed through INI files and command-line arguments:

INI File Structure (ExportAlert.py.INI):
[FEAPI]
API = feapi
Tenant = YourTenant
LogRoot = logs
DEBUG = False

[ExportAlert]
AlertFilter = 
FolderFilter = 
OutputPrefix = Alerts
ExportDependencies = True
ExportJSON = False
IncludeInactive = True

MAIN FUNCTIONS:
---------------

1. GetAlerts(helper, config):
   - Retrieves all alerts from FEAPI
   - Applies filtering based on configuration
   - Returns filtered alert dataset
   - Handles folder organization and hierarchy

2. GetAlert(helper, config, alert_data):
   - Retrieves detailed data for specific alerts
   - Fetches complete alert JSON including triggers
   - Processes notification configurations
   - Handles error cases for missing alerts

3. ParseAlertFilters(helper, config, df_alerts):
   - Analyzes alert trigger configurations
   - Identifies dependencies on scores and categories
   - Generates dependency tracking data
   - Creates relationship matrices

4. ExportToExcel(helper, config, data_dict):
   - Creates comprehensive Excel workbooks
   - Multiple worksheets for different data types
   - Professional formatting and styling
   - Alert analysis and dependency visualizations

OUTPUT FILES:
-------------

1. Excel Workbook (Alerts_[timestamp].xlsx):
   - Alerts worksheet: Main alert data and configurations
   - Dependencies worksheet: Relationship analysis
   - Triggers worksheet: Trigger condition details
   - Notifications worksheet: Notification settings
   - Folders worksheet: Folder organization structure
   - Summary worksheet: Export statistics and metrics

2. JSON Files (if enabled):
   - Individual alert JSON files with complete configurations
   - Bulk export JSON with all alerts
   - Dependency mapping JSON
   - Trigger analysis JSON

3. CSV Files (if enabled):
   - Alert summary data for analysis
   - Dependency relationships
   - Usage statistics and metrics

4. Log Files:
   - Main log: Detailed operation log
   - Error log: Error messages and stack traces
   - Warning log: Warning messages and issues

WORKFLOW PROCESS:
-----------------

1. INITIALIZATION:
   - Load configuration from INI file and command line
   - Initialize logging system with multiple handlers
   - Create FEAPI helper instance
   - Validate tenant connection and permissions

2. DATA RETRIEVAL:
   - Fetch all alerts from FEAPI with folder information
   - Apply filtering criteria based on configuration
   - Retrieve detailed alert configurations
   - Process trigger conditions and notification settings

3. DEPENDENCY ANALYSIS:
   - Analyze alert configurations for score/category references
   - Identify relationships with other alerts and entities
   - Build dependency matrices and impact analysis
   - Calculate usage statistics and metrics

4. EXPORT GENERATION:
   - Create Excel workbook with multiple formatted worksheets
   - Generate JSON files if requested
   - Apply professional formatting and styling
   - Save files with timestamp-based naming

5. CLEANUP AND REPORTING:
   - Display comprehensive export statistics
   - Show any errors or warnings encountered
   - Provide file location information
   - Clean up temporary resources and connections

ALERT DATA ELEMENTS:
--------------------
The export includes comprehensive alert information:

- Basic Properties: ID, Name, Description, Folder
- Configuration: Active status, Priority, Severity
- Triggers: Conditions, Thresholds, Logic operators
- Notifications: Recipients, Methods, Templates
- Scheduling: Frequency, Time windows, Delays
- Dependencies: Referenced scores, categories, alerts
- Metadata: Creation date, Last modified, Owner
- Statistics: Trigger history, Performance metrics

ERROR HANDLING:
---------------
- Comprehensive error logging with detailed stack traces
- Graceful handling of API timeouts and connection failures
- Validation of alert data integrity and completeness
- Recovery from partial export failures
- Clear error messages for troubleshooting and resolution

PERFORMANCE CONSIDERATIONS:
---------------------------
- Efficient batch processing of large alert collections
- Optimized API calls to minimize network requests
- Memory-efficient processing of complex alert configurations
- Progress indicators for long-running export operations
- Configurable timeout and retry settings for reliability

SECURITY FEATURES:
------------------
- Secure credential management using environment variables
- No sensitive data exposure in log files (configurable)
- Proper authentication token handling and refresh
- Audit trail of export operations for compliance

USE CASES:
----------

1. BACKUP AND RECOVERY:
   - Regular backups of alert configurations
   - Disaster recovery preparation and testing
   - Configuration versioning and change tracking

2. MIGRATION SUPPORT:
   - Export from source environment for migration
   - Data preparation for cross-tenant migrations
   - Environment synchronization and updates

3. ANALYSIS AND REPORTING:
   - Alert usage analysis and optimization
   - Dependency impact assessment
   - Configuration auditing and compliance

4. DEVELOPMENT AND TESTING:
   - Development environment setup and configuration
   - Test data preparation and validation
   - Configuration comparison and validation

INTEGRATION:
------------
- Works seamlessly with ImportAlert.py for round-trip operations
- Integrates with other FEAPI utilities in the suite
- Supports automation and scripting workflows
- Compatible with CI/CD pipelines and DevOps processes

TROUBLESHOOTING:
----------------
Common issues and solutions:
- API connection failures: Verify credentials and network connectivity
- Large dataset timeouts: Adjust timeout settings and use filtering
- Memory issues with large alerts: Process in smaller batches
- Permission errors: Verify API user has appropriate read access
- Missing dependencies: Ensure all referenced entities exist

This utility is essential for CallMiner administrators who need to manage, backup, analyze, or migrate alert configurations across different environments and tenants.
