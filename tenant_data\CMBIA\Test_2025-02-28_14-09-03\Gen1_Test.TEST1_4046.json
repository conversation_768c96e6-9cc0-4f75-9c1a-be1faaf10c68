{"BucketId": "4046", "BucketFullname": "Categories.Test.TEST1", "BucketDescription": "test of dependencies for <PERSON><PERSON><PERSON>", "UserEditable": false, "BucketName": "TEST1", "SectionName": "Test", "SectionNameDisplay": "Test", "SectionId": 2251, "RetroStartDate": null, "CategoryDisplay": "Test.TEST1", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-01-29T11:57:50.283-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-28T14:08:54.74", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 1, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -22022, "Name": "testSC1_updated", "Description": null, "ActionString": "<test>", "UserEntry": "test", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -22022, "BucketID": 90, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-28T14:08:57.35", "ComponentCount": 1}