{"BucketId": "562", "BucketFullname": "Categories.Emotions.Can`t talk right now", "BucketDescription": "Outcome based emotion of inconvient time to call", "UserEditable": false, "BucketName": "Can`t talk right now", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.Can`t talk right now", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-16T19:01:12.467-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 30, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -29890, "Name": "in middle of", "Description": null, "ActionString": "[in+middle|midst|thick|overwhelmed|busy+of|with:0.8]", "UserEntry": "\"in middle|midst|thick|overwhelmed|busy of|with\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29889, "Name": "can`t talk", "Description": null, "ActionString": "[can`t+talk+right|now:0.8]", "UserEntry": "\"can't talk right|now\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29888, "Name": "I`m driving", "Description": null, "ActionString": "[i`m|am+in+car|truck|vehicle:0.8][i`m|am+driving:0.5]$OR$", "UserEntry": "\"i'm|am driving\" OR \"i'm|am in car|truck|vehicle\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29887, "Name": "Inconvenient Time", "Description": null, "ActionString": "[is+a+better|convenient+time:1.1][not+convenient+time:0.8][not+free+right+now:1.1][not:0.2][i`m|am+working|busy:0.5]$NOTNEAR:0$$OR$$OR$$OR$", "UserEntry": "(\"i'm|am working|busy\" NOT NEAR:0 \"not\") OR (\"not free right now\") OR (\"not convenient time\") OR (\"is a better|convenient time\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29886, "Name": "inconvenient place", "Description": null, "ActionString": "[not|can`t:0.2][i`m|am+at+work|office|desk|gym|dinner|lunch|breakfast|table|church:0.8]$NOTNEAR:0$", "UserEntry": "(\"i'm|am at work|office|desk|gym|dinner|lunch|breakfast|table|church\" NOT NEAR:0 \"not|can't\" )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-16T19:01:14.2", "ComponentCount": 5}