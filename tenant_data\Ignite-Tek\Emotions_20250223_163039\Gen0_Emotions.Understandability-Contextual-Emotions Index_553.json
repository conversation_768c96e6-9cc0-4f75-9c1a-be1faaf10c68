{"BucketId": "553", "BucketFullname": "Categories.Emotions.Understandability-Contextual-Emotions Index", "BucketDescription": "Added as negative sentiment", "UserEditable": false, "BucketName": "Understandability-Contextual-Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.Understandability-Contextual-Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-08T13:29:23.333-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -29987, "Name": "Doesnt make sense", "Description": null, "ActionString": "[not|can`t|cant|cannot+make|making+sense:1.5][it|really|that|literally+makes|making+no+sense:1.5][didn`t|doesn`t|don`t|try|trying|wouldn`t+make+sense:1.5]$OR$$OR$", "UserEntry": "(\"didn't|doesn't|don't|try|trying|wouldn't make sense\":1.5 OR \"it|really|that|literally makes|making no sense\":1.5 OR \"not|can't|cant|cannot make|making sense\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29986, "Name": "I`m confused", "Description": null, "ActionString": "<confusion>[can|it`s|is|its|that`s|was|what|what`s+confusing:1][bit|completely|definitely|just|kind|kinda|little|pretty|quite|really|so|totally|very+baffled|baffling:1.5][confused+don`t+know:1][confused+as+to:1][confusing|confuse%+about|because|sorry|you|me|everything|thing|part|which|when|why|that:1][bit|completely|definitely|getting|got|gets|just|kind|kinda|little|more|pretty|quite|really|so|sounds|totally|too|two|very+confuse%|confusing:1.2][i`m|i|it`s|its+confus%:1]$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"i'm|i|it's|its confus*\":1 OR \"bit|completely|definitely|getting|got|gets|just|kind|kinda|little|more|pretty|quite|really|so|sounds|totally|too|two|very confuse*|confusing\":1.2 OR \"confusing|confuse* about|because|sorry|you|me|everything|thing|part|which|when|why|that\":1 OR \"confused as to\":1 OR \"confused don't know\":1 OR \"bit|completely|definitely|just|kind|kinda|little|pretty|quite|really|so|totally|very baffled|baffling\":1.5 OR \"can|It's|is|its|that's|was|what|what's confusing\":1 OR confusion)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29985, "Name": "Don`t know what you are talking about", "Description": null, "ActionString": "[he`s|she`s|they|they`re|their|you|your|you`re+asking|referring|saying|said|speaking|talking:0.8][can`t|cant|cannot|couldn`t|don`t|doesn`t|didn`t|not|wasn`t|isn`t+clear|follow|following|getting|know|seeing|see|sure+what%:1.5]$BEFORE:2$", "UserEntry": "((\"can't|cant|cannot|couldn't|don't|doesn't|didn't|not|wasn't|isn't clear|follow|following|getting|know|seeing|see|sure what*\":1.5) BEFORE:2 (\"he's|she's|they|they're|their|you|your|you're asking|referring|saying|said|speaking|talking\":0.8))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29984, "Name": "Nobody explained", "Description": null, "ActionString": "[should%+explain%|informed|told+it|me|that:1.5][there+was+not|no+explanation%|explain%:1.5][explan%+not|wasn`t|never+given|provide%|shared|said|told|mentioned:1.5][no+one+explain%|told|informed:1.5][never|nobody%+explain%|informed|told:1.5]$OR$$OR$$OR$$OR$", "UserEntry": "(\"never|nobody* explain*|informed|told\":1.5 OR \"no one explain*|told|informed\":1.5 OR \"explan* not|wasn't|never given|provide*|shared|said|told|mentioned\":1.5 OR \"there was not|no explanation*|explain*\":1.5 OR \"should* explain*|informed|told it|me|that\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29983, "Name": "Not understanding", "Description": null, "ActionString": "[cannot|couldn`t|can`t|cant|didn`t|difficult%|doesn`t|don`t|hard|impossible|trouble|unable+understand%:1][not+quite|really|sure+understanding:1.5]$OR$<trying>[am|could|can|did|do|does|i`m|really|still|she|she`s|he|he`s|we|we`re|they|they`re+not+understand%:1.5]$NOTNEAR:0$$OR$", "UserEntry": "(((\"am|could|can|did|do|does|i'm|really|still|she|she's|he|he's|we|we're|they|they're not understand*\":1.5) NOT NEAR:0 (trying)) OR (\"not quite|really|sure understanding\":1.5 OR \"cannot|couldn't|can't|cant|cannot|didn't|difficult*|doesn't|don't|hard|impossible|trouble|unable understand*\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29982, "Name": "What are you saying", "Description": null, "ActionString": "[he`s|she`s|they|they`re|their|you|your|you`re+asking|referring|saying|said|speaking|talking:1.2][can`t|cant|cannot|couldn`t|don`t|doesn`t|didn`t|not|wasn`t|unable+clear|follow|following|getting|know|seeing|see|sure+what%:1.5]$BEFORE:2$[no+idea|clue+what%+going+on:1.5][no+idea|clue+what%+he`s|she`s|they|they`re|you|your|you`re|he|she+asking|doing|mean|meant|referring|saying|talking:2]$OR$$OR$", "UserEntry": "((\"no idea|clue what* he's|she's|they|they're|you|your|you're|he|she asking|doing|mean|meant|referring|saying|talking\":2 OR \"no idea|clue what* going on\":1.5) OR ((\"can't|cant|cannot|couldn't|don't|doesn't|didn't|not|wasn't|unable clear|follow|following|getting|know|seeing|see|sure what*\":1.5) BEFORE:2 (\"he's|she's|they|they're|their|you|your|you're asking|referring|saying|said|speaking|talking\":1.2)))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29981, "Name": "What do You mean", "Description": null, "ActionString": "<i>[don`t|not|wouldn`t+know+what+it|they|this|that|we|you+mean|means|meant:1.5][not+sure+what+that|this|it|you+mean|means|meant:1.5][what+does+that|this|it+mean:1.5][what+do+they|you+mean:1.5]$OR$$OR$$OR$$NOTNEAR:0$", "UserEntry": "((\"what do they|you mean\":1.5 OR \"what does that|this|it mean\":1.5 OR \"not sure what that|this|it|you mean|means|meant\":1.5 OR \"don't|not|wouldn't know what it|they|this|that|we|you mean|means|meant\":1.5) NOT NEAR:0 (I))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29980, "Name": "Complicated Topic", "Description": null, "ActionString": "[hard+to|get+head+%round:1.5][difficult+concept:1.5][hard+to+grasp:1.5][it|it`s|its|that%|this+appear%|extremely|kind%|pretty|really|seems|sound%|very+complicated|convoluted|complicating:1.5]$OR$$OR$$OR$", "UserEntry": "(\"it|it's|its|that*|this appear*|extremely|kind*|pretty|really|seems|sound*|very complicated|convoluted|complicating\":1.5 OR \"hard to grasp\":1.5 OR \"difficult concept\":1.5 OR \"hard to|get head *round\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29979, "Name": "Unclear", "Description": null, "ActionString": "[cannot|couldn`t|can`t|cant|didn`t|difficult%|doesn`t|don`t|hard|impossible|trouble|unable+understand%:1][not+quite|really|sure+understanding:1.5]$OR$<trying>[am|could|can|did|do|does|i`m|really|still|she|she`s|he|he`s|we|we`re|they|they`re+not+understand%:1.5]$NOTNEAR:0$$OR$", "UserEntry": "(((\"am|could|can|did|do|does|i'm|really|still|she|she's|he|he's|we|we're|they|they're not understand*\":1.5) NOT NEAR:0 (trying)) OR (\"not quite|really|sure understanding\":1.5 OR \"cannot|couldn't|can't|cant|cannot|didn't|difficult*|doesn't|don't|hard|impossible|trouble|unable understand*\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29978, "Name": "Not Following", "Description": null, "ActionString": "[unable|difficult|hard+to+follow:1.5][not+following+you:1.5][cannot|cant|can`t|not|unable+really+follow|following:2]$OR$$OR$", "UserEntry": "(\"cannot|cant|can't|not|unable really follow|following\":2 OR \"not following you\":1.5 OR \"unable|difficult|hard to follow\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29977, "Name": "Further Explanation", "Description": null, "ActionString": "<number><credit><card>[your+name:0.5]$OR$$OR$$OR$[tell+me+again:1]$NOTNEAR:0$[would+you+explain+to+me:1.5][would+you+be+able+explain:1.5][go+over+it|this|that+again:2][explain|say|tell+once+more:1.5][repeat|say|tell+one+more+time:1.5][explain|say|tell+it|that|this+again:1.2][can|could|will|would+you+explain|say|tell+again:1.5][apologize|apologies|please|sorry+explain|give|say|tell+again:1.5][explain+best|better:1][best|better+able+explain:1.2][can|could|will|would+you+able|just+explain:1.5][if+you+can|could|will|would+explain:1.5][can|could|will+you+explain+to+me:1.5][can|could|will|would+you+explain+again|everything|how|it|once|one|more|that|this|what|why:1.5][explain+over|you+again:1.5][sorry+explain+again:1.2][try|trying|tried+explain|explaining:1.2][wasn`t+able+explain:1.2][can`t|cant|cannot|couldn`t|unable+explain+to+me:1.5][can|could+not+explain+to+me:1.5][can|could+not+explain+it|that|why:1.5][can`t|cant|cannot|couldn`t|unable+explain+it|that|why|reason:1.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "((\"can't|cant|cannot|couldn't|unable explain it|that|why|reason\":1.5 OR \"can|could not explain it|that|why\":1.5 OR \"can|could not explain to me\":1.5 OR \"can't|cant|cannot|couldn't|unable explain to me\":1.5 OR \"wasn't able explain\":1.2 OR \"try|trying|tried explain|explaining\":1.2 OR \"sorry explain again\":1.2 OR \"explain over|you again\":1.5 OR \"can|could|will|would you explain again|everything|how|it|once|one|more|that|this|what|why\":1.5 OR \"can|could|will you explain to me\":1.5 OR \"if you can|could|will|would explain\":1.5 OR \"can|could|will|would you able|just explain\":1.5 OR \"best|better able explain\":1.2 OR \"explain best|better\":1 OR \"apologize|apologies|please|sorry explain|give|say|tell again\":1.5 OR \"can|could|will|would you explain|say|tell again\":1.5 OR \"explain|say|tell it|that|this again\":1.2 OR \"repeat|say|tell one more time\":1.5 OR \"explain|say|tell once more\":1.5 OR \"go over it|this|that again\":2 OR \"would you be able explain\":1.5 OR \"would you explain to me\":1.5) OR ((\"tell me again\":1) NOT NEAR:0 (\"your name\" OR card|credit|number)))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-08T13:29:25.763", "ComponentCount": 11}