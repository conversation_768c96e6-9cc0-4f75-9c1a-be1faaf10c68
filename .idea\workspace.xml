<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="BranchesTreeState">
    <expand>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="LOCAL_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
    </expand>
    <select />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="014e5e5d-c995-4999-a163-ebd7f9cfb7af" name="Default Changelist" comment="debugged ImportScore">
      <change beforePath="$PROJECT_DIR$/ExportCategory.py" beforeDir="false" afterPath="$PROJECT_DIR$/ExportCategory.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/token.txt" beforeDir="false" afterPath="$PROJECT_DIR$/token.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="origin/main" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectId" id="2q06IxcWtmyTm8ckFJzMHtR3aaj" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="ASKED_MARK_IGNORED_FILES_AS_EXCLUDED" value="true" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="restartRequiresConfirmation" value="false" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\CALLMINER\TFS\FEAPI" />
    </key>
  </component>
  <component name="RunManager" selected="Python.GetCategoryContent (cmbia-test)">
    <configuration name="CreateCategory (Emotions-cmbia)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ImportCategory.py" />
      <option name="PARAMETERS" value="--tenant=cmbia --foldername=Emotions --xlsfilename=Emotions.xlsx --section_name=Emotions-mike --importer=null --api=feapi --useexistingsection=1" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="CreateCategory (Emotions-mint)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ImportCategory.py" />
      <option name="PARAMETERS" value="--tenant=mint --foldername=Emotions --xlsfilename=Emotions.xlsx --section_name=Emotions2 --importer=null --api=feapif --useexistingsection=1" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="CreateCategory (TEST)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ImportCategory.py" />
      <option name="PARAMETERS" value="CMBIA\test testimport null" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="CreateCategory (deptest-cmbia) (newsection)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ImportCategory.py" />
      <option name="PARAMETERS" value="--Tenant=cmbia --FolderName=CMBIA\\^deptest_ --NewSectionName=deptest_filters2 --Importer=null --API=feapi --UseExistingSection=True" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="CreateCategory (deptest-cmbia)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ImportCategory.py" />
      <option name="PARAMETERS" value="--Tenant=cmbia --FolderName=CMBIA\\^deptest_ --NewSectionName=deptest_filters --Importer=null --API=feapi --UseExistingSection=True" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="CreateCategory (test-cmbia) (Overwrite)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ImportCategory.py" />
      <option name="PARAMETERS" value="--Tenant=cmbia --FolderName=CMBIA\\Test2 --NewSectionName=Test2b --Importer=null --API=feapi --UseExistingSection=True --OverwriteExistingCategories=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ExportAlert" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ExportAlert.py" />
      <option name="PARAMETERS" value="--API=uatafeapi --Tenant=productuata --NameFilter=&quot;API Test&quot; --IgnoreFilterDependencies=True" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ExportCategory (cmbia-AK Emotions)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --Tenant=cmbia --BucketFullnameFilter=&quot;^KA Emotions&quot; --GetDependencies=True --IgnoreFilterDependencies=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ExportCategory (cmbia-Emotions-mike)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --Tenant=cmbia --BucketFullnameFilter=&quot;^Emotions-mike&quot; --GetDependencies=True --IgnoreFilterDependencies=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ExportCategory (cmbia-Emotions)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --Tenant=cmbia --BucketFullnameFilter=&quot;^Emotions\.&quot; --GetDependencies=True --IgnoreFilterDependencies=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ExportScore" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ExportScore.py" />
      <option name="PARAMETERS" value="--Tenant=&quot;cmbia&quot; --NameFilter=&quot;API Test&quot; --IgnoreFilterDependencies=1" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="GetCategoryContent (abbottus-Categories.Quality_Automation_Calls) " type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --BucketFullnameFilter=&quot;Quality Automation_Calls&quot; --GetDependencies" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="GetCategoryContent (cmbia-deptest)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --Tenant=cmbia --BucketFullnameFilter=&quot;^deptest\.&quot; --GetDependencies=True --IgnoreFilterDependencies=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="GetCategoryContent (cmbia-test)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --Tenant=cmbia --BucketFullnameFilter=&quot;Test&quot; --GetDependencies=True --IgnoreFilterDependencies=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="GetCategoryContent (ignite-tek.Emotions)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --BucketFullnameFilter=&quot;Emotions&quot; --GetDependencies --IgnoreFilterDependencies" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="GetCategoryContent (ignite-tek.z_leadgen)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="C:\CALLMINER\TFS\FEAPI\ExportCategory.py" />
      <option name="PARAMETERS" value="--API=feapi --BucketFullnameFilter=&quot;z_Lead&quot;" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ImportAlert" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ImportAlert.py" />
      <option name="PARAMETERS" value="--API=uatafeapi --Tenant=productuata --SourceFolderName=&quot;productuata\API Test&quot; --TargetAlertFolder=test.import --TargetNamePrefix=imported_ --OverwriteExisting=True --RemoveFilters=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ImportScore (cmbia-withfilters)" type="PythonConfigurationType" factoryName="Python">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ImportScore.py" />
      <option name="PARAMETERS" value="--Tenant=&quot;cmbia&quot; --FolderName=&quot;cmbia/test_import&quot; --NewScoreName=&quot;test_import_filters&quot; --OverwriteExistingScore=True --RemoveFilters=False" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ImportScore" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ImportScore.py" />
      <option name="PARAMETERS" value="--Tenant=&quot;cmbia&quot; --FolderName=&quot;cmbia/test_import&quot; --NewScoreName=&quot;test_import3&quot; --OverwriteExistingScore=True --RemoveFilters=True" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="FEAPI" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.CreateCategory (Emotions-cmbia)" />
      <item itemvalue="Python.CreateCategory (Emotions-mint)" />
      <item itemvalue="Python.CreateCategory (TEST)" />
      <item itemvalue="Python.CreateCategory (deptest-cmbia) (newsection)" />
      <item itemvalue="Python.CreateCategory (deptest-cmbia)" />
      <item itemvalue="Python.CreateCategory (test-cmbia) (Overwrite)" />
      <item itemvalue="Python.ExportCategory (cmbia-AK Emotions)" />
      <item itemvalue="Python.ExportCategory (cmbia-Emotions-mike)" />
      <item itemvalue="Python.ExportCategory (cmbia-Emotions)" />
      <item itemvalue="Python.GetCategoryContent (abbottus-Categories.Quality_Automation_Calls) " />
      <item itemvalue="Python.GetCategoryContent (cmbia-deptest)" />
      <item itemvalue="Python.GetCategoryContent (cmbia-test)" />
      <item itemvalue="Python.GetCategoryContent (ignite-tek.Emotions)" />
      <item itemvalue="Python.GetCategoryContent (ignite-tek.z_leadgen)" />
      <item itemvalue="Python.ImportScore (cmbia-withfilters)" />
      <item itemvalue="Python.ExportAlert" />
      <item itemvalue="Python.ExportScore" />
      <item itemvalue="Python.ImportAlert" />
      <item itemvalue="Python.ImportScore" />
      <item itemvalue="Python.test" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.ExportScore" />
        <item itemvalue="Python.ImportAlert" />
        <item itemvalue="Python.ExportAlert" />
        <item itemvalue="Python.ImportScore" />
        <item itemvalue="Python.test" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="014e5e5d-c995-4999-a163-ebd7f9cfb7af" name="Default Changelist" comment="" />
      <created>1733787070408</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1733787070408</updated>
    </task>
    <task id="LOCAL-00001" summary="original before mint">
      <created>1740415245501</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1740415245501</updated>
    </task>
    <task id="LOCAL-00002" summary="original before mint">
      <created>1740415942329</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1740415942329</updated>
    </task>
    <task id="LOCAL-00003" summary="after config change">
      <created>1740439732428</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1740439732428</updated>
    </task>
    <task id="LOCAL-00004" summary="after config change to both Get and Create">
      <created>1740494492368</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1740494492368</updated>
    </task>
    <task id="LOCAL-00005" summary="after filter dependencies and old/new bucketname added">
      <created>1740783189597</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1740783189597</updated>
    </task>
    <task id="LOCAL-00006" summary="adding scores">
      <created>1741119427378</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1741119427378</updated>
    </task>
    <task id="LOCAL-00007" summary="after debugin score updates">
      <created>1741137862551</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1741137862551</updated>
    </task>
    <task id="LOCAL-00008" summary="fixed update in API">
      <created>1741138565274</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1741138565274</updated>
    </task>
    <task id="LOCAL-00009" summary="fixed error in failed API for scores">
      <created>1741141714381</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1741141714381</updated>
    </task>
    <task id="LOCAL-00010" summary="added export alert">
      <created>1743122301279</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1743122301279</updated>
    </task>
    <task id="LOCAL-00011" summary="added export alert">
      <created>1743196942828</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1743196942828</updated>
    </task>
    <task id="LOCAL-00012" summary="added alert ImportAlert.py">
      <created>1743514556425</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1743514556425</updated>
    </task>
    <task id="LOCAL-00013" summary="debugged ImportScore">
      <created>1743540576222</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1743540576222</updated>
    </task>
    <task id="LOCAL-00014" summary="debugged ImportScore">
      <created>1743602616220</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1743602616220</updated>
    </task>
    <option name="localTasksCounter" value="15" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="original before mint" />
    <MESSAGE value="after config change" />
    <MESSAGE value="after config change to both Get and Create" />
    <MESSAGE value="after filter dependencies and old/new bucketname added" />
    <MESSAGE value="adding scores" />
    <MESSAGE value="after debugin score updates" />
    <MESSAGE value="fixed update in API" />
    <MESSAGE value="fixed error in failed API for scores" />
    <MESSAGE value="added export alert" />
    <MESSAGE value="added alert ImportAlert.py" />
    <MESSAGE value="debugged ImportScore" />
    <option name="LAST_COMMIT_MESSAGE" value="debugged ImportScore" />
  </component>
  <component name="WindowStateProjectService">
    <state x="447" y="85" key="#com.intellij.execution.impl.EditConfigurationsDialog" timestamp="1751317125081">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state x="677" y="-973" key="#com.intellij.execution.impl.EditConfigurationsDialog/0.0.1536.816/0.-1080.1920.1032@0.-1080.1920.1032" timestamp="1744814893301" />
    <state x="94" y="-1326" key="#com.intellij.execution.impl.EditConfigurationsDialog/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743547999409" />
    <state x="447" y="85" key="#com.intellij.execution.impl.EditConfigurationsDialog/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@0.0.1536.816" timestamp="1751317125081" />
    <state x="-454" y="-1346" key="#xdebugger.evaluate" timestamp="1743549886061">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state x="-454" y="-1346" key="#xdebugger.evaluate/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743549886061" />
    <state width="2027" height="331" key="GridCell.Tab.0.bottom" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.0.bottom/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state width="2027" height="331" key="GridCell.Tab.0.center" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.0.center/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state width="2027" height="331" key="GridCell.Tab.0.left" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.0.left/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state width="2027" height="331" key="GridCell.Tab.0.right" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.0.right/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state width="2027" height="331" key="GridCell.Tab.1.bottom" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.1.bottom/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state width="2027" height="331" key="GridCell.Tab.1.center" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.1.center/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state width="2027" height="331" key="GridCell.Tab.1.left" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.1.left/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state width="2027" height="331" key="GridCell.Tab.1.right" timestamp="1743602661878">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="2027" height="331" key="GridCell.Tab.1.right/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602661878" />
    <state x="463" y="211" key="Vcs.Push.Dialog.v2" timestamp="1749158500051">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state x="-11" y="-1155" key="Vcs.Push.Dialog.v2/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743602618208" />
    <state x="463" y="211" key="Vcs.Push.Dialog.v2/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@0.0.1536.816" timestamp="1749158500051" />
    <state x="751" y="-813" key="VcsDiffUtil.ChangesDialog" timestamp="1744744659163">
      <screen x="0" y="-1080" width="1920" height="1032" />
    </state>
    <state x="751" y="-813" key="VcsDiffUtil.ChangesDialog/0.0.1536.816/0.-1080.1920.1032@0.-1080.1920.1032" timestamp="1744744659163" />
    <state width="1031" height="559" key="XDebugger.FullValuePopup" timestamp="1743550103569">
      <screen x="-628" y="-1440" width="2048" height="1104" />
    </state>
    <state width="1031" height="559" key="XDebugger.FullValuePopup/0.0.1536.816/1932.-1755.1152.2000/-3188.-1433.2048.1104/-628.-1440.2048.1104@-628.-1440.2048.1104" timestamp="1743550103569" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/FEAPI_Helpers.py</url>
          <line>491</line>
          <option name="timeStamp" value="71" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ExportCategory.py</url>
          <line>364</line>
          <option name="timeStamp" value="80" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ImportCategory.py</url>
          <line>299</line>
          <option name="timeStamp" value="98" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ImportCategory.py</url>
          <line>322</line>
          <option name="timeStamp" value="100" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ImportCategory.py</url>
          <line>327</line>
          <option name="timeStamp" value="102" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ImportCategory.py</url>
          <line>295</line>
          <option name="timeStamp" value="107" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/FEAPI_Helpers.py</url>
          <line>599</line>
          <option name="timeStamp" value="132" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ImportScore.py</url>
          <line>358</line>
          <option name="timeStamp" value="134" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ImportScore.py</url>
          <line>367</line>
          <option name="timeStamp" value="135" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/FEAPI_Helpers.py</url>
          <line>583</line>
          <option name="timeStamp" value="141" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="config" language="Python" />
        <watch expression="list(json_allAlerts[0].keys())" language="Python" />
        <watch expression="pd.json_normalize(json_allAlerts,record_path='SearchComponents',meta=k, meta_prefix=&quot;alert.&quot;, sep='.')" language="Python" />
        <watch expression="k.remove(&quot;SearchComponents&quot;)" language="Python" />
        <watch expression="config.json_output_dir" />
        <watch expression="FEAPI_Helpers.FEAPI_Helper.MatchesAnyRegex(config.NameFilter,&quot;API &quot;)" language="Python" />
        <watch expression="helper" language="Python" />
        <watch expression="df_allscores.loc[df_allscores['Id'] == s[&quot;Id&quot;], 'ScoreFullname']" language="Python" />
        <watch expression="df_allscores[df_allscores['Id'] != 34]" language="Python" />
        <watch expression="s[&quot;Id&quot;]" language="Python" />
      </configuration>
    </watches-manager>
  </component>
</project>