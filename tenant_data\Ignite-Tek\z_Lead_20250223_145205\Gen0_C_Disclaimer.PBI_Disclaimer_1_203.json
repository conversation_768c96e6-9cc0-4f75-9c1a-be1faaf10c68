{"BucketId": "203", "BucketFullname": "Categories.C_Disclaimer.PBI_Disclaimer_1", "BucketDescription": "we do not offer every plan available in your area. this is what Lucid/PBI currently runs as Disclaimer 1", "UserEditable": false, "BucketName": "PBI_Disclaimer_1", "SectionName": "C_Disclaimer", "SectionNameDisplay": "C_Disclaimer", "SectionId": 72, "RetroStartDate": "2024-10-04T00:00:00", "CategoryDisplay": "C_Disclaimer.PBI_Disclaimer_1", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-18T12:18:54.063-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-04T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-10-14T00:00:00", "EndDate": "2024-10-14T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -31342, "Name": "Disclaimer 2", "Description": null, "ActionString": "[every+plan+available+in+your+area:2][do+not+offer:2]$AND$[do+not+offer+every+plan+available+in+your+area:2]$OR$", "UserEntry": "(\"do not offer every plan available in your area\":2) OR (\"do not offer\":2 AND \"every plan available in your area\":2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31137, "Name": "V2", "Description": null, "ActionString": "[in+your|the+area:1.5]<available>[not|don`t+offer+every+plan:1.5]$BEFORE:1$$BEFORE:1$", "UserEntry": "\"not|don't offer every plan\":1.5 BEFORE:1 available BEFORE:1 \"in your|the area\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31088, "Name": "V3", "Description": null, "ActionString": "[your+area:1.5]<plan>[not+offer:1]$BEFORE:1$$BEFORE:1.5$", "UserEntry": "\"not offer\":1 BEFORE:1 plan BEFORE:1.5 \"your area\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31087, "Name": "V4", "Description": null, "ActionString": "[plan+available+in+your|the+area:2.5][available+in+your|the+area:1.1][not|don`t+offer:0.5]$BEFORE:3$$OR$", "UserEntry": "(\"not|don't offer\" BEFORE:3 \"available in your|the area\") OR (\"plan available in your|the area\":2.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-06T12:52:20.973", "ComponentCount": 4}