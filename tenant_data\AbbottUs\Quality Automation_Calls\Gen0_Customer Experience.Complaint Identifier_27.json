{"BucketId": "27", "BucketFullname": "Categories.Customer Experience.Complaint Identifier", "BucketDescription": "Identifies calls where a case needs to be logged due to customers requesting a sensor/reader replacement or are expressing concern about their health being affected by sensors/readers not being readily available.", "UserEditable": false, "BucketName": "Complaint Identifier", "SectionName": "Customer Experience", "SectionNameDisplay": "Customer Experience", "SectionId": 34, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Customer Experience.Complaint Identifier", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2023-10-03T10:54:03.94-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32641, "Name": "Complaint Identifier", "Description": null, "ActionString": "[say%+try%+minutes|again|later:2]!35%[it%+brok%:1]!35%[only|just+put+it:1.5]!35%[only+lasted+me:1.5]!35%[stop+doing+future:2]!35%[something+wrong:1.2]!35%[knock%|hang%|bump%+arm|off|out:1.5]!35%[managed+to+knock:1.5]!35%[woke%|wake%+up:1]!35%[sense|sensor|census|centre|libra|fence|sentence+off:1.2]!35%<speak>!35%<online>!35%$OR$[try+again|minut%|later:1.7]!35%$NOTNEAR:4${lost+libra|got|meter|reader|sensor|sense%|fence|sentence|census|centre|signal|conection:1.2}!35%[scan%+sensor|census|sense%|sentence%|fence|centre:1]!35%[still|percent|send+off:1]!35%[in|during|after|before|out|hav%|had|while+shower%:1.2]!35%[in|on|it|put|sensor|sense|fence|sentence|centre+last+night:1.2]!35%[content|contra|contract|control|controlled|controls|correct|couple|credit|current+solution:1.5]!35%[pull%|came|drop%|remov%+off:2]!35%<accident%>!35%$NEAR:3$[hang%+off|on:1]!35%[the+last+sensor%|census|sense%|sentence%|centre:1.3]!35%[stick%+out|on:1]!35%[%stuck|%t`s|gone|went|is|exaclty|obviously|drastically+wrong|lost+data|dates|sensor|sense%|centre|reader:1.5]!35%[change%|inserted+sensor%|sense%|centre|sponsor%|device%|service%|census|fence%|meter|reader|sentence%:2]!35%[sensor%|sense%|centre|sponsor%|device%|service%|census|fence%|meter|reader|sentence%+yesterday:2]!35%[same+results:1.2]!35%[want%|need|like|calling|ringing|phoning+report:1.5]!35%[ready|fifteen|fifty|sixty|sixteen+minutes:2.2]!35%[hasn`t|not|never+arrive%:1.2]!35%[miss%+item|parcel|part%:1.5]!35%[incorrect+reading%:1.2]!35%[reading%+not+correct:1.5]!35%{not|same+result%:1.5}!35%{black|blank|broke%+screen:1.2}!35%[never+happened|seen+before:1.5]!35%[it%|just+come|came+up|off:1.5]!35%[stuck|stock+in:1]!35%<censor%>!35%<sen%r%>!35%<patch%>!35%<freestyle>!35%<libre>!35%<libra>!35%$OR$$OR$$OR$$OR$$OR$<remove%>!35%$NEAR:20${really+hurt%:1}!35%[some|bit|quite|lot|lots|in|have|little|very+pane|pain%|ache%|discomfort:1.7]!35%[%n`t|never|not+receive%|got+it|order%|anything|nothing|package|deliver%:2]!35%[door|fall|fell+frame|off:1.2]!35%[appl%|need+new|centre|sensor|one|sense%|sentence%:1.5]!35%{big|massive|high|between|from+differen%:1.7}!35%{day%+left|early|after|run:1.2}!35%[seems|appears|looks+bad|dodgy|faulty+batch:2]!35%[phone+%n`t|%not+scan%:1.7]!35%[cancel%|%schedul%+appointment%:1.2]!35%[get%+new:1]!35%$NOTBEFORE:40$[%n`t|%nt|%not+look|appear|seem|go+low|high|hypo|hyper:2]!35%<alert%>!35%<alarm%>!35%$OR$$NEAR:15${false|high%|inaccurate|low%|wrong|different+read%:1.5}!35%[sense%|sensor%|census|fence%|meter|reader|sentence%|centre+recognized|found:2]!35%[replac%|send%|new|issue%|fault%|remove+sensor%|sense%|sponsor%|fence%|census|meter|reader|sentence|centre%:2]!35%[finger+prick%|test:0.5]!35%[show%+hi|high%|lo|low%:2]!35%[scan+read%|was:2]!35%[have|had+low|high|lo|hi|hypo|hyper+alarm%|alert%|reading%:2]!35%$OR$$OR$$NEAR:5$[meter|reader|monitor|machine|scanner+unresponsive:1.7]!35%[alarm%|alert%+keep%|are+unavailable|going|ringing:2]!35%[finger+prick%+broke%:2]!35%[never+had+before:1.4]!35%{fail%|fault%|issue%|problem%+server|sensor%|sense%|centre|sponsor%|device%|service%|census|fence%|meter|reader|sentence%|libr%|defect%|alarm%:2}!35%[not|%n’t+stick%|stay|attach:1.5]!35%[something%+wrong:1.3]!35%[message+say%|said:1]!35%{error+message:1.2}!35%{problem%|issue%+server|sensor%|sense%|sponsor%|device%|service%|census|fence%|meter|reader|sentence%|alarm|centre%:1.4}!35%[adhesiv%+%not|%n`t|%nt+hold%:2]!35%[%not|%nt|%n`t+stick%:0.5]!35%{differen%+results|between:1.4}!35%{really|lot|lots|pouring|have+hurt%|bleed%|bled|blood%:1.7}!35%{scan%|read%+problem%|issue%:1.7}!35%<advance>!35%<plan>!35%<box>!35%<market%>!35%<credit>!35%$OR$$OR$$OR$$OR$<stuck>!35%<inaccurate>!35%<sticky>!35%<bleed>!35%<discrepancy>!35%<replace%>!35%<error%>!35%<recognise%>!35%<fault%>!35%<incorrect%>!35%<unavailable>!35%<damage%>!35%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTNEAR:2$[fell|came|come%|fall%|drop%|com%+off|out:1.2]!35%[not|%n’t+get%|giv%|got+result%:1.5]!35%[thank+you:1.1]!35%[not|%n’t|stop%+work%|scan%|charg%|correct%|read%|switch|match|turn:2]!35%$NOTNEAR:2$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"not|*n’t|stop* work*|scan*|charg*|correct*|read*|switch|match|turn\":2 NOT NEAR:2 \"thank you\":1.1\nOR \"not|*n’t get*|giv*|got result*\":1.5 \nOR \"fell|came|come*|fall*|drop*|com* off|out\":1.2 \nOR ((damage*|unavailable|incorrect*|fault*|recognise*|error*|replace*|discrepancy|bleed|sticky|inaccurate|stuck) NOT NEAR:2 (credit|market*|box|plan|advance))\nOR [scan*|read* problem*|issue*]:1.7 \nOR [really|lot|lots|pouring|have hurt*|bleed*|bled|blood*]:1.7 \nOR [differen* results|between]:1.4\nOR \"*not|*nt|*n't stick*\"\nOR \"adhesiv* *not|*n't|*nt hold*\":2 \nOR [problem*|issue* server|sensor*|sense*|sponsor*|device*|service*|census|fence*|meter|reader|sentence*|alarm|centre*]:1.4 \nOR [error message]:1.2 \nOR \"message say*|said\":1  \nOR \"something* wrong\":1.3 \nOR \"not|*n’t stick*|stay|attach\":1.5 \nOR [fail*|fault*|issue*|problem* server|sensor*|sense*|centre|sponsor*|device*|service*|census|fence*|meter|reader|sentence*|libr*|defect*|alarm*]:2 \nOR \"never had before\":1.4\nOR \"finger prick* broke*\":2\nOR \"alarm*|alert* keep*|are unavailable|going|ringing\":2\nOR \"meter|reader|monitor|machine|scanner unresponsive\":1.7\nOR (\"have|had low|high|lo|hi|hypo|hyper alarm*|alert*|reading*\":2 OR \"scan read*|was\":2 OR \"show* hi|high*|lo|low*\":2) NEAR:5 \"finger prick*|test\"\nOR \"replac*|send*|new|issue*|fault*|remove sensor*|sense*|sponsor*|fence*|census|meter|reader|sentence|centre*\":2 \nOR \"sense*|sensor*|census|fence*|meter|reader|sentence*|centre recognized|found\":2 \nOR [false|high*|inaccurate|low*|wrong|different read*]:1.5\nOR (alarm*|alert*) NEAR:15 \"*n't|*nt|*not look|appear|seem|go low|high|hypo|hyper\":2 \nOR \"get* new\":1 NOT BEFORE:40 \"cancel*|*schedul* appointment*\":1.2\nOR \"phone *n't|*not scan*\":1.7\nOR \"seems|appears|looks bad|dodgy|faulty batch\":2                                                                                                                                                                                                                      \nOR [day* left|early|after|run]:1.2 \nOR [big|massive|high|between|from differen*]:1.7  \nOR \"appl*|need new|centre|sensor|one|sense*|sentence*\":1.5 \nOR \"door|fall|fell frame|off\":1.2 \nOR \"*n`t|never|not receive*|got it|order*|anything|nothing|package|deliver*\":2 \nOR \"some|bit|quite|lot|lots|in|have|little|very pane|pain*|ache*|discomfort\":1.7\nOR [really hurt*]:1 \nOR (remove*) NEAR:20 (libra|libre|freestyle|patch*|sen*r*|censor*) \nOR \"stuck|stock in\":1 \nOR \"it*|just come|came up|off\":1.5 \nOR \"never happened|seen before\":1.5 \nOR [black|blank|broke* screen]:1.2 \nOR [not|same result*]:1.5 \nOR \"reading* not correct\":1.5 \nOR \"incorrect reading*\":1.2 \nOR \"miss* item|parcel|part*\":1.5 \nOR \"hasn`t|not|never arrive*\":1.2 \nOR \"ready|fifteen|fifty|sixty|sixteen minutes\":2.2 \nOR \"want*|need|like|calling|ringing|phoning report\":1.5 \nOR \"same results\":1.2 \nOR \"sensor*|sense*|centre|sponsor*|device*|service*|census|fence*|meter|reader|sentence* yesterday\":2                                                                          \nOR \"change*|inserted sensor*|sense*|centre|sponsor*|device*|service*|census|fence*|meter|reader|sentence*\":2 \nOR \"*stuck|*t's|gone|went|is|exaclty|obviously|drastically wrong|lost data|dates|sensor|sense*|centre|reader\":1.5 \nOR \"stick* out|on\":1 OR \"the last sensor*|census|sense*|sentence*|centre\":1.3\nOR \"hang* off|on\":1\nOR (accident*) NEAR:3 (\"pull*|came|drop*|remov* off\":2) \nOR \"content|contra|contract|control|controlled|controls|correct|couple|credit|current solution\":1.5                                                                         \nOR \"in|on|it|put|sensor|sense|fence|sentence|centre last night\":1.2                                                                                                                                   \nOR \"in|during|after|before|out|hav*|had|while shower*\":1.2\nOR \"still|percent|send off\":1 \nOR \"scan* sensor|census|sense*|sentence*|fence|centre\":1 \nOR [lost libra|got|meter|reader|sensor|sense*|fence|sentence|census|centre|signal|conection]:1.2 \nOR \"try again|minut*|later\":1.7 NOT NEAR:4 (online|speak)\nOR \"sense|sensor|census|centre|libra|fence|sentence off\":1.2 \nOR \"woke*|wake* up\":1 \nOR \"managed to knock\":1.5 \nOR \"knock*|hang*|bump* arm|off|out\":1.5                     \nOR \"something wrong\":1.2 \nOR \"stop doing future\":2 OR \"only lasted me\":1.5\nOR \"only|just put it\":1.5 \nOR \"it* brok*\":1                       \nOR \"say* try* minutes|again|later\":2){+35%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32641, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32641, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-15T09:36:03.943", "ComponentCount": 1}