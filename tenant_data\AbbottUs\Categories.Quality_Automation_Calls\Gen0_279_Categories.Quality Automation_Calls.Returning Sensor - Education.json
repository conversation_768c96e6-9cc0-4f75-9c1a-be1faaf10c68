{"BucketId": "279", "BucketFullname": "Categories.Quality Automation_Calls.Returning Sensor - Education", "BucketDescription": "To find calls where the agents are providing  explanation or education for the reason or importance of returning the sensors", "UserEditable": false, "BucketName": "Returning Sensor - Education", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": null, "CategoryDisplay": "Quality Automation_Calls.Returning Sensor - Education", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 0.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2025-02-14T10:58:10.987-05:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-19T10:52:55.463", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31665, "Name": "Test", "Description": null, "ActionString": "<investigat%><learn><test><analyze><verify><determin%><check%><improv%><investigat%><look>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[return+kit|envelop%|box|instruction%|label%|gift%:2]$NEAR:60$", "UserEntry": "\"return kit|envelop*|box|instruction*|label*|gift*\":2 NEAR:60 (look|investigat*|improv*|check*|determin*|verify|analyze|test|learn|investigat*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31665, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "UDF_text_37", "TableFilterTypes": null, "FriendlyName": "Skill Name", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Reads", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31664, "Name": "Returning Education", "Description": null, "ActionString": "[back|return%+for+investigation%:1.2][we+need+to+check+sensor%:1.5][in+order+improve|learn|investigat%|analyze:1.3][for+us+to+improve|investi%|learn:1.3][why+working+proper%:1.3]{determine|verify|analyze|exactly+what+happen%:1.4}[why+we+need+it+back|return%:1.3][we+need+it+back+to:1.4][we+do|perform:1][because+of:1.2][sensor%+replace%:1.4][you+receiv%|seeing|get%|got:1.3]<i><patient%><customer%>$OR$$OR$[i`m+replac%|asking|need%:1][i+will|would|need:1]$OR$$OR$$OR$$OR$$OR$$OR$[the+reason+why:1.3]$NOTNEAR:2$<determine><investigate>$OR$<option%>[look+into:1.2]$NEAR:3$[further+analys%|investigation%|evaluation:1.3][our+specialist%|technician%:1.3][we+can+inspect%|analyze%|evaluat%:1.3][so+we+can|%sure+see|look|check|investigate|determine:1.3][we+can+take+a+look:1.3]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[prepaid+label:1.2][send+me+back:1.2][don`t|no|wouldn`t+need:1.2][send|return+back+to+us:1.4]$NOTNEAR:2$[return|send|ship|restoring+sensor%:1.3][return+kit|envelop%|box|instruction%|label%|gift:1.3]$OR$$OR$$OR$$OR$$NEAR:50$", "UserEntry": "(\"return kit|envelop*|box|instruction*|label*|gift\":1.3 OR \"return|send|ship|restoring sensor*\":1.3 OR (\"send|return back to us\":1.4 NOT NEAR:2 \"don't|no|wouldn't need\":1.2) OR \"send me back\":1.2 OR \"prepaid label\":1.2) NEAR:50 (\"we can take a look\":1.3 OR  \"so we can|*sure see|look|check|investigate|determine\":1.3 OR \"we can inspect*|analyze*|evaluat*\":1.3 OR \"our specialist*|technician*\":1.3 OR \"further analys*|investigation*|evaluation\":1.3 OR \"look into\":1.2 NEAR:3 (option*) OR (investigate|determine) OR \"the reason why\":1.3 NOT NEAR:2 (\"I will|would|need\":1 OR \"I'm replac*|asking|need*\":1 OR (customer*|patient*|i) OR \"you receiv*|seeing|get*|got\":1.3 OR \"sensor* replace*\":1.4 OR \"because of\":1.2 OR \"we do|perform\":1) OR \"we need it back to\":1.4 OR \"why we need it back|return*\":1.3 OR [determine|verify|analyze|exactly what happen*]:1.4 OR \"why working proper*\":1.3 OR \"for us to improve|investi*|learn\":1.3 OR \"in order improve|learn|investigat*|analyze\":1.3 OR \"we need to check sensor*\":1.5 OR \"back|return* for investigation*\":1.2)", "SpeakerList": "", "Status": true, "Weight": -1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31664, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "UDF_text_37", "TableFilterTypes": null, "FriendlyName": "Skill Name", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Reads", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-19T10:52:58.13", "ComponentCount": 2}