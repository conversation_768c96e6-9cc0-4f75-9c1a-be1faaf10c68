{"BucketId": "568", "BucketFullname": "Categories.Emotions.Holiday Wishes", "BucketDescription": "Capturing Holiday wishes as positive language", "UserEditable": false, "BucketName": "Holiday Wishes", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.<PERSON>es", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-18T10:40:07.67-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "Unknown", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -29826, "Name": "Holiday Greeting-Closing", "Description": null, "ActionString": "[happy|merry|wonderful|great|blessed|nice|safe|beautiful|favorite|amazing+holiday%|thanksgiving|christmas|year%|easter|fourth|july|memorial|labor|veterans:0.5]", "UserEntry": "\"happy|merry|wonderful|great|blessed|nice|safe|beautiful|favorite|amazing holiday*|thanksgiving|christmas|year*|easter|fourth|july|memorial|labor|veterans\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29814, "Name": "Happy Birthday", "Description": null, "ActionString": "[happy|belated+birthday:0.5]", "UserEntry": "\"happy|belated birthday\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-18T14:55:02.54", "ComponentCount": 2}