
'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''
import FEAPI_Helpers
import json
import os,sys
import pandas as pd
from pathlib import Path
from datetime import datetime
import configparser



class config:



    # Default values
    '''
    [FEAPI]
    '''
    API = "feapi"
    DataRoot = "tenant_data"
    LogRoot = "logs"
    DEBUG = False
    LoggerLevel = "DEBUG"

    '''
    [ImportScore]
    '''
    Tenant = ""
    FolderName = ""
    NewScoreName=""
    Importer=None
    RemoveFilters = False
    OverwriteExisting = False

    API_timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    json_output_dir = ""
    @staticmethod
    def FilenameInOutputFolder(filename):
        return os.path.join(config.json_output_dir, filename)

    @staticmethod
    def AllCategories_xls_filename():
        return config.FilenameInOutputFolder (  f"AllCategories.xlsx")

    @staticmethod
    def PyFilename():
        os.path.basename(sys.argv[0])

    @staticmethod
    def SetFolderName(foldername):
        config.FolderName = f"{config.DataRoot}/{foldername}"

    @staticmethod
    def read_ini_file(file_path:str=""):
        if file_path=="":
            file_path = f"{os.path.basename(sys.argv[0])}.INI"
        if os.path.exists(file_path):
            config.strip_lines_with_triple_single_quotes(file_path,file_path)
            configparse = configparser.ConfigParser()
            configparse.read(file_path)

            config.DEBUG = configparse.get('FEAPI', 'DEBUG').strip('"')  == 1
            config.LoggerLevel = configparse.get('FEAPI', 'LoggerLevel').strip('"')
            config.API = configparse.get('FEAPI', 'API').strip('"')
            config.Tenant = configparse.get('FEAPI', 'Tenant').strip('"')
            config.LogRoot = configparse.get('FEAPI', 'LogRoot').strip('"')
            config.DataRoot = configparse.get('FEAPI', 'DataRoot').strip('"')
        else:
            print(f"ERROR: INI file at {file_path} does not exist **************************")

    @staticmethod
    def strip_lines_with_triple_single_quotes(input_file: str, output_file: str):
        with open(input_file, 'r') as file:
            lines = file.readlines()
            # Filter out lines that are just ''' (including any whitespace)
        cleaned_lines = [line for line in lines if line.strip() != "'''"]
        # Write the cleaned lines to the output file
        with open(output_file, 'w') as file:
            file.writelines(cleaned_lines)

config.read_ini_file()



# LOGGING --------------------------------------------------------------------------------------
import logging
# Set up logging configuration
logger = logging.getLogger(config.PyFilename())
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

#reset logger level if passed in args
level_string = config.LoggerLevel.upper()  # Ensure case-insensitivity
level = logging.getLevelName(level_string)
if isinstance(level, int):  # Check if a valid level was found
    logging.getLogger().setLevel(level)
    print(f"Logging level set to: {logging.getLevelName(level)}")
else:
    print(f"Invalid logging level string passed as config: {level_string}")

# Create handlers and log folders
if not os.path.exists(config.LogRoot):
    os.makedirs(config.LogRoot)

logprefix = f"{config.LogRoot}/{config.PyFilename()}-{config.timestamp}"
file_handler = logging.FileHandler(f'{logprefix}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'{logprefix}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'{logprefix}.warnings.txt')  # Log to errors.txt

console_handler = logging.StreamHandler()  # Log to console


# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)



#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, config)



#API info
# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# for FISUS, use feapif.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token variable







def LoadCategoryXLS(xls_filename):
    df_categories = None
    if os.path.exists(xls_filename):
        try:
            df_categories = pd.read_excel(xls_filename, sheet_name="ALL Categories")
            logger.info(f"Successfully loaded 'ALL categories' sheet from {xls_filename}")
            # You can now work with the df_categories DataFrame
            # print(df_categories.head()) # Example: print the first few rows
        except FileNotFoundError:  # redundant check, but good practice
            logger.error(f"File not found: {xls_filename}")
        except KeyError:
            logger.error(f"Sheet 'categories' not found in {xls_filename}")
        except Exception as e:
            logger.error(f"An error occurred while loading the file: {e}")
    else:
        logger.error(f"File {xls_filename} does not exist.")

    return df_categories





# # Example usage
# try:
#     old_id = 2  # Example old bucket ID
#     new_id = GetNewBucketId(old_id)
#     print(f"The new BucketId for old BucketId {old_id} is {new_id}.")
# except ValueError as e:
#     print(e)

def PrepScoreJson(config, scorejson, scoreidtoimport):
    # Update the JSON so it cretes a new bucekt or updates an existing bucket
    current_date_time = config.API_timestamp

    #Update the ScoreID - set scoreidtoimport to None if new score
    scorejson['Id'] = scoreidtoimport

    # Remove all bin ScoreID's
    for b in scorejson["Bins"]:
        b["Id"] = None
        b["ScoreId"] = None

    # Remove SC and Bin ID's
    for component in scorejson["ScoreComponents"]:
        component["LastUser"] = config.Importer
        component["LastModified"] = current_date_time
        component["Id"] = None
        for b in component["Bins"]:
            b["Id"] = None
            b["ScoreComponentId"]=None
        for r in component["Ranges"]:
            r["ScoreComponentId"] = None

    scorejson['RetroStartDate'] = None
    scorejson['Created'] = current_date_time
    scorejson['EffectiveDate'] = current_date_time
    scorejson['LastModified'] = current_date_time
    scorejson['LastUser'] = config.Importer

    scorejson['LastNDays'] = None
    scorejson['TimeFrame'] = "Yesterday"
    # Update owner
    scorejson['Creator'] = config.Importer

    return scorejson


def UpdateFiltersWithNewBucketIds(df_newbuckets, df_oldbuckets, filterjson, scorenametoimport):
    newfilters = []
    for f in filterjson:
        if f["BucketID"] != None:
            # Lookup newbucketid
            oldbucketid = f["BucketID"]
            newbucketid, bucketname = helper.GetNewBucketIdAndName(oldbucketid, df_oldbuckets, df_newbuckets)
            if newbucketid == None:
                # Can't map bucketid, abandon
                logging.error(f"Could not find matching New BucketID to filter ScoreComponent by in {f}")
                return

            #Success
            f["BucketID"] = newbucketid
            newfilters.append(f)
            logger.info(
                f"Updated Category Filter ({bucketname}) for {scorenametoimport} from {oldbucketid} to {f['BucketID']}")

    return newfilters




#-----------------------------------------------------------------------------------------------

def CreateScore(config, json_file, df_AllCategories, df_oldbuckets, df_newbuckets):
    # Load JSON data from the file
    scorejson = helper.load_json_from_file(json_file)
    if scorejson is not None:
        logger.info(f"Loaded JSON data for: {json_file}")
        # logger.info(json.dumps(data, indent=4))

    scorenametoimport = config.NewScoreName
    scoreidtoimport = None
    if config.NewScoreName == "":
        # Use section names from imported categories
        logger.info(f"NewScoreName is blank.  Using original Score Names to import")
        scorenametoimport = scorejson["Name"]
        scoreidtoimport = scorejson["Id"]

    # Check to see if score_name already exists, if so overwrite it
    allscorejson, df_allscores = helper.API_GetAllScores()
    for s in allscorejson:
        if s["Name"] == scorenametoimport:
            scoreidtoimport = s["Id"]

            #Check for overwriting
            if config.OverwriteExisting == False:
                logger.error(f"Score ({scorenametoimport}) already exists and config.OverwriteExisting = false.  Exiting...")
                exit(1)

    scorejson = PrepScoreJson(config, scorejson, scoreidtoimport)
    #Update Score's name
    scorejson["Name"] = scorenametoimport

    # UPDATE BucketID from oldbucketID to newbucketID
    for sc in scorejson["ScoreComponents"]:
        oldbucketid = sc["BucketId"]
        if oldbucketid != None:
            #Lookup newbucketid
            newbucketid, bucketname = helper.GetNewBucketIdAndName(oldbucketid, df_oldbuckets, df_newbuckets)
            if newbucketid == None:
                #Can't map bucketid, abandon
                logger.error(
                    f"For Score.Scorecomponent ({scorenametoimport}.{sc['Name']}), Unable to find newbucketid matching oldbucketid ({oldbucketid} = {bucketname})")
                return

            #otherwise success
            sc["BucketId"] = newbucketid




    #Update Filters to new bucketids
    if scorejson["Filters"] != []:
        if config.RemoveFilters:
            #Update all filters
            scorejson["Filters"] = []

        else:
            #Convert filter
            if scorejson["Filters"] != []:
                #Update Filters
                filterjson = scorejson["Filters"]
                newfilters = UpdateFiltersWithNewBucketIds(df_newbuckets, df_oldbuckets, filterjson,  scorenametoimport)
                scorejson["Filters"] = newfilters

    # Handle the filters of each ScoreComponent
    for sc in scorejson['ScoreComponents']:
        if sc["Filters"] != []:
            if config.RemoveFilters:
                # Update all filters
                sc["Filters"] = []
                logger.info(f"Ignoring ScoreComponents Filter Dependencies for {scorenametoimport}.{sc['Name']} ...")
            else:
                if sc["Filters"] != []:
                    # Update filter
                    filterjson = sc["Filters"]
                    newfilters = UpdateFiltersWithNewBucketIds(df_newbuckets, df_oldbuckets, filterjson,
                                                               scorenametoimport)
                    sc["SearchComponentId"] = None
                    sc["Filters"] = newfilters

    # Dump the importable Category JSON
    # Write JSON object to a file
    json_for_import_filename = os.path.join(config.json_output_dir,
                                            f"{helper.cleanfilename(scorenametoimport)}.json")
    with open(f"{json_for_import_filename}", 'w') as json_file_import:
        try:
            json.dump(scorejson, json_file_import, indent=4)  # `indent=4` for pretty-printing
        except Exception as e:
            logger.error(f"Unable to save Import_json ({scorejson}) as ({json_file_import}). Excpetion = {e}")

    if scoreidtoimport != None:
        # Couldn't find score, create new one
        if config.DEBUG==False:
            logger.info(
                f"OVERWRITING OldScoreName: {scorenametoimport}, ScoreID = {scoreidtoimport} ----------------------")
            scoreid = helper.API_UpdateScore(scorejson)
            if scoreid == None:
                logger.error(
                    f"FAILED OVERWRITING OldScoreName: {scorenametoimport}, ScoreID = {scoreidtoimport} ----------------------")


    else:
        if config.DEBUG == False:
            logger.info(f"CREATING NewScoreName: {scorenametoimport} ----------------------")
            scoreid = helper.API_CreateScore(scorejson)
            if scoreid == None:
                logger.error(
                    f"FAILED CREATING NewScoreName: {scorenametoimport} ----------------------")










# ----------------------------------------------------------------------------------------------------
def CreateScores():

    #Try to load Categories from inputfolder
    xls_filename = os.path.join(config.FolderName, "AllCategories.xlsx")

    df_XLSCategories = LoadCategoryXLS(xls_filename)
    if df_XLSCategories.empty:
        logger.error(f"Unable to load Exported Category info from ({xls_filename}) - Exiting")
        exit(1)
    df_oldbuckets = df_XLSCategories[['BucketId', 'BucketFullname']].copy()

    # Create a Path object for the Imported JSON folder
    folder_path = Path(config.FolderName)
    json_files = folder_path.glob('*.json')
    json_files = list(json_files)
    logger.info (f"Found ({len(json_files)}) json files in {folder_path}...")

    df_AllCategories = helper.GetAllCategories()
    df_newbuckets = df_AllCategories[['BucketId', 'BucketFullname']].copy()



    # Iterate through the generator of json files
    i=1
    for json_file in json_files:
        logger.info(f"Processing ( {i} / {len(json_files)} ) file = {json_file} -------------------------------------------------")
        i = i+1

        CreateScore(config, json_file, df_XLSCategories, df_oldbuckets, df_newbuckets)



#---------------------------------------------------------------------------------------

import argparse

def str_to_bool(s):
    if s.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif s.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')

def main():

    # get cmdline args and overwrite defaults from INI
    config.read_ini_file()

    parser = argparse.ArgumentParser(description='Script to process folder and section names')
    parser.add_argument('--Tenant', type=str,  required=True,help='Name of the tenant to double-check before importing')
    parser.add_argument('--FolderName', type=str, required=True, help='Name of the folder')
    parser.add_argument('--NewScoreName', nargs='?',  required=True,type=str, default="", help='Name of the score to be created. Leave blank to use old core names from export')
    parser.add_argument('--Importer', nargs='?', type=str, default=config.Importer, help='Importer name (optional) = use null if section is to be public')
    parser.add_argument('--API', nargs='?', type=str, default=config.API, help='API (feapi/feapif) (optional)')
    parser.add_argument('--RemoveFilters',  type=str_to_bool, default=config.RemoveFilters, help='RemoveFilters=True if you want to remove all filters from a score')
    parser.add_argument('--OverwriteExisting', type=str_to_bool, default=config.OverwriteExisting,
                        help='OverwriteExisting=True if you want to overwrite a score that already exists with this name')

    args = parser.parse_args()
    logger.info(f"ARGUMENTS PASSED:-------------------------------------------")
    logger.info("\n".join(FEAPI_Helpers.FEAPI_Helper.show_all_attributes_and_values(args)))

    # Loop through attributes of A and copy from B if they match
    FEAPI_Helpers.FEAPI_Helper.UpdateObjectA_WithB(config, args)

    # Global Helper functions ------------------------------------------------------------------------------
    global helper
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, config)

    # Check for blank Importer
    if config.Importer == None or config.Importer=="":
        config.Importer = f"{FEAPI_Helpers.FEAPI_Helper.get_windows_user_id()}@callminer.com"

    # Check if the tenant matches
    logger.info(f"Checking if API tenant matches target Config.Tenant ({config.Tenant})")
    config.tenant = helper.API_CheckTenantName(config.Tenant)

    # Check if FolderName exists
    config.SetFolderName(config.FolderName)
    if os.path.exists(config.FolderName) and os.path.isdir(config.FolderName):
        logger.info(f"Found input folder ({config.FolderName})")
        # Create a directory for JSON files
        config.json_output_dir = f"{config.FolderName}/JSON_for_import_into_{config.Tenant}_at_{config.timestamp}"

        # Create a directory for JSON files
        logger.info(f"Creating JSON import files in {config.json_output_dir}")
        os.makedirs(config.json_output_dir, exist_ok=True)

    else:
        logger.error(f"FolderName ({config.FolderName}) does not exist. Exiting...")
        exit(1)

    CreateScores()


if __name__ == "__main__":
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")



