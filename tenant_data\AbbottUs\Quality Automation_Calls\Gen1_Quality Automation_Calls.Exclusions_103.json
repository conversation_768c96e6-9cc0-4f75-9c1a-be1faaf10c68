{"BucketId": "103", "BucketFullname": "Categories.Quality Automation_Calls.Exclusions", "BucketDescription": "To identify calls that should not be scored for quality as no meaningful conversation or contact takes place on them, such as Ghost Calls, Voicemail, IVR, short calls with almost no words, etc.", "UserEditable": false, "BucketName": "Exclusions", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Exclusions", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 3.0, "HasPendingRetroRequest": false, "Created": "2023-12-19T10:09:59.833-05:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32486, "Name": "Audio Conferencing Centre", "Description": null, "ActionString": "<month%><address><got>$OR$$OR$[conference+id:1.5]$NOTNEAR:5$[please+enter+conference:2][audio+conferenc%+cent%:2]$OR$$OR$", "UserEntry": "\"audio conferenc* cent*\":2 \nOR \"please enter conference\":2 \nOR \"conference id\":1.5 NOT NEAR:5 (got|address|month*)", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32486, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32486, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32485, "Name": "IVR", "Description": null, "ActionString": "[call+may+be+monitor%|recorded:3][to+hear+options+again:2][speak|reach+agent|representative+press:3][vpn+agent+number:2.5]{listen+carefully+available+options:3}[enter+agent|origin+number:2]<password>[please+enter+the+vdn|vpn|vdm:2]$NOTNEAR:3$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"please enter the vdn|vpn|vdm\":2 NOT NEAR:3 (password)\nOR \"enter agent|origin number\":2 \nOR [listen carefully available options]:3 \nOR \"vpn agent number\":2.5 OR \"speak|reach agent|representative press\":3 OR \"to hear options again\":2 OR \"call may be monitor*|recorded\":3", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32485, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32485, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32484, "Name": "Short Outgoing Calls", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32484, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Direction", "TableFilterTypes": null, "FriendlyName": "Direction", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "O", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32484, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "WAVLength", "TableFilterTypes": null, "FriendlyName": "Duration", "BaseTypeName": "Numeric", "FormatString": "time", "Operator": "<", "Operand1": "60", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32484, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32484, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32483, "Name": "Less Than 100 Words", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32483, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "WordCount", "TableFilterTypes": null, "FriendlyName": "Word Count", "BaseTypeName": "Numeric", "FormatString": "#,0", "Operator": "<", "Operand1": "100", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32483, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32483, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32477, "Name": "Ghost Calls", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32477, "BucketID": 11, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32477, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32477, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-15T09:29:28.71", "ComponentCount": 5}