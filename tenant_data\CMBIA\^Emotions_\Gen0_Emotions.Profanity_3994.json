{"BucketId": "3994", "BucketFullname": "Categories.Emotions.Profanity", "BucketDescription": "Language indicating the customer or an agent is using profane expression in communication.", "UserEditable": false, "BucketName": "Profanity", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.<PERSON><PERSON><PERSON>", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2023-05-09T16:45:29.07-04:00", "Creator": "<EMAIL>", "Version": 2, "LanguageTag": "en", "EffectiveDate": "2023-05-09T16:45:30.48", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -23831, "Name": "Crap", "Description": "Dissatisfaction", "ActionString": "[all|with|through+this+crap:1.2][bunch|load|piece+crap:1]$OR$<other>[all|with|through+this+crap:1.2][bunch|load|piece+crap:1.2]$OR$$NOTNEAR:0$$OR$", "UserEntry": "(((\"bunch|load|piece crap\":1.2 OR \"all|with|through this crap\":1.2) NOT NEAR:0 (other)) OR (\"bunch|load|piece crap\":1 OR \"all|with|through this crap\":1.2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23830, "Name": "F word", "Description": "Dissatisfaction", "ActionString": "[what+the+fuck:1.2][stop|quit+fucking:1.2][fucking+issue%|problem%|wrong:1.2][mother+doctor%|fuck%:1][no+fuck%+way:1.2][that|this+is+fucked+up:1.2][go+fuck+off|your%:1.2][fuck+this|that+noise|shit:1.2][it|it`s|that%+fuck%+crazy:1][fuck%+absurd|bull%|insane|insanity|nuts|outrageous|ridiculous:1.2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"fuck* absurd|bull*|insane|insanity|nuts|outrageous|ridiculous\":1.2 OR \"it|it's|that* fuck* crazy\":1 OR \"fuck this|that noise|shit\":1.2 OR \"go fuck off|your*\":1.2 OR \"that|this is fucked up\":1.2 OR \"no fuck* way\":1.2 OR \"mother doctor*|fuck*\":1  OR \"fucking issue*|problem*|wrong\":1.2 OR \"stop|quit fucking\":1.2 OR \"what the fuck\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23829, "Name": "<PERSON><PERSON>", "Description": "Dissatisfaction", "ActionString": "[get%|going|gonna|basically+screwed+over:1.2][screw%+me+over:1][he|she|someone|somebody|you|totally|they+screwed|screw+up:1]$OR$$OR$", "UserEntry": "(\"he|she|someone|somebody|you|totally|they screwed|screw up\":1 OR \"screw* me over\":1 OR \"get*|going|gonna|basically screwed over\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23828, "Name": "Bullsh*t", "Description": "Dissatisfaction", "ActionString": "[bunch|complete|load|this|that%|total|ton|absolute|pile+bull|it`s|its+shit|crap|shite:1.2][bunch|complete|load|this|that%|total|absolute|ton|pile|its|it`s+bullshit:1.2]$OR$", "UserEntry": "(\"bunch|complete|load|this|that*|total|absolute|ton|pile|its|it's bullshit\":1.2 OR \"bunch|complete|load|this|that*|total|ton|absolute|pile bull|it's|its shit|crap|shite\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23827, "Name": "Sh*t", "Description": "", "ActionString": "[that`s|really|very+shitty|shit:1][shit+out+of+me:1][don`t|not|wouldn`t+give+shit:1.5][shitty|shit+service%|product|people|answer%:1][piece|pieces|jack|bunch|stupid|pile+shit:1.2][i|she|he|just|really|they+don`t|not|doesn`t+get|give+shit:1.2]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"I|she|he|just|really|they don't|not|doesn't get|give shit\":1.2 OR \"piece|pieces|jack|bunch|stupid|pile shit\":1.2 OR \"shitty|shit service*|product|people|answer*\":1 OR \"don't|not|wouldn't give shit\":1.5 OR \"shit out of me\":1 OR \"that's|really|very shitty|shit\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23826, "Name": "B*tch", "Description": "", "ActionString": "[stop|quit+bitch%:1][acting|act+like+bitch:1][don`t|no|not+act|acting|mean|trying+bitch%:1.2][bitch%+about:1][absolute|complete|crazy|dirty|dumb|fucking|need|son|stupid|total|useless+bitch%:1.2][you|your|you`re+a|are|being|little+bitch|witch:1.2]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"you|your|you're a|are|being|little bitch|witch\":1.2 OR \"absolute|complete|crazy|dirty|dumb|fucking|need|son|stupid|total|useless bitch*\":1.2 OR \"bitch* about\":1 OR \"don't|no|not act|acting|mean|trying bitch*\":1.2 OR \"acting|act like bitch\":1 OR \"stop|quit bitch*\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23825, "Name": "A**hole", "Description": "", "ActionString": "[he|he`s|pretty|she`s|she|such|you|you`re|your+are|an|being|just|much+asshole%|arsehole%:1.5][complete+asshole|arsehole:1.2]{call%|fucking|list|reach%|told+asshole%|arsehole%:1.2}$OR$$OR$", "UserEntry": "([call*|fucking|list|reach*|told asshole*|arsehole*]:1.2 OR \"complete asshole|arsehole\":1.2 OR \"he|he's|pretty|she's|she|such|you|you're|your are|an|being|just|much asshole*|arsehole*\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23824, "Name": "Pi**ed me off", "Description": "", "ActionString": "[piss%|tick%+me+off:1][pissed|ticked+about|already|because|but|that|when|with:1][absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that`s|very|was|were|i`m|we`re|more+pissed|ticked+off:1.2]$OR$$OR$", "UserEntry": "(\"absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|get|getting|gotten|gonna|increasingly|kinda|kind|little|makes|mostly|pretty|quite|rather|really|seriously|sorta|sort|somewhat|still|super|totally|that's|very|was|were|i'm|we're|more pissed|ticked off\":1.2 OR \"pissed|ticked about|already|because|but|that|when|with\":1 OR \"piss*|tick* me off\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23823, "Name": "G*Damn", "Description": "", "ActionString": "[god+damn+thing|thing`s|day:1][my|this+god+damn:1.2][goddamn+thing|thing`s|day:1][my|this+goddamn:1.2]$OR$$OR$$OR$", "UserEntry": "(\"my|this goddamn\":1.2 OR \"goddamn thing|thing`s|day\":1 OR \"my|this god damn\":1.2 OR \"god damn thing|thing`s|day\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23822, "Name": "Hell", "Description": "", "ActionString": "[what+the+hell:1.2][go+to+hell:1.2][can+go+hell:1.2]$OR$$OR$", "UserEntry": "(\"can go hell\":1.2 OR \"go to hell\":1.2 OR \"what the hell\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-05-09T16:45:30.48", "ComponentCount": 10}