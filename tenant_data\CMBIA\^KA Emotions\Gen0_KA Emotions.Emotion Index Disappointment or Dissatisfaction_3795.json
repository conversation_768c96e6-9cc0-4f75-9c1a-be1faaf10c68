{"BucketId": "3795", "BucketFullname": "Categories.KA Emotions.Emotion Index Disappointment or Dissatisfaction", "BucketDescription": "Words and phrases that indicate customer is dissatisfied or disappointed", "UserEditable": false, "BucketName": "Emotion Index Disappointment or Dissatisfaction", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotion Index Disappointment or Dissatisfaction", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:22:56.95-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2022-03-10T15:29:10.34", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25474, "Name": "Awful", "Description": null, "ActionString": "[super|extremely|incredibly|absolute%|awfully|beyond+frustrat%|nightmare:0.5][bad+news:0.5][fed+up:1][distraught:0.2][it`s|this+very+sad:1.5][find+this+sad:1.5][sad+news|part|because:1][also+sad:1]$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"also sad\":1 OR \"sad news|part|because\":1 OR \"find this sad\":1.5 OR \"it's|this very sad\":1.5 OR \"distraught\" OR \"fed up\":1  OR \"bad news\"\nOR \"super|extremely|incredibly|absolute*|awfully|beyond frustrat*|nightmare\"", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25473, "Name": "Disappointed", "Description": null, "ActionString": "[get%|feel%|felt|got+cheat|cheating|cheated:1]<you>[don`t|not+like|want|wanna+cheat|cheating|cheated:1]$NOTBEFORE:1$[cheat|cheating|cheated+me|people:0.9]<stinks><bummed><bummer><disappoint%>$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(disappoint*|bummer|bummed|stinks) OR \"cheat|cheating|cheated me|people\":.9 OR \"don't|not like|want|wanna cheat|cheating|cheated\":1 NOT BEFORE:1 you OR \"get*|feel*|felt|got cheat|cheating|cheated\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25472, "Name": "Dissatisfaction", "Description": null, "ActionString": "<dissatisf%>[not|isn`t|aren`t|won`t|doesn`t+satisf%:1]$OR$", "UserEntry": "\"not|isn't|aren't|won't|doesn't  satisf*\":1 OR dissatisf*", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25471, "Name": "Don`t appreciate", "Description": null, "ActionString": "[don`t|not|isn`t|didn`t+appreciat%:1]", "UserEntry": "\"don't|not|isn't|didn't appreciat*\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25470, "Name": "Embarassed", "Description": null, "ActionString": "<embarrass%>", "UserEntry": "embarrass*", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25469, "Name": "Mishandled", "Description": null, "ActionString": "[too+young|old:0.5][old+enough:0.5][not+squared+away:0.5][these+days:0.5][too+long:0.5][too+late:0.5]<wished><versed><clarified><interrupted><mandated><hated><retarded><bombarded><ungrateful>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[was|they|am+promised:1.8]<lied><granted><bothered><mishandled><settled><disconnected><unfortunate><sick><unlucky>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[not+lucky:0.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"not lucky\":.5 OR (unlucky|sick|unfortunate|disconnected|settled|mishandled|bothered|granted|lied) \n OR \"was|they|am promised\":1.8 OR (ungrateful|bombarded|retarded|hated|mandated|interrupted|clarified|versed|wished) OR \"too late\":0.5 OR \"too long\":0.5 OR \"these days\":0.5 OR \"not squared away\":0.5 OR \"old enough\":0.5 OR \"too young|old\":0.5", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25468, "Name": "Poor value", "Description": null, "ActionString": "[can`t|not+afford%:1][too+expensive|costly:1][not|isn`t+worth+it|price|cost|money:1.2][not|poor|terrible|awful+value:1][cost%+much:1.2][too+much+money:1.2]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"too much money\":1.2 OR \"cost* much\":1.2 OR \"not|poor|terrible|awful value\":1 OR \"not|isn't worth it|price|cost|money\":1.2 OR \"too expensive|costly\":1 OR \"can't|not afford*\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25467, "Name": "<PERSON><PERSON>", "Description": null, "ActionString": "[made+mistake:0.9][didn`t+even|realize:0.5][wish+you|i|we|they:0.5]<regret%>[biggest+thing:0.5]$OR$$OR$$OR$$OR$", "UserEntry": "\"biggest thing\":0.5 OR regret* OR \"wish you|i|we|they\":0.5 OR  \"didn`t even|realize\":0.5 OR \"made mistake\":0.9", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25466, "Name": "Sadness", "Description": null, "ActionString": "[heart+wrenching:0.5]<emotional><distraught><sad><devasta%><depressed><sadness><grievance><mushy><whiny><cranky>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(cranky|whiny|mushy|grievance|sadness|depressed|devasta*|sad|distraught|emotional) OR \"heart wrenching\":0.5", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25465, "Name": "Worse than", "Description": null, "ActionString": "[better+than+nothing:1.5][worse+than:1]$OR$", "UserEntry": "\"worse than\":1 OR \"better than nothing\":1.5", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2022-03-10T15:29:10.34", "ComponentCount": 10}