{"BucketId": "20", "BucketFullname": "Categories.Customer Experience.Can you hold", "BucketDescription": "Identifies instances where a request to hold on the line is made, such as hold on a second, can you hold for a couple of minutes, etc.", "UserEditable": false, "BucketName": "Can you hold", "SectionName": "Customer Experience", "SectionNameDisplay": "Customer Experience", "SectionId": 34, "RetroStartDate": "2024-02-03T00:00:00", "CategoryDisplay": "Customer Experience.Can you hold", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2023-09-29T04:42:39.057-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-02-03T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2023-09-27T00:00:00", "EndDate": "2023-10-01T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -32669, "Name": "Can you hold?", "Description": null, "ActionString": "<transfer>[they`ll|will|be+assist%|help+more|further:2][you+over+to:1](quality automation_calls.call closing)(quality automation_calls.further questions)(customer experience.call closing)$OR$$OR$$OR$$OR$$OR$~silence:+25~[bear|bare+with+me:1][please+bear|bare:1]$OR$$BEFORE:15$~silence:+30~[you%+able+hold|holding|hang%:1.5][could|please+two|one|few|couple+minut%:2][pack|back+with+you:2][thank%+you|for+patien%:1.7]{please+don`t|not+hang|hung+up:2}[no|not|know+more|long%+two|one|few|couple+minut%:2][for|within|leave+two|one|few|couple+minut%:2][put+quick|brief|short+call:2]{hold%|hang%+quick|quickly|brief|briefly|short|temporarily|little:2}<survey><service>$OR${could|would|can|please|minute%|second%|moment%+hang%|hold%|hole+on:2}[thank%+for:1.3]{hold%|hang%+line|online|you|put|putting|add%|place|placing|ask:1.3}$NOTNEAR:5$$OR$$NOTNEAR:3$[minute%|second%|moment%+please:1][give|allow+me+few|couple|minute%|second%|moment%:1.8]<survey><connect%><transfer%>$OR$$OR${you+hold%|hang%|hole+on|line|online:1.5}$NOTNEAR:5$[need+talk:1.2][need|have+consult%:1.6][apolog%|sorry+hold%|hang%+on|line|online:1.2]<transfer%><connect%>$OR$[stay%|wait%+line|while:1.5][hold%|hang%+while:1.4]$OR$$NOTNEAR:3$[be|back+you+momentarily|soon|shortly|minut%|moment%|second%|within:1.7][i`ll|will|gonna|going|i|am+return%+momentarily|soon|shortly|minut%|second%|moment%|within:2.5]<receipt><reach%><contact%><calling>$OR$$OR$$OR${be|get+back+line|minute%|moment%|ok|okay|alright|within|you|right:1.9}$NOTNEAR:5${mind|mine|mined|problem%|trouble%+hold|holding|hang%:2.5}$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$BEFORE:30$$OR$$NOTAFTER:15$", "UserEntry": "(([mind|mine|mined|problem*|trouble* hold|holding|hang*]:2.5\nOR [be|get back line|minute*|moment*|ok|okay|alright|within|you|right]:1.9 NOT NEAR:5 (calling|contact*|reach*|receipt)\nOR \"i'll|will|gonna|going|i|am return* momentarily|soon|shortly|minut*|second*|moment*|within\":2.5\nOR \"be|back you momentarily|soon|shortly|minut*|moment*|second*|within\":1.7\nOR (\"hold*|hang* while\":1.4\nOR \"stay*|wait* line|while\":1.5) NOT NEAR:3 (connect*|transfer*)\nOR \"apolog*|sorry hold*|hang* on|line|online\":1.2\nOR \"need|have consult*\":1.6 \nOR \"need talk\":1.2\nOR [you hold*|hang*|hole on|line|online]:1.5 NOT NEAR:5 (transfer*|connect*|survey) \nOR \"give|allow me few|couple|minute*|second*|moment*\":1.8\nOR \"minute*|second*|moment* please\":1\nOR ([hold*|hang* line|online|you|put|putting|add*|place|placing|ask]:1.3 NOT NEAR:5 \"thank* for\":1.3\nOR [could|would|can|please|minute*|second*|moment* hang*|hold*|hole on]:2) NOT NEAR:3 (service|survey)\nOR [hold*|hang* quick|quickly|brief|briefly|short|temporarily|little]:2\nOR \"put quick|brief|short call\":2                                                                                  \nOR \"for|within|leave two|one|few|couple minut*\":2\nOR \"no|not|know more|long* two|one|few|couple minut*\":2\nOR [please don't|not hang|hung up]:2\nOR \"thank* you|for patien*\":1.7\nOR \"pack|back with you\":2\nOR \"could|please two|one|few|couple minut*\":2\nOR \"you* able hold|holding|hang*\":1.5) BEFORE:30 SIL:[+30]\nOR (\"please bear|bare\":1 \nOR \"bear|bare with me\":1) BEFORE:15 SIL:[+25]) NOT AFTER:15 (CAT:[Customer Experience.Call Closing] OR CAT:[Quality Automation_Calls.Further Questions]  OR CAT:[Quality Automation_Calls.Call Closing]  OR \"you over to\":1 OR \"they'll|will|be assist*|help more|further\":2 OR transfer)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32669, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32669, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-05-03T09:49:11.357", "ComponentCount": 1}