{"BucketId": null, "BucketFullname": "", "BucketDescription": "Identifies that the agent conducts the call with a professional attitude.", "UserEditable": false, "BucketName": "Agent Professionalism", "SectionName": "", "SectionNameDisplay": "", "SectionId": 2317, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 2.0, "HitMaxWeight": 4.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T16:41:34", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T16:41:34", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 2.0, "SearchComponents": [{"Id": null, "Name": "Does Not Disclose Information", "Description": null, "ActionString": "[if+you+don`t+have:1.7]=1[don`t+have+information+provide|give:2.5]=1$NOTNEAR:3$[can`t+inform%|provide+you+moment:2.5]=1[any+replacements:0.5]=1[case+number:1]=1[i+can`t+provide+you:2]=1$NOTNEAR:5$$NOTNEAR:4$[can+not+disclose:1.5]=1$AND$[can`t+disclose:1.5]=1[you%+provide%|gave:1.4]=1<profile>=1$OR$[this+all|only+information+have:1.8]=1$NOTNEAR:3$$OR$$OR$$OR$$OR$", "UserEntry": "(\"this all|only information have\":1.8 NOT NEAR:3 ((profile) OR \"you* provide*|gave\":1.4) OR \"can't disclose\":1.5 OR \"can not disclose\":1.5 \"i can't provide you\":2 NOT NEAR:5 (\"case number\":1) NOT NEAR:4 (\"any replacements\") OR \"can't inform*|provide you moment\":2.5 OR \"don't have information provide|give\":2.5 NOT NEAR:3 \"if you don't have\":1.7)=Agent", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Does Not Disparage Colleague", "Description": null, "ActionString": "[agent|previous|contact+should+mentioned:1.5][you|was|been|were|have|they+promised:1][you|they|she|they`ve|have|has|colleague|he`s|gentleman+told+me:1.5]=2{gentleman|lady|person|earlier|yeasterday|afternoon|colleague%+i+spoke+to:2}[your|my|our|a+colleague%:1.2][previous|%other|different+agent:1]$OR$$OR$$OR$$OR$[made+mistake:1]=1[don`t+worry:1]=1[no+you+have+to:1.2]=1$NOTNEAR:2$[did+not+happen:1.2]=1[wrong+information:1]=1<drop>=1<amount>=1<whole>=1<transfer>=1<department>=1<burry>=1<person>=1<address>=1<one>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[not+correct:1]=1$NOTNEAR:30$$OR$$OR$$OR$$OR$$AFTER:30$$OR$$NOT$", "UserEntry": "-((((\"not correct\":1 NOT NEAR:30 (one|address|person|burry|department|transfer|whole|amount|drop)) OR \"wrong information\":1 OR \"did not happen\":1.2 OR \"no you have to\":1.2 NOT NEAR:2 (\"don`t worry\":1) OR \"made mistake\":1)=Agent AFTER:30 (\"previous|*other|different agent\":1 OR \"your|my|our|a colleague*\":1.2\nOR [gentleman|lady|person|earlier|yeasterday|afternoon|colleague* i spoke to]:2 \nOR \"you|they|she|they`ve|have|has|colleague|he`s|gentleman told me\":1.5=Customer \nOR \"you|was|been|were|have|they promised\":1)) OR \"agent|previous|contact should mentioned\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Does Not Maim Brand Credibility", "Description": null, "ActionString": "[new+product%:1.2]=1[we+had+issue:2]=1$NEAR:5$<like>=1<abbott>=1<company>=1$OR$[not+happy|satified:0.5]=1$NEAR:5$$NOTNEAR:2$[with+company:1.2]=1[not+happy:0.5]=1[not+satisfied:0.5]=1<unimpressed>=1$OR$$OR$$NEAR:5$$OR$$OR$$NOT$", "UserEntry": "-((((unimpressed|\"not satisfied\"|\"not happy\") NEAR:5 \"with company\":1.2) \nOR (\"not happy|satified\" NEAR:5 (company|abbott)) NOT NEAR:2 (like)\nOR (\"we had issue\":2 NEAR:5 \"new product*\":1.2))=AGENT)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Does Not Use Adverse Language", "Description": null, "ActionString": "[sensor+not+working:1.2]=1!40-100%[in+order+for+me+to:1.2]=1[maybe+because:1.2]=1[sorry+for+inconvenience:1.2]=1[free+of+charge:1.2]=1$OR$$OR$$OR$$OR$<survey>[a+couple+of+minutes:1.2][it+would+help:1.4][it+help+me:1.4][do+something+for+me:1.2][small+favor:1.2][do+me+favor:1.4]$OR$$OR$$OR$$OR$$OR$$NEAR:12$$OR$$NOT$", "UserEntry": "-((\"do me favor\":1.4 OR \"small favor\":1.2 OR \"do something for me\":1.2 OR \"it help me\":1.4 OR \"it would help\":1.4 OR \"a couple of minutes\":1.2) NEAR:12 (survey) OR (\"free of charge\":1.2 OR \"sorry for inconvenience\":1.2 OR \"maybe because\":1.2 OR \"in order for me to\":1.2 OR \"sensor not working\":1.2{40%-100%})=AGENT)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T16:41:34", "ComponentCount": 4}