# INPUT: categoryimport<PERSON>lena<PERSON>, NewSectionName, NewCategoryName
# IF BLANK, append "imported_DateTime" to current SectionName or CategoryName

# Check to see if SectionAlready Exists, if not create it
# Check to see if CategoryName already exists, if so, append "imported_Datetime" to CategoryName

#Load Category JSON from File
#Alter Section name and Categoryname
#Set BucketID = null so it creates a new category
#Remove unnecessary JSON components

#Call Category Create API

'''
run this in environment to download libraries

pip install requests
pip install pandas
pip install numpy
pip install openpyxl
'''
import FEAPI_Helpers
import json
import os
import pandas as pd
from pathlib import Path
from datetime import datetime

now = datetime.now()
current_date_time = now.strftime("%Y-%m-%dT%H:%M:%S")
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
DEBUG = False


import logging

# Set up logging configuration
logger = logging.getLogger('CreateCategory')
logger.setLevel(logging.DEBUG)  # Set the logging level to DEBUG

# Create handlers
console_handler = logging.StreamHandler()  # Log to console
file_handler = logging.FileHandler(f'logs\\CreateCategory{timestamp}.log.txt')  # Log to log.txt
error_handler = logging.FileHandler(f'logs\\CreateCategory-{timestamp}.errors.txt')  # Log to errors.txt
warning_handler = logging.FileHandler(f'logs\\CreateCategory-{timestamp}.warnings.txt')  # Log to errors.txt

# Set levels for handlers
console_handler.setLevel(logging.INFO)  # Show INFO level and above in console
file_handler.setLevel(logging.DEBUG)  # Show DEBUG level and above in log.txt
error_handler.setLevel(logging.ERROR)  # Show ERROR level and above in errors.txt
warning_handler.setLevel(logging.WARN)  # Show ERROR level and above in errors.txt

# Create formatters and add them to the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)
error_handler.setFormatter(formatter)
warning_handler.setFormatter(formatter)


# Add the handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)
logger.addHandler(error_handler)
logger.addHandler(warning_handler)

#Global Helper functions ------------------------------------------------------------------------------
helper = FEAPI_Helpers.FEAPI_Helper("helper",logger, "feapi")








#API info
# token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
# swagger found here: http://feapi.callminer.net
# for FISUS, use feapif.callminer.net
# once logged in to your tenant, copy the JWT token from the URL line into the token variable



def API_CreateCategory(category_data):
    # token must be retrieved manually as JWT from swagger as AD security is not allowed for the security API to programmatically get token.
    # swagger found here: http://feapi.callminer.net
    # once logged in to your tenant, copy the JWT token from the URL line into the token variable


    # Define the URL and headers
    url = f'https://{helper.API}.callminer.net/api/v2/categories'

    # Define the payload with the loaded category data
    payload = category_data

    # Make the POST request
    api_response = helper.ApiPostJson(url, payload)

    catid = api_response
    bucketname = category_data["BucketName"]
    logger.info(f"Category {bucketname} created as catid={catid}")
    return catid

def API_CreateSection(sectionname,importer, UseExistingSection):
    # Define the URL and headers
    url = f'https://{helper.API}.callminer.net/api/v2/sections/category'

    # Define the payload with the loaded category data
    ownername = importer
    if importer == "null":
        ownername = None
    payload = {
        "SectionName": sectionname,
        "OwnerName": ownername ,
        "VisibilityMode": "All",
        "Groups": []
    }

    # Make the POST request
    api_response = helper.ApiPostJson(url, payload)
    if api_response != None:
        sectionid = api_response
        logger.info (f"Section {sectionname} created as sectionid={sectionid}")
    else:
        logger.info(f"Section CREATE FAILED")
        sectionid= None

    return sectionid






def LoadCategoryXLS(foldername, xlsfilename):
    xls_filename = os.path.join(foldername, xlsfilename)
    df_categories = None
    if os.path.exists(xls_filename):
        try:
            df_categories = pd.read_excel(xls_filename, sheet_name="ALL Categories")
            logger.info(f"Successfully loaded 'ALL categories' sheet from {xls_filename}")
            # You can now work with the df_categories DataFrame
            # print(df_categories.head()) # Example: print the first few rows
        except FileNotFoundError:  # redundant check, but good practice
            logger.error(f"File not found: {xls_filename}")
        except KeyError:
            logger.error(f"Sheet 'categories' not found in {xls_filename}")
        except Exception as e:
            logger.error(f"An error occurred while loading the file: {e}")
    else:
        logger.error(f"File {xls_filename} does not exist.")

    return df_categories






def CreateCategory(importer, json_file, json_output_dir, sectionid, df_AllCategories):
    # Load JSON data from the file
    data = helper.load_json_from_file(json_file)
    if data is not None:
        logger.info(f"Loaded JSON data for: {json_file}")
        # logger.info(json.dumps(data, indent=4))
    # Print the section and category names
    bucketfullname = data['BucketFullname']
    logger.info(f"CategoryName: {bucketfullname}")

    #Check that Bucket doesnt already exist
    if bucketfullname in df_AllCategories['BucketFullname']:
        logger.warning(f"Bucket ({bucketfullname}) ALREADY EXISTS. Skipping....")
        return None



    # Update the BucketId and SectionName for each dictionary in the list
    data['BucketId'] = None
    data['SectionId'] = sectionid
    data['SectionName'] = ""
    data['SectionNameDisplay'] = ""
    data['CategoryDisplay'] = ""
    data['BucketFullname'] = ""  # f"Categories.{data['SectionName']}.{data['BucketName']}"
    data['RetroStartDate'] = None
    data['Created'] = current_date_time
    data['EffectiveDate'] = current_date_time
    data['LastModified'] = current_date_time
    data['LastNDays'] = None
    data['TimeFrame'] = "Yesterday"
    # Update owner
    data['Creator'] = importer
    data['Username'] = importer

    # Update SearchComponent keys
    for sc in data['SearchComponents']:
        sc['Id'] = None

        filterstring, filterdeplist = helper.ParseFilters(sc['Filters'], df_AllCategories)

        if sc['Filters'] != []:
            # Add Filters for this search component to the output file
            filterfilename = os.path.join(json_output_dir, f"Import_Filters.xlsx")
            helper.LogFilter(bucketfullname, sc['Name'], filterstring, filterfilename)
            logger.warning(
                f"Saving Filters for SearchComponent:{bucketfullname}.{sc['Name']}.\tFilterString = {filterstring}.")

        sc['Filters'] = []


        # Check for Category Dependency
        matches, eqldeplist = helper.ParseEQL(sc['UserEntry'], df_AllCategories)

        if matches != "":
            eqldependfilename = os.path.join(json_output_dir, f"Import_EQLDependencies.xlsx")
            helper.LogEQLDependency(bucketfullname, sc['Name'], matches, eqldependfilename)
            logger.warning(
                f"Saving EQL Dependencies found for SearchComponent:{bucketfullname}.{sc['Name']}.\tEQLDependencies = {matches}")
        #else:
            #logger.info(f"No EQL Dependencies found for SearchComponent:{bucketfullname}.{sc['Name']}.\tEQLDependencies = {matches}")

    # Dump the importable Category JSON
    # Write JSON object to a file
    json_for_import_filename = os.path.join(json_output_dir, f"{helper.cleanfilename(bucketfullname)}.json")
    with open(f"{json_for_import_filename}", 'w') as json_file_import:
        json.dump(data, json_file_import, indent=4)  # `indent=4` for pretty-printing
    # Import the category into the newly created section
    global DEBUG
    catresult=None
    if DEBUG == False:
        catresult = API_CreateCategory(data)

    return catresult






#---------------------------------------------------------------------------------------

import argparse
def main():
    global timestamp
    global helper
    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, "feapi")


    parser = argparse.ArgumentParser(description='Script to process folder and section names')
    parser.add_argument('--tenant', type=str, help='Name of the tenant to double-check before importing')
    parser.add_argument('--foldername', type=str, help='Name of the folder')
    parser.add_argument('--xlsfilename', type=str, help='Name of the export xls filename')
    parser.add_argument('--section_name', type=str, help='Name of the section')
    parser.add_argument('--importer', nargs='?', type=str, default="null", help='Importer name (optional)')
    parser.add_argument('--api', nargs='?', type=str, default="feapi", help='API (feapi/feapif) (optional)')
    parser.add_argument('--useexistingsection',  nargs='?',type=int, choices=[0, 1], default=0, help='UseExistingSection=1 if you want to add existing section')

    args = parser.parse_args()

    tenant = args.tenant
    foldername = args.foldername
    xlsfilename = args.xlsfilename
    section_name = args.section_name if args.section_name else f"Imported-{foldername}-{timestamp}"
    importer = args.importer if args.importer else f"{FEAPI_Helpers.FEAPI_Helper.get_windows_user_id()}@callminer.com"
    API = args.api
    UseExistingSection = args.useexistingsection==1

    # Print the captured arguments for demonstration purposes
    logger.info(f"USAGE = CreateCategory.py <foldername> <SectionName> <importerName> [--UseExistingSection]")
    logger.info(f"tenant: {tenant}")
    logger.info(f"Foldername: {foldername}")
    logger.info(f"xlsfilename: {xlsfilename}")
    logger.info(f"Sectionname: {section_name}")
    logger.info(f"Importer: {importer}")
    logger.info(f"API: {API}")
    logger.info(f"UseExistingSection: {UseExistingSection}")

    helper = FEAPI_Helpers.FEAPI_Helper("helper", logger, API)

    #Check tenant
    apitenant = helper.API_CheckTenantName(API)
    if tenant.lower() != apitenant.lower():
        logger.error(f"Target Tenant ({tenant}) does NOT MATCH api tenant ({apitenant}). Update token.txt. Exiting...")
        exit(1)

    #Try to load Categories from inputfolder
    df_XLSCategories = LoadCategoryXLS(foldername,xlsfilename)
    if df_XLSCategories.empty:
        logger.error(f"Unable to load Category info from ({xlsfilename}) - Exiting")
        exit(1)

    #Check to see if section_name already exists, if it does then rename it to OLD_timestamp
    sectionid=None
    df_sections = helper.API_GetAllSections()
    if section_name in df_sections['SectionName'].values:
        if UseExistingSection:
            sectionid = int(df_sections.loc[df_sections['SectionName'] == section_name, 'SectionID'].iloc[0])
            logger.info(f"Using existing section ({section_name} as SectionID = {sectionid}")
        else:
            logger.error(f"Section ({section_name}) already exists, and UseExistingSection = false. Exiting")
            exit(1)
    else:
        sectionid = API_CreateSection(section_name, importer, UseExistingSection)
        logger.info (f"Successfully created ({section_name} as SectionID = {sectionid}")


    # Create a Path object for the folder
    folder_path = Path(foldername)
    json_files = folder_path.glob('*.json')
    json_files = list(json_files)
    logger.info (f"Found ({len(json_files)}) json files in {folder_path}...")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_output_dir = folder_path / f"JSON_for_import_into_{tenant_at_{timestamp}"

    # Create a directory for JSON files
    logger.info (f"Creating JSON import files in {json_output_dir}")
    os.makedirs(json_output_dir, exist_ok=True)

    # Iterate through the generator of json files
    i=1
    for json_file in json_files:
        logger.info(f"Processing ( {i} / {len(json_files)} ) file = {json_file} -------------------------------------------------")
        i = i+1

        CreateCategory(importer, json_file, json_output_dir, sectionid, df_XLSCategories)



if __name__ == "__main__":
    main()

    helper.show_error_file_contents(warning_handler, "WARNINGS")
    helper.show_error_file_contents(error_handler, "ERRORS")



