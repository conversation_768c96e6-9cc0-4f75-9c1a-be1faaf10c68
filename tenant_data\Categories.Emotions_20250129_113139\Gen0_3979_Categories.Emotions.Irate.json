{"BucketId": "3979", "BucketFullname": "Categories.Emotions.Irate", "BucketDescription": "Captures language indicating the customer is <PERSON><PERSON>.", "UserEditable": false, "BucketName": "<PERSON><PERSON>", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.<PERSON><PERSON>", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2023-05-04T11:03:15.883-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "en", "EffectiveDate": "2023-07-17T12:59:55.453", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -23230, "Name": "Appalling", "Description": "", "ActionString": "[it|it`s|he|she|they|you|you`ve|they`re|you`re|he`s|she`s|its+was|has|been|is|are+horribl%:1.5]{service|treated|treatment+appall%|badly|disgust%|abysmal%|atrocious%|contempt%|deplorabl%|derisory|dire|disgrace%|dismal%|dismay%|dreadful%|ghastly|horrendous%|horrif%|horribl%|poor|poorly|rotten|rubbish|scandal%|shocking%|unforgiv%:1.5}[i+think|feel|thought|felt+appalling|disgusting|abysmal|atrocious|contempt%|deplorable|derisory|dire|disgrace%|dismal|dismay%|dreadful%|ghastly|horrendous|horrific|horrible|poor|rotten|rubbish|scandal%|shocking|unforgivable:1.5][it|it`s|that|that`s|this|which|been|i|i`m|complete%|absolute%|total%|utter%|really|frankly|actually|quite|pretty+appall%|disgust%|abysmal%|atrocious%|contempt%|deplorabl%|derisory|dire|disgrace%|dismal%|dismay%|dreadful%|ghastly|horrendous%|horrif%|rotten|rubbish|scandal%|shocking%|unforgiv%:1.5]$OR$$OR$$OR$", "UserEntry": "(\"it|it's|that|that's|this|which|been|i|i'm|complete*|absolute*|total*|utter*|really|frankly|actually|quite|pretty appall*|disgust*|abysmal*|atrocious*|contempt*|deplorabl*|derisory|dire|disgrace*|dismal*|dismay*|dreadful*|ghastly|horrendous*|horrif*|rotten|rubbish|scandal*|shocking*|unforgiv*\":1.5 OR \"i think|feel|thought|felt appalling|disgusting|abysmal|atrocious|contempt*|deplorable|derisory|dire|disgrace*|dismal|dismay*|dreadful*|ghastly|horrendous|horrific|horrible|poor|rotten|rubbish|scandal*|shocking|unforgivable\":1.5 OR [service|treated|treatment appall*|badly|disgust*|abysmal*|atrocious*|contempt*|deplorabl*|derisory|dire|disgrace*|dismal*|dismay*|dreadful*|ghastly|horrendous*|horrif*|horribl*|poor|poorly|rotten|rubbish|scandal*|shocking*|unforgiv*]:1.5 OR \"it|it's|he|she|they|you|you've|they're|you're|he's|she's|its was|has|been|is|are horribl*\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23229, "Name": "Incompetence", "Description": "", "ActionString": "<incompetent><incompetenc%>$OR$", "UserEntry": "(incompetenc*|incompetent)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23228, "Name": "No need to be rude", "Description": "", "ActionString": "[no+need+be+disrespectful|nasty|rude|awful:1.5]{don`t|didn`t|wasn`t|not+want|wanna|have|mean|trying|necessary+be+disrespectful|nasty|rude|awful:1.5}[without+being+rude:1.2]$OR$$OR$", "UserEntry": "(\"without being rude\":1.2 OR [don't|didn't|wasn't|not want|wanna|have|mean|trying|necessary be disrespectful|nasty|rude|awful]:1.5 OR \"no need be disrespectful|nasty|rude|awful\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23227, "Name": "Pain in backside", "Description": "", "ActionString": "<i`m>[pain+in|the+arse|bum|backside|back:1.5]$NOTAFTER:1$", "UserEntry": "((\"pain in|the arse|bum|backside|back\":1.5) NOT AFTER:1 (i'm))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23226, "Name": "Pissed off", "Description": "", "ActionString": "[getting|frankly|really+pissed+off:1.5][pissed|pissing|piss|pisses+me+off:1.2]$OR$", "UserEntry": "(\"pissed|pissing|piss|pisses me off\":1.2 OR \"getting|frankly|really pissed off\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23225, "Name": "Useless", "Description": "", "ActionString": "<press><funds><fund>$OR$$OR$[complete%|absolute%|total%|utter%|really|frankly|actually|quite|pretty+hopeless|useless:1.5]{service|treated|treatment+abject|clueless|feeble|hopeless|inadequate|ineffect%|inept%|insufficient|pathetic%|pitiful|pointless%|unsatisfactory|useless|woeful|worthless|wretched|unjustifi%:1.5}[i+think|feel|thought|felt+abject|clueless|feeble|hopeless|inadequate|ineffect%|inept%|insufficient|pathetic%|pitiful|pointless%|unsatisfactory|useless|woeful|worthless|wretched|unjustifi%:1][it|it`s|that|that`s|this|which|been|you|you`ve|you`re|your|they|they`ve|they`re|complete%|absolute%|total%|utter%|really|frankly|actually|quite|pretty|very+abject|clueless|inadequate|ineffect%|inept%|insufficient|pitiful|unsatisfactory|woeful|worthless|wretched|unjustifi%:1.5]$OR$$OR$$OR$$NOTNEAR:1$", "UserEntry": "((\"it|it's|that|that's|this|which|been|you|you've|you're|your|they|they've|they're|complete*|absolute*|total*|utter*|really|frankly|actually|quite|pretty|very abject|clueless|inadequate|ineffect*|inept*|insufficient|pitiful|unsatisfactory|woeful|worthless|wretched|unjustifi*\":1.5 OR \"i think|feel|thought|felt abject|clueless|feeble|hopeless|inadequate|ineffect*|inept*|insufficient|pathetic*|pitiful|pointless*|unsatisfactory|useless|woeful|worthless|wretched|unjustifi*\":1 OR [service|treated|treatment abject|clueless|feeble|hopeless|inadequate|ineffect*|inept*|insufficient|pathetic*|pitiful|pointless*|unsatisfactory|useless|woeful|worthless|wretched|unjustifi*]:1.5 OR \"complete*|absolute*|total*|utter*|really|frankly|actually|quite|pretty hopeless|useless\":1.5) NOT NEAR:1 (fund|funds|press))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23224, "Name": "Yelling & Screaming", "Description": "", "ActionString": "[don`t|quit|stop+scream|screaming|shout|shouting|yell|yelling:1.5][no+need+scream|shout|yell:1.2]{shout|shouting|yell|yelling+scream|screaming:1.2}[scream|screaming|yell|yelling+about|me:1.2]$OR$$OR$$OR$", "UserEntry": "(\"scream|screaming|yell|yelling about|me\":1.2 OR [shout|shouting|yell|yelling scream|screaming]:1.2 OR \"no need scream|shout|yell\":1.2 OR \"don't|quit|stop scream|screaming|shout|shouting|yell|yelling\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23223, "Name": "What hell going on", "Description": "", "ActionString": "[what+are|you+play|playing|plain|plane+at:1.5][what+the+hell+happen%:2][what+the+hell|hells|hell`s+going+on:2]$OR$$OR$", "UserEntry": "(\"what the hell|hells|hell's going on\":2 OR \"what the hell happen*\":2 OR \"what are|you play|playing|plain|plane at\":1.5)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -23222, "Name": "Unhappy", "Description": "", "ActionString": "[if+unhappy:1][absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|getting|just|incredibly|increasingly|kinda|kind|little|makes|pretty|somewhat|still|really|seriously|so|sort|sorta|totally|very|obviously+unhappy|upset|angry:1.5][unhappy|upset|angry+about|with:1.2][i`m|we`re|she`s|he`s|they%|he|she+unhappy:1.5][i|we|he|she|they+am|are|is|was|were+unhappy:1.5]$OR$$OR$$OR$$NOTNEAR:2$", "UserEntry": "((\"i|we|he|she|they am|are|is|was|were unhappy\":1.5 OR \"i`m|we`re|she`s|he`s|they*|he|she unhappy\":1.5 OR \"unhappy|upset|angry about|with\":1.2 OR \"absolutely|already|awfully|become|becoming|became|beyond|certain|certainly|completely|definitely|exceedingly|extremely|fairly|frankly|getting|just|incredibly|increasingly|kinda|kind|little|makes|pretty|somewhat|still|really|seriously|so|sort|sorta|still|totally|very|obviously unhappy|upset|angry\":1.5) NOT NEAR:2 (\"if unhappy\":1))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-07-17T12:59:55.453", "ComponentCount": 9}