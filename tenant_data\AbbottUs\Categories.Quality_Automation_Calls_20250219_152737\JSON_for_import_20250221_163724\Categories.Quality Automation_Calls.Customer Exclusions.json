{"BucketId": null, "BucketFullname": "", "BucketDescription": "To identify where the customer is going away from the phone or conversation for at least 5 seconds.", "UserEditable": false, "BucketName": "Customer Exclusions", "SectionName": "", "SectionNameDisplay": "", "SectionId": -99, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T16:37:22", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T16:37:22", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Customer Exclusions", "Description": null, "ActionString": "~silence:+5~[hold+off|on|sec:1.2][bear+with+me:1.2][let+me+see+here:1.3][let+me+get+my:1.2][i`ll+try:1.2][i+will+try:1.2][hold+on:1.2][wait|hold+sec%|moment%|minute%|bit:1.2][try%+find|look|search|get:1.2][i`ll+be+back:1.2][one|just+second|moment|minute+please:1.6][be+right+back:1.2][a+moment|minute|second:1.2][just+a+minute%|second%|moment%:1.2][give+me+a|one+minute%|sec%|moment%:1.5][let+me+look|find|search|see|check:1.2][please+wait|hold:1.2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$BEFORE:3$", "UserEntry": "(\"please wait|hold\":1.2 \nOR \"let me look|find|search|see|check\":1.2 \nOR \"give me a|one minute*|sec*|moment*\":1.5\nOR \"just a minute*|second*|moment*\":1.2 OR \"a moment|minute|second\":1.2\nOR \"be right back\":1.2 OR \"one|just second|moment|minute please\":1.6\nOR \"i'll be back\":1.2 \nOR \"try* find|look|search|get\":1.2 \nOR \"wait|hold sec*|moment*|minute*|bit\":1.2  OR \"hold on\":1.2\nOR \"i will try\":1.2 OR \"i'll try\":1.2  \nOR \"let me get my\":1.2 OR \"let me see here\":1.3\nOR \"bear with me\":1.2 \nOR \"hold off|on|sec\":1.2)  BEFORE:3 SIL:[+5]", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T16:37:22", "ComponentCount": 1}