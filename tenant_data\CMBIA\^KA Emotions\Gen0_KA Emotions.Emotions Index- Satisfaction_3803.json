{"BucketId": "3803", "BucketFullname": "Categories.KA Emotions.Emotions Index- Satisfaction", "BucketDescription": "Words and phrases that indicate customer is satisfied", "UserEditable": false, "BucketName": "Emotions Index- Satisfaction", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotions Index- Satisfaction", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:52:38.96-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-02-28T09:28:16.04", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25418, "Name": "Better than", "Description": null, "ActionString": "[quicker|better|higher|sooner|earlier|faster+than:0.5][more+than:0.5]$OR$", "UserEntry": "\"more than\"|\"quicker|better|higher|sooner|earlier|faster than\"", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25417, "Name": "Can`t complain", "Description": null, "ActionString": "[can`t|cannot+beat|complain:1]", "UserEntry": "(\"can`t|cannot beat|complain\":1)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25416, "Name": "Pretty Good", "Description": null, "ActionString": "[straight+forward:0.5][pretty|fairly+soon|big|cool|fun|nice|decent|cheap|strong|pretty|good|shape|solid|thorough:0.9]$OR$", "UserEntry": "\"pretty|fairly soon|big|cool|fun|nice|decent|cheap|strong|pretty|good|shape|decent|solid|pretty|thorough\":0.9|\"straight forward\"", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25415, "Name": "Satisfied", "Description": null, "ActionString": "[pleasant%|nice|great+surprise%:1][happy+camper:1]<thrilling><thriller><thrill><impress><delighted><thrilled><impressed><pleased><satisf%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(satisf*|pleased|impressed|thrilled|delighted|impress|thrill|thriller|thrilling) OR \"happy camper\":1 OR \"pleasant*|nice|great surprise*\":1", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-02-28T09:28:16.04", "ComponentCount": 4}