{"BucketId": "4200", "BucketFullname": "Categories.deptest.deptest2a", "BucketDescription": "base cat 2a", "UserEditable": false, "BucketName": "deptest2a", "SectionName": "deptest", "SectionNameDisplay": "deptest", "SectionId": 2309, "RetroStartDate": null, "CategoryDisplay": "deptest.deptest2a", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-22T14:02:07.797-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-22T14:02:08.27", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -22477, "Name": "deptest2a_sc1", "Description": null, "ActionString": "[test+dep+2a+sc1:1.1]", "UserEntry": "\"Test dep 2a sc1\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-22T14:02:08.27", "ComponentCount": 1}