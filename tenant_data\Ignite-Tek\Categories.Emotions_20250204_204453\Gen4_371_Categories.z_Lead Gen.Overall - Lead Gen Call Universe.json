{"BucketId": "371", "BucketFullname": "Categories.z_Lead Gen.Overall - Lead Gen Call Universe", "BucketDescription": "", "UserEditable": false, "BucketName": "Overall - Lead Gen Call Universe", "SectionName": "z_Lead Gen", "SectionNameDisplay": "z_Lead Gen", "SectionId": 105, "RetroStartDate": "2024-10-14T00:00:00", "CategoryDisplay": "z_Lead Gen.Overall - Lead Gen Call Universe", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-11-12T11:33:38.513-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-14T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30679, "Name": "Lead Gen - POS", "Description": null, "ActionString": "(z_Lead Gen.POS - Lead Gen Call Universe)", "UserEntry": "CAT:[z_Lead Gen.POS - Lead Gen Call Universe] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30678, "Name": "Lead Gen - NEG", "Description": null, "ActionString": "(z_Lead Gen.NEG -Lead Gen Call Universe)", "UserEntry": "CAT:[z_Lead Gen.NEG -Lead Gen Call Universe] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-05T12:40:03.46", "ComponentCount": 2}