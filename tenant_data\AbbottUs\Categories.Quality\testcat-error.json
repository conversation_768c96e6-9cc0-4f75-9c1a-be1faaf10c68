{"BucketId": "515", "BucketFullname": "Categories.testsection.test1", "BucketDescription": "test category", "UserEditable": false, "BucketName": "test1-error", "SectionName": "testsection", "SectionNameDisplay": "testsection", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "testsection.test1", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T08:07:11.22-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30225, "Name": "testcomponent1", "Description": null, "ActionString": "", "UserEntry": "CAT:[Quality Automation_Calls.Shipping Method]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -30225, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "testcolumn", "TableFilterTypes": null, "FriendlyName": "testcolumn", "BaseTypeName": "String", "FormatString": null, "Operator": "between", "Operand1": "1", "Operand2": "2", "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-03T14:25:30.777", "ComponentCount": 1}