{"BucketId": "101", "BucketFullname": "Categories.Quality Automation_Calls.PRE Questions Asked", "BucketDescription": "To identify where the agent is asking PRE questions on the call.", "UserEditable": false, "BucketName": "PRE Questions Asked", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Quality Automation_Calls.PRE Questions Asked", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2023-12-14T10:13:32.383-05:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32502, "Name": "Loss of Consciousness/Seizure", "Description": null, "ActionString": "<%conci%><process><loss><less><situation>$OR$$OR$$OR$$OR$[make+sure:1.2]<receive%><prescription><code><name><beat><monitor><reader><sorry><free><app><phone><recap><support><customer><answer%><concern%><problem%><issue%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[did|has|this|the+result%|resolv%:1]$NOTNEAR:2$$NEAR:5$[black%|pass%+out:1][since|because|deal%|talk%+medical+device:2.5][experience+loss+of:1.4][resulting+any+loss|lot|lots:3.5]<favourable><today>$OR$<sausage%><fainted>$OR$<concept><cautious><conversations><process%><function%><lost><loss>$OR$$OR$$OR$$OR$$OR$$OR$$NEAR:5$$NOTNEAR:3$<install%><please><website>$OR$$OR$[lots+questions:1.5]$NOTNEAR:5$[also+conscious:1.5][because+of|this|product|strongest+issue%:1.4]<lots><treatment><happy><well><okay><hear><glad>$OR$$OR$$OR$$OR$$OR$$OR$[nothing|anything+like+that:1.5]$NEAR:5$[did|that+you|he|she+experience%:1.4][lot%|loss|lost|los%|less+function%|process%|conversations|cautious|concept|%conscious%|conferenc%|colchester|customer|gossip|clashes|frequency|constance|caution|constant%:1.5]<seashore><convulsion%><gorgeousness><%conscious%><seizure%>$OR$$OR$$OR$$OR${kind|any|experience%+symptom%|sympathy:1.5}$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "[kind|any|experience* symptom*|sympathy]:1.5 \nOR (seizure*|*conscious*|gorgeousness|convulsion*|seashore) \nOR \"lot*|loss|lost|los*|less function*|process*|conversations|cautious|concept|*conscious*|conferenc*|colchester|customer|gossip|clashes|frequency|constance|caution|constant*\":1.5 \nOR \"did|that you|he|she experience*\":1.4 \nOR \"nothing|anything like that\":1.5 NEAR:5 (glad|hear|okay|well|happy|treatment|lots) \nOR \"because of|this|product|strongest issue*\":1.4 \nOR \"also conscious\":1.5\nOR \"lots questions\":1.5 NOT NEAR:5 (website|please|install*) \nOR ((loss|lost|function*|process*|conversations|cautious|concept) NEAR:5 (fainted|sausage*)) NOT NEAR:3 (today|favourable) \nOR \"resulting any loss|lot|lots\":3.5 OR \"experience loss of\":1.4\nOR \"since|because|deal*|talk* medical device\":2.5\nOR \"black*|pass* out\":1 OR (\"did|has|this|the result*|resolv*\":1 NOT NEAR:2 ((issue*|problem*|concern*|answer*|customer|support|recap|phone|app|free|sorry|reader|monitor|beat|name|code|prescription|receive*) OR \"make sure\":1.2)) NEAR:5 (situation|less|loss|process|*conci*)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32502, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32502, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32501, "Name": "Medical Treatment", "Description": null, "ActionString": "[other+than+yourself:1.2][anyone|someone+other+than:1.4]$OR$<receive%>$NEAR:3$<suppl%><field>$OR$<medical>$NOTNEAR:1$<loss><experience>$OR$$NEAR:5$<device>[medical+question%|intervention%:2]$NEAR:5$[anyone|else|other|person|beside+herself|yourself|himself:2][you|she|he|did+require%+any:1.2][seek%|requir%|emergency|urgency|cause%|costing|receive%+medic%|attention|treat%|placements:1.8]{medical+attention|treat%|emergency%|assistance|placement%|issue%:1.8}$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "[medical attention|treat*|emergency*|assistance|placement*|issue*]:1.8 OR \"seek*|requir*|emergency|urgency|cause*|costing|receive* medic*|attention|treat*|placements\":1.8 OR \"you|she|he|did require* any\":1.2 OR \"anyone|else|other|person|beside herself|yourself|himself\":2 OR \"medical question*|intervention*\":2 NEAR:5 (device) OR (experience|loss) NEAR:5 (medical) NOT NEAR:1 (field|suppl*) OR (receive*) NEAR:3 (\"anyone|someone other than\":1.4 OR \"other than yourself\":1.2)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32501, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32501, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-15T09:42:50.033", "ComponentCount": 2}