{"BucketId": "4279", "BucketFullname": "Categories.Emotions.Positive Emotion", "BucketDescription": "Identifies when Positive language occurs on a call.", "UserEditable": false, "BucketName": "Positive Emotion", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 2257, "RetroStartDate": null, "CategoryDisplay": "Emotions.Positive Emotion", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-03-25T16:02:44.517-04:00", "Creator": "<EMAIL>", "Version": 5, "LanguageTag": "en", "EffectiveDate": "2025-03-25T16:02:46.727", "SlnInstallationID": 1, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -21951, "Name": "Excitement", "Description": "", "ActionString": "(emotions.excitement)", "UserEntry": "CAT:[Emotions.Excitement] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21950, "Name": "Happiness", "Description": "", "ActionString": "(emotions.happiness)", "UserEntry": "CAT:[Emotions.Happiness] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21949, "Name": "Satisfaction", "Description": "", "ActionString": "(emotions.satisfaction)", "UserEntry": "CAT:[Emotions.Satisfaction] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -21948, "Name": "Surprise", "Description": "", "ActionString": "(emotions.surprise)", "UserEntry": "CAT:[Emotions.Surprise] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": 1, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-03-25T16:02:46.727", "ComponentCount": 4}