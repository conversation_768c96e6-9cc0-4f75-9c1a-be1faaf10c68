{"BucketId": null, "BucketFullname": "", "BucketDescription": "Useful for removing scripted language from emotion around problems", "UserEditable": false, "BucketName": "IVR or Voicemail", "SectionName": "", "SectionNameDisplay": "", "SectionId": 352, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T21:02:13", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T21:02:13", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "IVR", "Description": null, "ActionString": "[you+may+hang+up:1.1][options|please|press+one|two|three|four|five|six|seven|eight|nine|ten:2]$OR$", "UserEntry": "(\"options|please|press one|two|three|four|five|six|seven|eight|nine|ten\":2 ) OR \"you may hang up\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Voicemail", "Description": null, "ActionString": "[return+your+call+soon+possible:2][delivery+options:0.5][hang+up+when+done:1.1][i`ll+get+back+to+you:1.4][sorry+didn`t+get+that+please+try:2][you+were+not+speaking:1.1][because+of+bad+connection:1.1][didn`t+get+message:1.5][after|wait+beep|tone:1][finish%+record%:1.5][leave+message:1]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"leave message\":1 OR \"finish* record*\":1.5 OR \"after|wait beep|tone\":1 OR \"didn't get message\":1.5 OR \"because of bad connection\" OR \"you were not speaking\" OR \"sorry didn't get that please try\":2 OR \"I'll get back to you\" OR \"hang up when done\" OR \"delivery options\" OR \"return your call soon possible\":2", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T21:02:13", "ComponentCount": 2}