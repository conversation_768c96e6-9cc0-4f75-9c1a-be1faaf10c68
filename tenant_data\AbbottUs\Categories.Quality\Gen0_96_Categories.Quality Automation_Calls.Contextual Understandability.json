{"BucketId": "96", "BucketFullname": "Categories.Quality Automation_Calls.Contextual Understandability", "BucketDescription": "To identify confusion on the call where the customer does not understand the agent.", "UserEditable": false, "BucketName": "Contextual Understandability", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-06-21T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Contextual Understandability", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 4.0, "HasPendingRetroRequest": false, "Created": "2023-12-13T05:03:49.5-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-06-21T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-09-12T00:00:00", "EndDate": "2024-09-18T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -32507, "Name": "Complicated", "Description": null, "ActionString": "[can|could+you+simplify:1.4][difficult+concept|comprehend:1.5][hard+to+grasp:1.5][it|it`s|its|that%|this|process|system|you%+appear%|extremely|kind%|pretty|really|seems|sound%|very|is|too+complicated|convoluted|confusing:2]$OR$$OR$$OR$", "UserEntry": "\"it|it's|its|that*|this|process|system|you* appear*|extremely|kind*|pretty|really|seems|sound*|very|is|too complicated|convoluted|confusing\":2 \nOR \"hard to grasp\":1.5 \nOR \"difficult concept|comprehend\":1.5\nOR \"can|could you simplify\":1.4", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32507, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32507, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-09-19T07:57:21.097", "ComponentCount": 1}