{"BucketId": null, "BucketFullname": "", "BucketDescription": "To identify where a call back is mentioned as a trigger for following the Follow Up/Call Back process.", "UserEditable": false, "BucketName": "Call Back", "SectionName": "", "SectionNameDisplay": "", "SectionId": -99, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T16:37:22", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T16:37:22", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Call Back Offered By Agent", "Description": null, "ActionString": "[dial+again|back:1.2]!-40[phone|ring+me|you+back:1]!-40[return+my+call:1.2]!-40$OR$$OR$<minute%>!-30%<couple>!-30%$OR$[try+call+back:1.2]!-30%$NEAR:2$<just>!-30%<go>!-30%<before>!-30%<if>!-30%<them>!-30%$OR$$OR$$OR$$OR$[phone+number:1.2]!-30%<now>!-30%<so>!-30%$OR$[don`t+need|have+to:1]!-30%$NOTNEAR:3$$OR$$OR$[reference|case|confirmation+number:1.4]!-30%[good|nice|great|amazing+day|week|rest|afternoon|evening:1.2]!-30%[any|every+time:1.2]!-30%[in+case:1]!-30%<further>!-30%<problm%>!-30%<redial>!-30%<cover>!-30%<week>!-30%<hope%>!-30%<send>!-30%<disconnect%>!-30%<pickup>!-30%<fedex>!-30%<libre>!-30%<freestyle>!-30%<product>!-30%<replace%>!-30%<immediate%>!-30%<appear%>!-30%<again>!-30%<service>!-30%<future>!-30%<office>!-30%<insurance>!-30%<pharmacy>!-30%<wait>!-30%<treatment>!-30%<medical>!-30%<receive>!-30%<education>!-30%<anything>!-30%<survey>!-30%<reason%>!-30%<euro%>!-30%<issue%>!-30%<question%>!-30%<concern%>!-30%<detail>!-30%<correct%>!-30%<free>!-30%<hesitate>!-30%<order>!-30%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[call|ring+back:1]!-30%$NOTNEAR:5$$NOTNEAR:2$$OR$$OR$", "UserEntry": "((\"call|ring back\":1 NOT NEAR:5 ((order|hesitate|free|correct*|detail|concern*|question*|issue*|euro*|reason*|survey|anything|education|receive|medical|treatment|wait|pharmacy|insurance|office|future|service|again|appear*|immediate*|replace*|product|freestyle|libre|fedex|pickup|disconnect*|send|hope*|week|cover|redial|problm*|further) OR \"in case\":1 OR \"any|every time\":1.2 OR \"good|nice|great|amazing day|week|rest|afternoon|evening\":1.2 OR \"reference|case|confirmation number\":1.4)) NOT NEAR:2 (\"don't need|have to\":1 NOT NEAR:3 ((so|now)) OR \"phone number\":1.2 OR (them|if|before|go|just)) OR \"try call back\":1.2 NEAR:2 (couple|minute*)){-30%} OR (\"return my call\":1.2 OR \"phone|ring me|you back\":1 OR \"dial again|back\":1.2){-40}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Call Back Opening Hours", "Description": null, "ActionString": "[call+that+number:1.1][release+the+call:0.9][going+to+release:0.8][call+around+noon:0.9][i`m+gonna+call:0.7][try+to+call+again:1][hang+up+try+again:1.7][i|i`ll+call+back:0.9][please+phone|call+redacted:1.2][give+us+a+call:1][you+will+call+back:1.2][start+a+call+back:1][terminate|disconnect+the|a+call:1.1][call+us+at+redacted:1][i`ll+have+to+call:0.9][give+us+phone+call+back:1.5][i`m+going+call+back:1.3][seven+days+week:0.9][hours+of+operation:0.8][eight+a+m+to+eight+p+m:1.2][eight+to+eight:0.6][monday+through|to+sunday:1.2][give+us+call+back:0.9][contact|call+us|you+back|again:0.9]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"contact|call us|you back|again\":0.9 OR \"give us call back\":0.9 OR \"monday through|to sunday\":1.2 OR \"eight to eight\":0.6 OR \"eight a m to eight p m\":1.2 OR \"hours of operation\":0.8 OR \"seven days week\":0.9 OR \"i'm going call back\":1.3 OR \"give us phone call back\":1.5 OR \"i'll have to call\":0.9 OR \"call us at redacted\":1 OR \"terminate|disconnect the|a call\":1.1 OR \"start a call back\":1 OR \"you will call back\":1.2 OR \"give us a call\":1 OR \"please phone|call redacted\":1.2 OR \"i|i'll call back\":0.9 OR \"hang up try again\":1.7 OR \"try to call again\":1 OR \"i'm gonna call\":0.7 OR \"call around noon\":0.9 OR \"going to release\":0.8 OR \"release the call\":0.9 OR \"call that number\":1.1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Autopass", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T16:37:22", "ComponentCount": 3}