{"BucketId": "521", "BucketFullname": "Categories.Emotions.-Dissatisfaction - Emotions Index", "BucketDescription": "tuned for ignitetek scripts", "UserEditable": false, "BucketName": "-Dissatisfaction - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.-Dissatisfaction - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:22:51.147-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30163, "Name": "Awful", "Description": null, "ActionString": "[super|extremely|incredibly|terrible%absolute%|awfully|beyond+frustrat%|nightmare:0.5][bad+news:0.5]$OR$[distraught:0.2]$OR$[it`s|this+very+sad:1.5][find+this+sad:1.5][sad+news|part|because:1][also+sad:1]$OR$$OR$$OR$$OR$", "UserEntry": "\"also sad\":1 OR \"sad news|part|because\":1 OR \"find this sad\":1.5 OR \"it's|this very sad\":1.5 OR \"distraught\" OR \"bad news\" OR \"super|extremely|incredibly|terrible*absolute*|awfully|beyond frustrat*|nightmare\"", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30162, "Name": "Disappointed", "Description": null, "ActionString": "<disillusion%><sucks><stinks><bummed><bummer><disappoint%>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(disappoint*|bummer|bummed|stinks|sucks|disillusion*)", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30161, "Name": "Worse than", "Description": null, "ActionString": "[better+than+nothing:1.5][worse+than:1]$OR$", "UserEntry": "\"worse than\":1 OR \"better than nothing\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30160, "Name": "<PERSON><PERSON>", "Description": null, "ActionString": "[what+hoping+for:1][below|meet+expectations:0.5][ended|turned+differently:0.5][thought+would+be+better:2][can`t+believe+happened:0.8][not+what+expected:1][high+hopes:0.5][made+mistake:0.9][didn`t+even|realize:0.5][i+wish+you|i|we|they:0.5][only+reget:0.5]<not><don`t>$OR$[i|my+regret:1]$NOTNEAR:1$$OR$[biggest+thing:0.5]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"biggest thing\":0.5 \nOR ((\"i|my regret\":1 NOT NEAR:1 (don't|not)) OR \"only reget\")\nOR \"i wish you|i|we|they\":0.5 \nOR  \"didn`t even|realize\":0.5 \nOR \"made mistake\":0.9 \nOR \"high hopes\" \nOR \"not what expected\":1 \nOR \"can't believe happened\" \nOR \"thought would be better\":2 \nOR \"ended|turned differently\" \nOR \"below|meet expectations\" \nOR (\"what hoping for\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30159, "Name": "Sadness", "Description": null, "ActionString": "[heart+wrenching:0.5]<unlucky><emotional><distraught><sad><devasta%><depressed><sadness><grievance><mushy><whiny><cranky>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(cranky|whiny|mushy|grievance|sadness|depressed|devasta*|sad|distraught|emotional|unlucky) OR \"heart wrenching\":0.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30158, "Name": "Embarassed", "Description": null, "ActionString": "<mortified><awkward><embarrass%>$OR$$OR$", "UserEntry": "embarrass*|awkward|mortified", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30157, "Name": "Mishandled", "Description": null, "ActionString": "[made+mess|goof|mistake:1]{they|somebody|he|she+messed|goofed|slipped+up:1}[not+treated|cared|handled:0.5]<poorly><negligent><misrepresented><miscalculated><misconducted><misapplied><fumbled><bungled><botched><abused><misjudged><misdirected><neglected><misused><mistreated><mismanaged>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "mismanaged|mistreated|misused|neglected|misdirected|misjudged|abused|botched|bungled|fumbled|misapplied|misconducted|miscalculated|misrepresented|negligent|poorly OR \"not treated|cared|handled\" OR  ([they|somebody|he|she messed|goofed|slipped up]:1) OR (\"made mess|goof|mistake\":1)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30156, "Name": "Dissatisfaction", "Description": null, "ActionString": "[isn`t|not+acceptable|thrilled|pleased:0.5][find+lacking:0.5]<not><not>$NEAR:0$[i|i`m|am+dissatisf%:0.5]$AND$<you`re><you>$OR$[not|isn`t|aren`t|won`t|doesn`t+satisf%:1]$NOTAFTER:1$$OR$$OR$$OR$", "UserEntry": "((\"not|isn't|aren't|won't|doesn't  satisf*\":1) NOT AFTER:1 (you|you're))  OR (\"i|i'm|am dissatisf*\" NOT  NEAR:0 (not)) OR \"find lacking\" OR ( \"isn't|not acceptable|thrilled|pleased\")", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30155, "Name": "Poor value", "Description": null, "ActionString": "[can`t|not+afford%:1][too+expensive|costly:1][not|isn`t+worth+it|price|cost|money:1.2][not|poor|terrible|awful+value:1][cost%+much:1.2][too+much+money:1.2]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"too much money\":1.2 OR \"cost* much\":1.2 OR \"not|poor|terrible|awful value\":1 OR \"not|isn't worth it|price|cost|money\":1.2 OR \"too expensive|costly\":1 OR \"can't|not afford*\":1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30154, "Name": "Don`t appreciate", "Description": null, "ActionString": "[don`t|not|isn`t|didn`t+appreciat%:1]", "UserEntry": "\"don't|not|isn't|didn't appreciat*\":1", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30006, "Name": "Complaining", "Description": null, "ActionString": "{tired|sick+about:1}{complain|whine|bitch|bellyache|moan|grumble|vent|fuss|lament+about|every:1}$OR$", "UserEntry": "[complain|whine|bitch|bellyache|moan|grumble|vent|fuss|lament about|every]:1 OR  [tired|sick about]:1", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30004, "Name": "Terrible", "Description": null, "ActionString": "[that|that`s|sounds|was|is+ugly|awful|aweful|sad|terribl%|ghastly|dreadful|lousy|shocking|nasty|heinous|frightful:0.5]", "UserEntry": "(\"that|that's|sounds|was|is ugly|awful|aweful|sad|terribl*|ghastly|dreadful|lousy|shocking|nasty|heinous|frightful\")", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30003, "Name": "Felt cheated", "Description": null, "ActionString": "[get%|feel%|felt|got+cheat|cheating|cheated|lied:1]<you>[don`t|not+like|want|wanna+lied|cheat|cheating|cheated:1]$NOTBEFORE:1$[cheat|cheating|cheated|lied+me|people:0.9]$OR$$OR$", "UserEntry": "\"cheat|cheating|cheated|lied me|people\":.9 OR \"don't|not like|want|wanna lied|cheat|cheating|cheated\":1 NOT BEFORE:1 you OR \"get*|feel*|felt|got cheat|cheating|cheated|lied\":1", "SpeakerList": "", "Status": true, "Weight": 4.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30002, "Name": "was disconnected", "Description": null, "ActionString": "[to+get:0.5][was|got|were+disconnected|hung:0.5]$NOTNEAR:1$", "UserEntry": "\"Was|got|were disconnected|hung\" NOT NEAR:1 \"to get\"", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30001, "Name": "broken promise", "Description": null, "ActionString": "[didn`t|not+keep|live+their+promise|word|trust:2][broke+their+promise:0.8][was|they|am+promised:1.8]$OR$$OR$", "UserEntry": "(\"was|they|am promised\":1.8)  OR (\"broke their promise\") OR (\"didn't|not keep|live their promise|word|trust\":2)", "SpeakerList": "", "Status": true, "Weight": 4.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-22T17:33:30.653", "ComponentCount": 15}