{"BucketId": "126", "BucketFullname": "Categories.Quality Automation_Calls.Coaching Materials", "BucketDescription": "To identify where the agent is providing coaching to the customer or offers to send guides or other materials to them, providing suggestions or in other ways educating customers.", "UserEditable": false, "BucketName": "Coaching Materials", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-08-31T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Coaching Materials", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 5.0, "HasPendingRetroRequest": false, "Created": "2024-01-16T11:16:27.21-05:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-08-31T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32412, "Name": "Coaching", "Description": null, "ActionString": "[clean+the+area:1.5]{clean%+moisturizing|soap|water|alcohol+air|hair|dry|before:3}[i+would+recommend|advise:1.2][we|strong%+advise%|recommend%|suggest%|advice:1.2][friendly+reminder|advice|advise:1.2][remember|remind%+before+appl%+sensor%|new:2][fragrance+free:1.2][alcohol+wipe%|whites:1.2][dry+before+appl%:1.2]{soap+water:1.2}[clean%+with|arm%+alcohol:2.2][send+user|usage|official|detailed|additional|adhesive|navigation+guide%|material%|instruction%:2.1][e-mail|mail|send+instruction%|guid%|material%:1.2][send%+education|adhesive+guidelines:3][not+working:1]=1!-60%<issue%>=1!-60%<read%>=1!-60%<incorrect>=1!-60%<invitation>=1!-60%<app>=1!-60%<application>=1!-60%<update>=1!-60%<link>=1!-60%<message>=1!-60%<error>=1!-60%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[that`s+reason:1.2]=1!-60%$NEAR:5$[transport%+not+careful:4]=1!-60%[meter+reading:1]=1!-60%[double|just+check%:1]=1!-60%$OR$[because+aggravation|minimum|requirement%|updated|outdated:1.2]=1!-60%$NOTAFTER:3$[made|make+difference+because+application:3]=1!-60%<return%>=1!-60%<told>=1!-60%<asking>=1!-60%<customer>=1!-60%<dispatch%>=1!-60%<order>=1!-60%<replace%>=1!-60%$OR$$OR$$OR$$OR$$OR$$OR$[that`s|this+why+gets|getting|able|scan|error|message%|code%|data|date:2]=1!-60%$NOTNEAR:3$$OR$$OR$$OR$$OR$[you+said:1]=1!90%<hamster>=1!90%<bristol>=1!90%<five>=1!90%<force>=1!90%<parcel>=1!90%<courier>=1!90%<request%>=1!90%<order>=1!90%<question>=1!90%<ask%>=1!90%<call%>=1!90%<rang>=1!90%<evaluat%>=1!90%<strange>=1!90%<cancel>=1!90%<was>=1!90%<not>=1!90%<no>=1!90%<department%>=1!90%<left>=1!90%<same>=1!90%<investiga%>=1!90%<conscience>=1!90%<holiday>=1!90%<replac%>=1!90%<escalat%>=1!90%<give>=1!90%<don`t>=1!90%<call%>=1!90%<check%>=1!90%<need%>=1!90%<request%>=1!90%<reach%>=1!90%<ask%>=1!90%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[+the+reason+why:1.3]=1[that|this+is+why+you+see%|receive|get%:1.6]=1$OR$$NOTNEAR:3.5$[keep|have+in+mind+that+you+have:1.7]=1[keep|have+in+mind+you+can|could:1.7]=1[finger|glucose+stick%|test%:1]=1[compare+sensor|center+reading%:2]=1$NEAR:3$[prevent|prepar%+application|site|fall%:2]=1<property>=1<properly>=1<friendly>=1<best>=1<reminder>=1$OR$$OR$$OR$$OR$$BEFORE:3$[reminder+of+proper:1.5]=1[see|notice+physical+considered|conerns:2]=1[prepar%+application+site:2]=1<guideline%>=1<prevent%>=1$OR$[fall%+off:1]=1$NEAR:5$<document%>=1<instruction%>=1<guid%>=1<e-mail>=1<send>=1$OR$$OR$$OR$$OR$<adhesi%>=1$NEAR:5$[lead%|leav%+s%+to+malfunction%:1.2]=1{reason%+sometime%|why+malfunc%:1.2}=1<scan%>=1[why+sometimes|happen%:1]=1$OR$<application>=1[could|can|possible|there%+many|lo%|several|various|number|variety+reason%:1.5]=1$NOTNEAR:2$$NEAR:3${sen%|cen%|%sp_n%|fence+%n`t|%not|stop%+work%+because|due+blood|bladder:2}=1[because|due+cont%|auto%+reading%:1.4]=1[find+lo%+them|error%|messag%:1.2]=1$NEAR:5$<i>=1{sometime%+can|possible|happen%|occur%|giv%|get%+error%|messag%|code%+%activ%|sleep%|blood%|bleed%|filament%|needle%|reason%|exerc%|middle:1.6}=1$NOTNEAR:0$[this|that%+why+need|should+send|back+%valu%:1.3]=1[don`t+know:0.5]=1<walmart>=1<already>=1<news>=1<before>=1<second>=1<closed>=1<fed>=1<priority>=1<offload%>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<no>=1<underneath>=1<schedule>=1<boston>=1<whatever>=1<get%>=1$OR$$OR$$OR$$OR$$OR${that|this+message%|error%|area|code%+means|meaning:1.3}=1$NOTNEAR:0$$NOTNEAR:3$<customer>=1[i+don`t+know:1.5]=1{reason%|why+messag%|error%|area|code%+appear%|happen%|occur%:1.3}=1$NOTAFTER:2$$NOTNEAR:2$[due+to+knock%|bend%|bent%|filament%|fiber|needle|stuck%|stick%:1.7]=1{due|because+you|of|to+%n`t|%not+appl%+proper%|correct%:1.3}=1$OR$<code%>=1<messag%>=1<error%>=1$OR$$OR$$NEAR:5$<give>=1<provide>=1<check>=1<see>=1$OR$$OR$$OR${can|may|possible|get|could+error%|area%+messag%|code%+reason%|factor%:1.5}=1$NOTNEAR:0$<probably>=1[two+hours:0.5]=1$NOTAFTER:2${did|need|have|should|must+you+wait|scan+two|redacted|ten+hours|minut%:1.4}=1$NEAR:20$[application|compatib%+guide:1.2]=1<recommendation%>=1<processes>=1<tips>=1<process>=1<useful>=1$OR$$OR$$OR$$OR$<e-mail>=1$NEAR:2$<document%>=1<instruction%>=1<guide%>=1$OR$$OR$[send+back:1.2]=1<kit>=1<box>=1<label>=1<return>=1<third>=1<party>=1<e-mail>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NEAR:3$[following+link:1.2]=1[case|if|event+happen%+again:1.4]=1[monday+friday:1.2]=1<business>=1<receive>=1<replace%>=1$OR$$OR$<use%>=1<reader>=1<sensor>=1$OR$$OR$[please+be+informed:2]=1$NEAR:5$$NOTNEAR:5$<my>=1<when>=1<have>=1<did>=1<think>=1<believe>=1$OR$$OR$$OR$$OR$$OR$[follow+steps|instruct%:1.2]=1$NOTNEAR:2.5$[would|want+suggest:1.2]=1<me>=1<i>=1<i>=1$OR$$OR$[top+website|page:1]=1[i+show|let|explain|tell+you|your+house|how:1.6]=1$NEAR:5$$NOTNEAR:4$<remove%>=1<censor%>=1<idea>=1<calling>=1<give>=1<think>=1<likely>=1<possible>=1<could>=1<ask%>=1<can>=1<may>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[the+reason+why:1.2]=1[keep|bear+in+mind:1.2]=1$OR$$NOTNEAR:2$[avoid+using|put%|consuming|dehydration|area%:1.3]=1[no%|%n`t+us%+moisturiz%:1.2]=1[good+way+clean%+skin:1.2]=1[never|%n`t+cover+sensor%:1.2]=1[before+apply%+sens%:1.2]=1<correct>=1<service>=1<receive%>=1<survey>=1<repeat>=1<question%>=1<return%>=1$OR$$OR$$OR$$OR$$OR$$OR$[allow|let+me+remind|advise+you:2]=1$NOTNEAR:3$[clean|wash+dry:1]=1[no|don`t|never|non-+lotion|powder|cream|sweat|moistur%:1.2]=1[dry|clean|wipe|wash+skin|area:1.2]=1[before+appl%+sens%:1.2]=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"before appl* sens*\":1.2\nOR \"dry|clean|wipe|wash skin|area\":1.2 \nOR \"no|don't|never|non- lotion|powder|cream|sweat|moistur*\":1.2 \nOR \"clean|wash dry\":1 \nOR \"allow|let me remind|advise you\":2 NOT NEAR:3 (return*|question*|repeat|survey|receive*|service|correct) \nOR \"before apply* sens*\":1.2 \nOR \"never|*n't cover sensor*\":1.2\nOR \"good way clean* skin\":1.2 \nOR \"no*|*n't us* moisturiz*\":1.2\nOR \"avoid using|put*|consuming|dehydration|area*\":1.3 \nOR (\"keep|bear in mind\":1.2 \nOR \"the reason why\":1.2) NOT NEAR:2 (may|can|ask*|could|possible|likely|think|give|calling|idea|censor*|remove*)                                                                                                                                                \nOR (\"i show|let|explain|tell you|your house|how\":1.6 NEAR:5 \"top website|page\":1) NOT NEAR:4 (i|I|me) \nOR \"would|want suggest\":1.2 \nOR \"follow steps|instruct*\":1.2 NOT NEAR:2.5 (believe|think|did|have|when|my) \nOR (\"please be informed\":2 NEAR:5 (sensor|reader|use*)) NOT NEAR:5 (replace*|receive|business) \nOR \"monday friday\":1.2 OR \"case|if|event happen* again\":1.4                                                                                                                                                       \nOR \"following link\":1.2 \nOR ((e-mail|party|third|return|label|box|kit) OR \"send back\":1.2) NEAR:3 (guide*|instruction*|document*) \nOR (e-mail) NEAR:2 (useful|process|tips|processes|recommendation*) \nOR \"application|compatib* guide\":1.2 \nOR [did|need|have|should|must you wait|scan two|redacted|ten hours|minut*]:1.4 NEAR:20 (\"two hours\" NOT AFTER:2 (probably))\nOR ([can|may|possible|get|could error*|area* messag*|code* reason*|factor*]:1.5 NOT NEAR:0 (see|check|provide|give))\nOR (error*|messag*|code*) NEAR:5 ([due|because you|of|to *n't|*not appl* proper*|correct*]:1.3 \nOR \"due to knock*|bend*|bent*|filament*|fiber|needle|stuck*|stick*\":1.7)\nOR ([reason*|why messag*|error*|area|code* appear*|happen*|occur*]:1.3 NOT AFTER:2 \"i don't know\":1.5) NOT NEAR:2 (customer)\nOR ([that|this message*|error*|area|code* means|meaning]:1.3 NOT NEAR:0 (get*|whatever|boston|schedule|underneath|no)) NOT NEAR:3 ((offload*|priority|fed|closed|second|before|news|already|walmart) OR \"don't know\")\nOR \"this|that* why need|should send|back *valu*\":1.3\nOR [sometime* can|possible|happen*|occur*|giv*|get* error*|messag*|code* *activ*|sleep*|blood*|bleed*|filament*|needle*|reason*|exerc*|middle]:1.6 NOT NEAR:0 (i)\nOR \"find lo* them|error*|messag*\":1.2 NEAR:5 \"because|due cont*|auto* reading*\":1.4\nOR [sen*|cen*|*sp_n*|fence *n't|*not|stop* work* because|due blood|bladder]:2\nOR (\"could|can|possible|there* many|lo*|several|various|number|variety reason*\":1.5 NOT NEAR:2 (application)) NEAR:3 (\"why sometimes|happen*\":1 OR (scan*))\nOR [reason* sometime*|why malfunc*]:1.2\nOR \"lead*|leav* s* to malfunction*\":1.2 OR (adhesi*) NEAR:5 (send|e-mail|guid*|instruction*|document*)                                                                                                                                                    \nOR \"fall* off\":1 NEAR:5 (prevent*|guideline*)\nOR \"prepar* application site\":2                                                                                          \nOR \"see|notice physical considered|conerns\":2 \nOR \"reminder of proper\":1.5                                                                                                                                                                                                                         \nOR ((reminder|best|friendly|properly|property) BEFORE:3 (\"prevent|prepar* application|site|fall*\":2))                                                                                                                                                                              \nOR (\"compare sensor|center reading*\":2 NEAR:3 \"finger|glucose stick*|test*\":1) \nOR \"keep|have in mind you can|could\":1.7 \nOR \"keep|have in mind that you have\":1.7 \nOR (\"that|this is why you see*|receive|get*\":1.6 \nOR \" the reason why\":1.3) NOT NEAR:3.5 ((ask*|reach*|request*|need*|check*|call*|don't|give|escalat*|replac*|holiday|conscience|investiga*|same|left|department*|no|not|was|cancel|strange|evaluat*|rang|call*|ask*|question|order|request*|courier|parcel|force|five|bristol|hamster) OR \"you said\":1){90%} \nOR (\"that's|this why gets|getting|able|scan|error|message*|code*|data|date\":2 NOT NEAR:3 (replace*|order|dispatch*|customer|asking|told|return*)\nOR \"made|make difference because application\":3 \nOR \"because aggravation|minimum|requirement*|updated|outdated\":1.2 NOT AFTER:3 (\"double|just check*\":1 OR \"meter reading\":1)\nOR \"transport* not careful\":4 \nOR \"that's reason\":1.2 NEAR:5 ((error|message|link|update|application|app|invitation|incorrect|read*|issue*) OR \"not working\":1)){-60%})=agent\nOR \"send* education|adhesive guidelines\":3                                                                                                                                                                                                                                                                               \nOR \"e-mail|mail|send instruction*|guid*|material*\":1.2\nOR \"send user|usage|official|detailed|additional|adhesive|navigation guide*|material*|instruction*\":2.1\nOR \"clean* with|arm* alcohol\":2.2 \nOR [soap water]:1.2\nOR \"dry before appl*\":1.2 \nOR \"alcohol wipe*|whites\":1.2 \nOR \"fragrance free\":1.2 \nOR \"remember|remind* before appl* sensor*|new\":2 \nOR \"friendly reminder|advice|advise\":1.2 \nOR \"we|strong* advise*|recommend*|suggest*|advice\":1.2 \nOR \"i would recommend|advise\":1.2\nOR [clean* moisturizing|soap|water|alcohol air|hair|dry|before]:3 \nOR \"clean the area\":1.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32412, "BucketID": 27, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32412, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32412, "BucketID": 25, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32412, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32224, "Name": "Product", "Description": null, "ActionString": "[libre|libra|leaver|lever+three:1][no+reader%|reading%|monitor%|scan%|machine%|meter%|device%:1]$NEAR:3${leave|put|keep+purse|bag|pocket+scan:2}{scan%+automatically:1.2}[not|%n`t+libre|libra|leaver|lever+two|too|to:1]{work%+only|different+app|application%|up|op:1}$NEAR:5$[compare%|comparison+libre|fibre|zebra|lever|leader|leaver+two|too|to:1.5]{price+will+same+as:0.8}<now>[new+feature%|function%:0.8]$NOTNEAR:2$[less+plastic:1][less+carton+paper:1][less+pollution:1][environmentally+friendly:1.5]{need|enough|direct%|straight|use+smartphone:0.8}[you|it+%n`t|not+need|require|come+reader%|monitor%|scan%|meter%|machine|reading%:1.5]$NEAR:3${smaller|smallest|thinner|thinnest+size|sensor%|centr%|cent%|fence%|server%|sentence%:1}[use+different+strips:1.6][sensors|censors|senses+are|for+single+use:2][use+touchscreen:1.3]<data><nfc><notification%><alarm%>$OR$$OR$$OR$[you+have|should|need+turn%+on:1.2]$NEAR:6$[has+own+reader:1.6]<issue%><software>$OR$[it+alarm%|notif%+you:1.2]$NOTNEAR:2$[main+difference+between:0.8]<issue%>[alarm+feature:1.4]$NOTNEAR:3$[difference|libra|libre+is|has+alarm%:2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"difference|libra|libre is|has alarm*\":2 \nOR \"alarm feature\":1.4 NOT NEAR:3 (issue*) \nOR \"main difference between\" \nOR \"it alarm*|notif* you\":1.2 NOT NEAR:2 (software|issue*)\nOR \"has own reader\":1.6 \nOR \"you have|should|need turn* on\":1.2 NEAR:6 (alarm*|notification*|nfc|data)\nOR \"use touchscreen\":1.3 \nOR \"sensors|censors|senses are|for single use\":2 \nOR \"use different strips\":1.6 \nOR [smaller|smallest|thinner|thinnest size|sensor*|centr*|cent*|fence*|server*|sentence*]:1 \nOR \"you|it *n't|not need|require|come reader*|monitor*|scan*|meter*|machine|reading*\":1.5 NEAR:3 [need|enough|direct*|straight|use smartphone]:0.8\nOR \"environmentally friendly\":1.5 \nOR \"less pollution\":1 \nOR \"less carton paper\":1 \nOR \"less plastic\":1 \nOR \"new feature*|function*\":0.8 NOT NEAR:2 (now) \nOR [price will same as]:0.8 \nOR \"compare*|comparison libre|fibre|zebra|lever|leader|leaver two|too|to\":1.5 \nOR ([work* only|different app|application*|up|op]:1 NEAR:5 \"not|*n't libre|libra|leaver|lever two|too|to\":1) \nOR [scan* automatically]:1.2 \nOR [leave|put|keep purse|bag|pocket scan]:2 \nOR \"no reader*|reading*|monitor*|scan*|machine*|meter*|device*\":1 NEAR:3 \"libre|libra|leaver|lever three\":1", "SpeakerList": "1", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32224, "BucketID": 27, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32224, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32224, "BucketID": 25, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32224, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32222, "Name": "Education", "Description": null, "ActionString": "[either+phone+or+reader:1.8][one+sensor+per+device:2]$AND$[can`t|won`t|unable+use+both:1.5][only+one+device:2]{box|kit|sens%|cent%|return%|product+evaluat%:1.4}{disregard|regard|ignor%+letter|e-mail+reminder|return%:2.2}<address><redacted>$OR$[i|i`ll+send%+return+kit|check|box:3]$NOTNEAR:2$[follow%|dispos%+local:1.8][local|dispos%+guide%|regulation%|legislation%:2.2][do+you:1.2]=1!80%<monitor>=1!80%<backup>=1!80%$OR$[finger+test:1.2]=1!80%$NEAR:3$[have|sure|perform|take+finger+test:1.4]=1!80%$OR$$NOTNEAR:3$[remember|care|sure+perform|do+finger%+test%:2.5]=1!80%$OR$[remember|care|sure+perform|do+finger%+test:1.2]=1[send|sens%|cent%+back:1.2]=1<box>=1<kit>=1<return>=1$OR$$OR$$OR$[free|three+of+charge:1.7]=1$NEAR:10$[how+to+return:1.3]=1[i|we+would+recommend:1.5]=1[what+i+can+suggest|advise|recommend:1.2]=1[what+you+can|also+do:1.2]=1[check|find|search|locate|go|type+website|web:1.2]=1[if|does|do|is+you|she|mother|brother|wife|husband:1.2]=1[do|if|any|don`t|of+you|chance|maybe|is+have:1.3]=1$OR$<remember>=1<sure>=1<important>=1<must>=1<way>=1<always>=1$OR$$OR$$OR$$OR$$OR$<lanc%>=1<stick>=1<check>=1<level>=1<strips>=1<prick>=1<finger>=1<sugar>=1<glucose>=1<test>=1<blood>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<or>=1[backup|another|different|alternat%+way|means|method:1.3]=1$NEAR:3.5$$AND$$NEAR:2$$NOTNEAR:4$[lead%|leav%+s%+to+malfunction%:3]=1<attention>=1<know>=1$OR$[menu+options:1.2]=1$NEAR:2$[it`s|is+important+to+know|explain:1.5]=1{reason%+sometime%|why+malfunc%:2}=1<scan%>=1[why+sometimes|happen%:1]=1$OR$<application>=1[could|can|possible|there%+many|lo%|several|various|number|variety+reason%:1.5]=1$NOTNEAR:3$$NEAR:7${sen%|cen%|%sp_n%|fence+%n`t|%not|stop%+work%+because|due+blood|bladder:2}=1[because|due+cont%|auto%+reading%:3.5]=1[find+lo%+them|error%|messag%:1.7]=1$NEAR:5$<i>=1{sometime%+can|possible|happen%|occur%|giv%|get%+error%|messag%|code%+%activ%|sleep%|blood%|bleed%|filament%|needle%|reason%|exerc%|middle:4}=1$NOTNEAR:0$[this|that%+why+need|should+send|back+%valu%:2]=1[don`t+know:0.5]=1<walmart>=1<already>=1<news>=1<before>=1<second>=1<closed>=1<fed>=1<priority>=1<offload%>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<no>=1<underneath>=1<schedule>=1<boston>=1<whatever>=1<get%>=1$OR$$OR$$OR$$OR$$OR${that|this+message%|error%|area|code%+means|meaning:2}=1$NOTNEAR:0$$NOTNEAR:3${what+usually+happen%:1.3}=1<check%>=1<know>=1<ask%>=1<want%>=1<customer>=1$OR$$OR$$OR$$OR$[i+don`t+know:1.5]=1{reason%|why+messag%|error%|area|code%+appear%|happen%|occur%:2}=1$NOTAFTER:2$$NOTNEAR:2$[due+to+knock%|bend%|bent%|filament%|fiber|needle|stuck%|stick%:1.7]=1{due|because+you|of|to+%n`t|%not+appl%+proper%|correct%:2}=1$OR$<code%>=1<messag%>=1<error%>=1$OR$$OR$$NEAR:5$<just>=1<if>=1<give>=1<provide>=1<check>=1<see>=1$OR$$OR$$OR$$OR$$OR${can|may|possible|get|could+error%|area%+messag%|code%+reason%|factor%:3}=1$NOTNEAR:0$<probably>=1[two+hours:0.5]=1$NOTAFTER:2${did|need|have|should|must+you+wait|scan+two|redacted|ten+hours|minut%:1.6}=1$NEAR:100$<urgently>=1<page>=1<restart>=1<turn>=1<resolve>=1<phone>=1<said>=1<say%>=1<mention>=1<departm%>=1<escalat%>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR${did|need|have|should|must+you+wait+two|redacted|ten+hours|minut%:1.6}=1$NOTNEAR:3$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "([did|need|have|should|must you wait two|redacted|ten hours|minut*]:1.6 NOT NEAR:3 (escalat*|departm*|mention|say*|said|phone|resolve|turn|restart|page|urgently)\nOR [did|need|have|should|must you wait|scan two|redacted|ten hours|minut*]:1.6 NEAR:100 (\"two hours\" NOT AFTER:2 (probably))\nOR ([can|may|possible|get|could error*|area* messag*|code* reason*|factor*]:3 NOT NEAR:0 (see|check|provide|give|if|just))\nOR (error*|messag*|code*) NEAR:5 ([due|because you|of|to *n't|*not appl* proper*|correct*]:2 OR \"due to knock*|bend*|bent*|filament*|fiber|needle|stuck*|stick*\":1.7)\nOR ([reason*|why messag*|error*|area|code* appear*|happen*|occur*]:2 NOT AFTER:2 \"i don't know\":1.5) NOT NEAR:2 (customer|want*|ask*|know|check*) OR [what usually happen*]:1.3\nOR ([that|this message*|error*|area|code* means|meaning]:2 NOT NEAR:0 (get*|whatever|boston|schedule|underneath|no)) NOT NEAR:3 ((offload*|priority|fed|closed|second|before|news|already|walmart) OR \"don't know\")\nOR \"this|that* why need|should send|back *valu*\":2\nOR [sometime* can|possible|happen*|occur*|giv*|get* error*|messag*|code* *activ*|sleep*|blood*|bleed*|filament*|needle*|reason*|exerc*|middle]:4 NOT NEAR:0 (i)\nOR \"find lo* them|error*|messag*\":1.7 NEAR:5 \"because|due cont*|auto* reading*\":3.5\nOR [sen*|cen*|*sp_n*|fence *n't|*not|stop* work* because|due blood|bladder]:2\nOR (\"could|can|possible|there* many|lo*|several|various|number|variety reason*\":1.5 NOT NEAR:3 (application)) NEAR:7 (\"why sometimes|happen*\":1 OR (scan*))\nOR [reason* sometime*|why malfunc*]:2 OR \"it's|is important to know|explain\":1.5 OR \"menu options\":1.2 NEAR:2 (know|attention)\nOR \"lead*|leav* s* to malfunction*\":3 \nOR (((\"backup|another|different|alternat* way|means|method\":1.3 NEAR:3.5 OR (blood|test|glucose|sugar|finger|prick|strips|level|check|stick|lanc*)) NEAR:2 (always|way|must|important|sure|remember))) NOT NEAR:4 (\"do|if|any|don't|of you|chance|maybe|is have\":1.3 OR \"if|does|do|is you|she|she|mother|brother|wife|husband\":1.2) \nOR \"check|find|search|locate|go|type website|web\":1.2 \nOR \"what you can|also do\":1.2 \nOR \"what I can suggest|advise|recommend\":1.2 \nOR \"I|we would recommend\":1.5 \nOR \"how to return\":1.3\nOR \"free|three of charge\":1.7 NEAR:10 ((return|kit|box) OR \"send|sens*|cent* back\":1.2)\nOR \"remember|care|sure perform|do finger* test\":1.2 \nOR (\"remember|care|sure perform|do finger* test*\":2.5 OR (\"have|sure|perform|take finger test\":1.4 OR \"finger test\":1.2 NEAR:3 (backup|monitor)) NOT NEAR:3 \"do you\":1.2){80%})=agent\nOR \"local|dispos* guide*|regulation*|legislation*\":2.2\nOR \"follow*|dispos* local\":1.8\nOR \"i|i'll send* return kit|check|box\":3 NOT NEAR:2 (redacted|address)\nOR [disregard|regard|ignor* letter|e-mail reminder|return*]:2.2\nOR [box|kit|sens*|cent*|return*|product evaluat*]:1.4 OR \"only one device\":2 OR \"can't|won't|unable use both\":1.5 OR \"one sensor per device\":2 \"either phone or reader\":1.8", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32222, "BucketID": 27, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32222, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32222, "BucketID": 25, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32222, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32221, "Name": "Insertion", "Description": null, "ActionString": "[libre|libra|leaver|lever+three:1][no+reader%|reading%|monitor%|scan%|machine%|meter%|device%:1]$NEAR:3${leave|put|keep+purse|bag|pocket+scan:2}{scan%+automatically:1.2}[not|%n`t+libre|libra|leaver|lever+two|too|to:1]{work%+only|different+app|application%|up|op:1}$NEAR:5$[compare%|comparison+libre|fibre|zebra|lever|leader|leaver+two|too|to:1.5]{price+will+same+as:0.8}<now>[new+feature%|function%:0.8]$NOTNEAR:2$[less+plastic:1][less+carton+paper:1][less+pollution:1][environmentally+friendly:1.5]{need|enough|direct%|straight|use+smartphone:0.8}[you|it+%n`t|not+need|require|come+reader%|monitor%|scan%|meter%|machine|reading%:1.5]$NEAR:3${smaller|smallest|thinner|thinnest+size|sensor%|centr%|cent%|fence%|server%|sentence%:1}[use+different+strips:1.6][sensors|censors|senses+are|for+single+use:2][use+touchscreen:1.3]<data><nfc><notification%><alarm%>$OR$$OR$$OR$[you+have|should|need+turn%+on:1.2]$NEAR:6$[has+own+reader:1.6]<issue%><software>$OR$[it+alarm%|notif%+you:1.2]$NOTNEAR:2$[main+difference+between:0.8]<issue%>[alarm+feature:1.4]$NOTNEAR:3$[difference|libra|libre+is|has+alarm%:2]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"difference|libra|libre is|has alarm*\":2 \nOR \"alarm feature\":1.4 NOT NEAR:3 (issue*) \nOR \"main difference between\" \nOR \"it alarm*|notif* you\":1.2 NOT NEAR:2 (software|issue*)\nOR \"has own reader\":1.6 \nOR \"you have|should|need turn* on\":1.2 NEAR:6 (alarm*|notification*|nfc|data)\nOR \"use touchscreen\":1.3 \nOR \"sensors|censors|senses are|for single use\":2 \nOR \"use different strips\":1.6 \nOR [smaller|smallest|thinner|thinnest size|sensor*|centr*|cent*|fence*|server*|sentence*]:1 \nOR \"you|it *n't|not need|require|come reader*|monitor*|scan*|meter*|machine|reading*\":1.5 NEAR:3 [need|enough|direct*|straight|use smartphone]:0.8\nOR \"environmentally friendly\":1.5 \nOR \"less pollution\":1 \nOR \"less carton paper\":1 \nOR \"less plastic\":1 \nOR \"new feature*|function*\":0.8 NOT NEAR:2 (now) \nOR [price will same as]:0.8 \nOR \"compare*|comparison libre|fibre|zebra|lever|leader|leaver two|too|to\":1.5 \nOR ([work* only|different app|application*|up|op]:1 NEAR:5 \"not|*n't libre|libra|leaver|lever two|too|to\":1) \nOR [scan* automatically]:1.2 \nOR [leave|put|keep purse|bag|pocket scan]:2 \nOR \"no reader*|reading*|monitor*|scan*|machine*|meter*|device*\":1 NEAR:3 \"libre|libra|leaver|lever three\":1", "SpeakerList": "1", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32221, "BucketID": 27, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32221, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32221, "BucketID": 25, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32221, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31890, "Name": "Autopass", "Description": null, "ActionString": "", "UserEntry": null, "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31890, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31890, "BucketID": 25, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31890, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31890, "BucketID": 14, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31724, "Name": "<PERSON><PERSON>", "Description": null, "ActionString": "<support>!-30%<online>!-30%$OR$<email>!-30%[record|summar%+interaction|call|conversation:1.4]!-30%$NEAR:2$[email+summar%:1.8]!-30%$OR$$NOTNEAR:3$", "UserEntry": "((\"email summar*\":1.8 OR \"record|summar* interaction|call|conversation\":1.4 NEAR:2 (email)) NOT NEAR:3 (online|support)){-30%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31724, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31724, "BucketID": 25, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "!=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31724, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-11-29T11:05:10.147", "ComponentCount": 6}