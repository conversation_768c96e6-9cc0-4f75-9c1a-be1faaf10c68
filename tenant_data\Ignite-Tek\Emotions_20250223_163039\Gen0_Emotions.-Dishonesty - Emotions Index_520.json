{"BucketId": "520", "BucketFullname": "Categories.Emotions.-Dishonesty - Emotions Index", "BucketDescription": "tuned for emotion.v2", "UserEditable": false, "BucketName": "-Dishonesty - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.-Dishonesty - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:18:57.38-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30169, "Name": "Evasive", "Description": null, "ActionString": "[sneak+around:0.5][jerk%+around:1.4]<or>$AND$[around+and+around:0.8][going+in+circle%:0.8]<runaround><evasiv%>$OR$$OR$$OR$$OR$$OR$", "UserEntry": "evasiv*|runaround OR \"going in circle*\" OR \"around and around\" OR  OR \"jerk* around\":1.4 OR \"sneak around\"", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30168, "Name": "Skeptical-Nonsense", "Description": null, "ActionString": "{about|stop%+nonsense:1}<dollar%><channel%>$OR$[all+this|that+nonsens%:1][this+is+nonsense:1][some|same|absolute%|complete|total+nonsens%:1]$OR$$OR$$NOTNEAR:2$[fleec%+public|me|customer%|people:1][don`t+want|need+surprises:1.2][no+surprises:1]$OR$$OR$$OR$$OR$", "UserEntry": "\"no surprises\":1 OR \"don't want|need surprises\":1.2 \nOR (\"fleec* public|me|customer*|people\":1 )\nOR ((\"some|same|absolute*|complete|total nonsens*\":1  \nOR \"this is nonsense\":1 \nOR \"all this|that nonsens*\":1) NOT NEAR:2 (channel*|dollar*))  \nOR [about|stop* nonsense]:1", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30167, "Name": "Lying-Deceit", "Description": null, "ActionString": "[not+straight+forward:1][can’t|won’t|don’t|not+trust:0.9]!90-max[screw%+over:1][bugging|bugs+me:1.2][losing+money|customer%|patience|confidence:1.2][tricked|fooled|misled:0.2][i|you+was|were|got|have:0.5][i`ve|have+been:0.5]$OR$$BEFORE:1$[you|they+tricked|fooled|misled:0.5][misled|misinformed|misguided|tricked|fooled+by|me|us|them:0.5][lie|lying|lied+to+me|us:1.4]$AND$<hoodwink%><duped><swindle%><dishonest%><deceiv%><decep%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(decep*|deceiv*|dishonest*|swindle*|duped|hoodwink*) \nOR (\"lie|lying|lied to me|us\":1.4) \n(\"misled|misinformed|misguided|tricked|fooled by|me|us|them\") OR ( \"you|they tricked|fooled|misled\" ) OR ( (\"i've|have been\" OR \"i|you was|were|got|have\") BEFORE:1 \"tricked|fooled|misled\" )\nOR \"losing money|customer*|patience|confidence\":1.2 \nOR \"bugging|bugs me\":1.2 \nOR \"screw* over\":1 \nOR ( \"can’t|won’t|don’t|not trust\":0.9{90-MAX} ) OR \"not straight forward\":1", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30166, "Name": "Hesitation", "Description": null, "ActionString": "{decision+difficult%|hard:1}[trouble|problem|difficulty|time+deciding:1.2][can`t|not|cannot+decide:1]<not><don`t>$OR$<hesitat%>$NOTAFTER:1$<why>[wait+minute:1]$BEFORE:1$[no|now|wait+wait+minute:1.2]<interested><cancel>[call+back:0.8]$OR$$OR$[never+mind:0.8]$NEAR:3$[cold+feet:0.5]<reluctant%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(reluctant*)\nOR (\"cold feet\")\nOR (\"never mind\":.8 NEAR:3 (\"call back\":.8 OR cancel|interested )) \nOR \"no|now|wait wait minute\":1.2 \nOR (\"wait minute\":1 BEFORE:1 why) \nOR hesitat* NOT AFTER:1 (don't|not) \nOR \"can't|not|cannot decide\":1 \nOR \"trouble|problem|difficulty|time deciding\":1.2\nOR [decision difficult*|hard]:1", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30165, "Name": "Lacks Transparency", "Description": null, "ActionString": "[not|no|don`t|doesn`t|isnt`|wasn`t|weren`t|more|lacks+transparen|clear|clarity|forthcoming%:1]!10-max", "UserEntry": "\"not|no|don't|doesn't|isnt'|wasn't|weren't|more|lacks transparen|clear|clarity|forthcoming*\":1{10-MAX}", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30164, "Name": "Telling me", "Description": null, "ActionString": "[under+impression:1][you`re+telling+me:1.2][you+kept|keep+saying|tellling:1][they`re|were|couldn`t|kept+tell%+me:1.5]$OR$$OR$$OR$", "UserEntry": "\"they`re|were|couldn`t|kept tell* me\":1.5 \nOR \"you kept|keep saying|tellling\":1 \nOR \"you're telling me\":1.2 \nOR \"under impression\":1", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29992, "Name": "No CallBack", "Description": null, "ActionString": "[didn`t|not+get+a|any+call:1][haven`t|not+received+call:2][did+not+call|called+back:1.1][didn`t+call+back:0.8][avoid+being+called:1][avoid%|duck%|dodge%|skip%+my+call|calls:1]$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"avoid*|duck*|dodge*|skip* my call|calls\":1) OR (\"avoid being called\":1) OR (\"didn't call back\") OR ( \"did not call|called back\") OR (\"haven't|not received call\":2) OR (\"didn't|not get a|any call\":1)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29990, "Name": "Suspicious-<PERSON><PERSON>", "Description": null, "ActionString": "[don`t|not+like|love+give|giving:0.8][very|little|kind|kinda|sort|sorta|bit|pretty|super+leery|suspicious|weary|wary:1.2][con+artist|man:0.5]<paranoid><hackable><hacks><hacking><hack><hacked><fraudster><conman><conned><fraud><theives><thief><scams><scammed><scammer%><scam><suspicious>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "suspicious|scam|scammer*|scammed|scams|thief|theives|fraud|conned|conman|fraudster|hacked|hack|hacking|hacks|hackable|paranoid\nOR \"con artist|man\"\nOR \"very|little|kind|kinda|sort|sorta|bit|pretty|super leery|suspicious|weary|wary\":1.2 \nOR (\"don't|not like|love give|giving\")", "SpeakerList": "", "Status": true, "Weight": 5.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29989, "Name": "Nervous", "Description": null, "ActionString": "[i|i`m+nervous:0.5][makes+nervous:1][nervous%+about:0.5]$OR$$OR$", "UserEntry": "\"nervous* about\" OR \"makes nervous\":1 OR \"I|i'm nervous\"", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29988, "Name": "they told me", "Description": null, "ActionString": "[they|you|he|she|guy|girl|lady|man|somoebody|someone+didn`t+tell+me|us:1][i+was|wasn`t+told:1][nobody|already|what+told+me|us:1][they|you|he|she|guy|girl|lady|man|somebody|someone+told+me|us:1]$OR$$OR$$OR$", "UserEntry": "(\"they|you|he|she|guy|girl|lady|man|somebody|someone told me|us\":1 ) \nOR (\"nobody|already|what told me|us\":1  )\nOR (\"i was|wasn't told\":1 ) \nOR (\"they|you|he|she|guy|girl|lady|man|somoebody|someone didn't tell me|us\":1 )", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29895, "Name": "Inaccurate-Misinformation", "Description": null, "ActionString": "[misinformation:0.2][give|that+inaccurate:0.5]$OR$", "UserEntry": "(\"give|that inaccurate\")\nOR (\"misinformation\")", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29894, "Name": "Missed Communication", "Description": null, "ActionString": "[i+didn`t|not+receive|get+it|your:1.1][receiv%|get+%mail|notice|letter:0.5][did|have+not:0.5]<didn`t><never>$OR$$OR$$BEFORE:1$$OR$", "UserEntry": "((never|didn't) OR \"did|have not\") BEFORE:1 (\"receiv*|get *mail|notice|letter\") OR (\"i didn't|not receive|get it|your\")", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29893, "Name": "Blocked-Unrecognized Call", "Description": null, "ActionString": "[didn`t|not|don`t+recognize%+phone|number:0.8]<number><calls><call>$OR$$OR$<blocked><block>$OR$$NEAR:1$$OR$", "UserEntry": "( (block|blocked) NEAR:1 (call|calls|number) ) OR (\"didn't|not|don't recognize* phone|number\")", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29834, "Name": "told me before", "Description": null, "ActionString": "[told|telling+me|us+before|%day:0.8]", "UserEntry": "( \"told|telling me|us before|*day\" )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-17T16:40:12.73", "ComponentCount": 14}