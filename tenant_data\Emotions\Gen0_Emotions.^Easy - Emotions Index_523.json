{"BucketId": "523", "BucketFullname": "Categories.Emotions.^Easy - Emotions Index", "BucketDescription": "tuned for quick and easy in scripts", "UserEditable": false, "BucketName": "^Easy - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-01T00:00:00", "CategoryDisplay": "Emotions.^Easy - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:25:59.623-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-01T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30147, "Name": "Easy", "Description": null, "ActionString": "[can`t|cannot+believe+how+easy|simple:2.5][thank+you:0.8]<thanks>$OR$[really|pretty|very|real|so+simple|easy:1]$NEAR:5$[easier|simpler+than+thought|expecting|expected|thinking:1.8]$OR$$OR$", "UserEntry": "\"easier|simpler than thought|expecting|expected|thinking\":1.8 OR (\"really|pretty|very|real|so simple|easy\":1 NEAR:5 (thanks OR \"thank you\":.8)) OR \"can't|cannot believe how easy|simple\":2.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30146, "Name": "Not as difficult", "Description": null, "ActionString": "[thought+would+harder|difficult:1.8][not|wasn`t|isn`t+as+difficult|hard:1]$OR$", "UserEntry": "\"not|wasn't|isn't as difficult|hard\":1 OR \"thought would harder|difficult\":1.8", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30144, "Name": "Quick and easy", "Description": null, "ActionString": "{fast|quick|quickly+and+easy|easily|simple|simply|brief|effortless:0.5}", "UserEntry": "[fast|quick|quickly and easy|easily|simple|simply|brief|effortless]:.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-17T16:52:38.18", "ComponentCount": 3}