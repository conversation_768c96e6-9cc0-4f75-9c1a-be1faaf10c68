{"BucketId": "4246", "BucketFullname": "Categories.Emotions-mike.^Positive Experience - Emotions Index", "BucketDescription": "Tuned for over-hitting appreciation and scripts/IVR", "UserEditable": false, "BucketName": "^Positive Experience - Emotions Index", "SectionName": "Emotions-mike", "SectionNameDisplay": "Emotions-mike", "SectionId": 2326, "RetroStartDate": null, "CategoryDisplay": "Emotions-mike.^Positive Experience - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T20:46:08.703-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T20:44:41", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -22192, "Name": "Grateful Agent <PERSON>ing", "Description": null, "ActionString": "[have+a+blessed:1][so|been|truly+glad|happy|blessed+you|i+help%|called|assist:1]$NOTNEAR:1$<dead>[i`m|we`re|you|you`re|he|he`s|she|she`s|so|been|truly+grateful|greatful|thankfulcontentful|fortunate|cared:1.5]$NOTBEFORE:1$$OR$", "UserEntry": "(\"i'm|we're|you|you're|he|he's|she|she's|so|been|truly grateful|greatful|thankfulcontentful|fortunate|cared\":1.5 NOT BEFORE:1 (dead))\nOR ((\"so|been|truly glad|happy|blessed you|i help*|called|assist\":1) NOT NEAR:1 (\"have a blessed\":1))", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22191, "Name": "Polite and Professional", "Description": null, "ActionString": "<weren`t><aren`t><didn`t><isn`t><wasn`t><not>$OR$$OR$$OR$$OR$$OR$[you|you`re|he|she|they|being+gracious|courteous|accommodating|polite|professional|apologetic|thoughtful|tolerance|mannered|sympathy|empathy|sympathies|respectful:0.5]$NOTNEAR:1$", "UserEntry": "(\"you|you're|he|she|they|being gracious|courteous|accommodating|polite|professional|apologetic|thoughtful|tolerance|mannered|sympathy|empathy|sympathies|respectful\") \n NOT NEAR:1 (not|wasn't|isn't|didn't|aren't|weren't)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22190, "Name": "Appreciation", "Description": null, "ActionString": "<volumes><couldn`t><wouldn`t><don`t><weren`t><can`t><wasn`t><not><didn`t>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<appreciate%><generous>$OR$$NOTAFTER:1$", "UserEntry": "(generous|appreciate*) NOT AFTER:1 (didn't|not|wasn't|can't|weren't|don't|wouldn't|couldn't|volumes)", "SpeakerList": "", "Status": true, "Weight": 0.2, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22189, "Name": "Comfort or Agreement", "Description": null, "ActionString": "[does|did|it+not:0.8]<doesn`t>$OR$[made|make|making|makes+feel+better|good|great|comfortable|relaxed|secure:2]$NOTAFTER:1$[i+feel+better|good|great|comfortable|relaxed|secure:1.8]$OR$", "UserEntry": "(\"i feel better|good|great|comfortable|relaxed|secure\":1.8)\nOR (\"made|make|making|makes feel better|good|great|comfortable|relaxed|secure\":2 NOT AFTER:1 (doesn't OR \"does|did|it not\":.8) )", "SpeakerList": "", "Status": true, "Weight": 3.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22188, "Name": "Thanks for Patience", "Description": null, "ActionString": "[thanks+calling:1]<volumes><customer%>$OR$$OR$[thank%|appreciate+patient|patience:2]$NOTNEAR:3$", "UserEntry": "(\"thank*|appreciate patient|patience\":2 NOT NEAR:3 (customer*|volumes|\"thanks calling\":1))", "SpeakerList": "", "Status": true, "Weight": 0.3, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22187, "Name": "Confident", "Description": null, "ActionString": "<lastly><not><don`t>$OR$[i|i`m|feel+confiden%:0.5]$NOTNEAR:0$$NOTNEAR:3$", "UserEntry": "( (\"i|i'm|feel confiden*\" NOT NEAR:0 (don't|not) ) NOT NEAR:3 (lastly) )", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22186, "Name": "Agreeable - includes no problem", "Description": null, "ActionString": "[totally+agree|understand%:1][not+worried:0.5][shoe+fits:0.5][no+worries|doubt|problem|complaints:0.5]$OR$$OR$<not><doesn’t><no>$OR$$OR$[makes+sense:0.5]$NOTNEAR:1$$OR$$OR$", "UserEntry": "(\"makes sense\" NOT NEAR:1.0 (no|doesn’t|not) )\nOR (\"no worries|doubt|problem|complaints\":0.5|\"shoe fits\"|\"not worried\" )\nOR (\"totally agree|understand*\":1)", "SpeakerList": "", "Status": true, "Weight": 0.1, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22185, "Name": "Taking Care", "Description": null, "ActionString": "[being+concerned|cared+about+me|my:1.1][appreciate|thank|thanks+for+taking+care:1.2]$OR$", "UserEntry": "(\"appreciate|thank|thanks for taking care\":1.2)  OR (\"being concerned|cared about me|my\" )", "SpeakerList": "", "Status": true, "Weight": 5.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22184, "Name": "Holiday Wishes", "Description": null, "ActionString": "(emotions.holiday wishes)", "UserEntry": "CAT:[Em<PERSON><PERSON>.<PERSON> Wishes]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22183, "Name": "Positive Attitude", "Description": null, "ActionString": "[positive|great|good|upbeat|sunny+attitude|disposition|mindset|approach:0.5]", "UserEntry": "\"positive|great|good|upbeat|sunny attitude|disposition|mindset|approach\"", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22182, "Name": "Double Positive", "Description": null, "ActionString": "(emotions.double emotion amplifier plus positive emotion)", "UserEntry": "CAT:[Emotions.Double Emotion Amplifier Plus Positive Emotion] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22181, "Name": "Special Events", "Description": null, "ActionString": "(emotions.special events)", "UserEntry": "CAT:[Emotions.Special Events] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22180, "Name": "not bad", "Description": null, "ActionString": "[doing+today+not+bad:2][not+too|half+bad|unimpressive|shabby|lousy:0.8][not+bad|terrible|horrible|awful+thing:0.8]$OR$$OR$", "UserEntry": "\"not bad|terrible|horrible|awful thing\" OR \"not too|half bad|unimpressive|shabby|lousy\" OR \"doing today not bad\":2", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22179, "Name": "no worry", "Description": null, "ActionString": "[don`t+worry|fret|stress:0.5][not+to+worry:0.8][no+worries|worry:0.5]$OR$$OR$", "UserEntry": "\"no worries|worry\" OR \"not to worry\" OR \"don't worry|fret|stress\"", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22178, "Name": "OMG-Positive", "Description": null, "ActionString": "(emotions.extremely positive language)[oh|ohh+my+god|lord|gosh|goodness|jesus:0.8]$NEAR:5$", "UserEntry": "\"oh|ohh my god|lord|gosh|goodness|jesus\" NEAR:5 CAT:[Emotions.Extremely Positive Language]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T20:46:10.283", "ComponentCount": 15}