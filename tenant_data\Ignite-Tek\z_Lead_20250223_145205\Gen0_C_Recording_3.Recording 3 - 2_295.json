{"BucketId": "295", "BucketFullname": "Categories.C_Recording_3.Recording 3 - 2", "BucketDescription": "if you choose not to provide the health information that is necessary to determine enrollment eligibility then you may not be able to enroll", "UserEditable": false, "BucketName": "Recording 3 - 2", "SectionName": "C_Recording_3", "SectionNameDisplay": "C_Recording_3", "SectionId": 92, "RetroStartDate": "2024-10-21T00:00:00", "CategoryDisplay": "C_Recording_3.Recording 3 - 2", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-28T17:02:25.517-04:00", "Creator": "<EMAIL>", "Version": 2, "LanguageTag": "", "EffectiveDate": "2024-10-21T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31085, "Name": "_SCRIPT", "Description": null, "ActionString": "[if+you+choose+not+to+provide+the+health+information+that+is+necessary+to+determine+enrollment+eligibility+then+you+may+not+be+able+to+enroll:7.1]", "UserEntry": "\"if you choose not to provide the health information that is necessary to determine enrollment eligibility then you may not be able to enroll\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31025, "Name": "Recording 3 - 2", "Description": null, "ActionString": "[choose|provide+health|information+necessary|determine+enrollment|eligibility+able|enroll:10]", "UserEntry": "\"choose|provide health|information necessary|determine enrollment|eligibility able|enroll\":10", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-11-04T13:42:45.11", "ComponentCount": 2}