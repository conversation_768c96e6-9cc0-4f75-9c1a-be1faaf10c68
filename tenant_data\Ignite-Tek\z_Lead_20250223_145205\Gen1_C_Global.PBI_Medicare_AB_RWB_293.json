{"BucketId": "293", "BucketFullname": "Categories.C_Global.PBI_Medicare_AB_RWB", "BucketDescription": "medicare parts A and B with the red white and blue card", "UserEditable": false, "BucketName": "PBI_Medicare_AB_RWB", "SectionName": "C_Global", "SectionNameDisplay": "C_Global", "SectionId": 69, "RetroStartDate": "2024-10-14T00:00:00", "CategoryDisplay": "C_Global.PBI_Medicare_AB_RWB", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-28T15:26:56.73-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-14T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 30, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31091, "Name": "SCRIPT", "Description": null, "ActionString": "[medicare+parts+a+and+b+do+you+also+have+your+red+white+and+blue+medicare+card:4.7][medicare+parts+a+and+b+with+the+red+white+and+blue+card:3.5]$OR$", "UserEntry": "\"medicare parts A and B with the red white and blue card\"|\"medicare parts A and B do you also have your red white and blue medicare card\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31090, "Name": "V1", "Description": null, "ActionString": "[red|white|blue+medicare|card:0.5](C_Global.PBI_Medicare_AB)$BEFORE:15$", "UserEntry": "CAT:[C_Global.PBI_Medicare_AB] BEFORE:15 \"red|white|blue medicare|card\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31089, "Name": "V2", "Description": null, "ActionString": "[red|white|blue:0.2](C_Global.PBI_Medicare_AB)$BEFORE:10$", "UserEntry": "CAT:[C_Global.PBI_Medicare_AB] BEFORE:10 (\"red|white|blue\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-10T14:37:30.07", "ComponentCount": 3}