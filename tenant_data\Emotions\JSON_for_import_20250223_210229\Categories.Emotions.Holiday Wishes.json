{"BucketId": null, "BucketFullname": "", "BucketDescription": "Capturing Holiday wishes as positive language", "UserEditable": false, "BucketName": "Holiday Wishes", "SectionName": "", "SectionNameDisplay": "", "SectionId": 352, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T21:02:13", "Creator": "null", "Version": 1, "LanguageTag": "Unknown", "EffectiveDate": "2025-02-23T21:02:13", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Holiday Greeting-Closing", "Description": null, "ActionString": "[happy|merry|wonderful|great|blessed|nice|safe|beautiful|favorite|amazing+holiday%|thanksgiving|christmas|year%|easter|fourth|july|memorial|labor|veterans:0.5]", "UserEntry": "\"happy|merry|wonderful|great|blessed|nice|safe|beautiful|favorite|amazing holiday*|thanksgiving|christmas|year*|easter|fourth|july|memorial|labor|veterans\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Happy Birthday", "Description": null, "ActionString": "[happy|belated+birthday:0.5]", "UserEntry": "\"happy|belated birthday\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T21:02:13", "ComponentCount": 2}