{"BucketId": "158", "BucketFullname": "Categories.Quality Automation_Calls.Unnatural Call Endings", "BucketDescription": "To identify call that did not have a proper closing or other type of logical ending (e.g. transfer)", "UserEditable": false, "BucketName": "Unnatural Call Endings", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Unnatural Call Endings", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2024-02-23T05:30:57.743-05:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32211, "Name": "Unnatural Call Endings", "Description": null, "ActionString": "(call drivers.ghost calls)!-60[order+line:1]!-60[transfer+you+through|now:2]!-60[stop+the+regarding|recording:1.5]!-60[don`t+know+person:2.5]!-60[can`t+help+you:2]!-60[nothing+more+say:2.5]!-60[wonderful+day:1]!-60[transfer+now:1]!-60[get+in+touch:1.5]!-60[contact+you+back:2]!-60[hang+up:1]!-60[all+the+best:1]!-60[appreciate+time:1.5]!-60[thank%|fancy+help:1.5]!-60[reschedule+call:1.5]!-60[problem|thank|tank|take|you+so|much+for|you%+time|today:2.2]!-60[bye|good|thank|you+bye:2]!-60[call+me+tomorrow|later:1.2]!-60[very+much:1.1]!-60[bear+with+me+moment|minute|second%:2.5]!-60[have|had|enjoy+lovely|good|great|nice|rest+day|afternoon|night|evening|morning|weekend:2]!-60[for+you%+time+today:2]!-60[thank%+you+for|your+time|today:2]!-60[have|enjoy|your+good|rest|lovely|wonderful+day|afternoon|night|evening|morning|weekend:2.5]!-60[important+useful+info%:2.5]!-60[other|further+questions|queries:1.5]!-60[help|assist+today:2]!-60[anything+else:1]!-60[call|ring|phone+back:1]!-60[no+worries|problem%:1.2]!-60[thank%+you:1.5]!-60[take+care|yeah:2]!-60<cheers>!-60<disconnect%>!-60<terminat%>!-60<thank%>!-60<%bye>!-60$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOT$", "UserEntry": "-((*bye|thank*|terminat*|disconnect*|cheers) OR \"take care|yeah\":2 OR \"thank* you\":1.5 OR \"no worries|problem*\":1.2 OR \"call|ring|phone back\":1 OR \"anything else\":1 OR \"help|assist today\":2 OR \"other|further questions|queries\":1.5 OR \"important useful info*\":2.5 OR \"have|enjoy|your good|rest|lovely|wonderful day|afternoon|night|evening|morning|weekend\":2.5 OR \"thank* you for|your time|today\":2 OR \"for you* time today\":2 OR \"have|had|enjoy lovely|good|great|nice|rest day|afternoon|night|evening|morning|weekend\":2 OR \"bear with me moment|minute|second*\":2.5 OR \"very much\":1.1 OR \"call me tomorrow|later\":1.2 OR \"bye|good|thank|you bye\":2 OR \"problem|thank|tank|take|you so|much for|you* time|today\":2.2 OR \"reschedule call\":1.5 OR \"thank*|fancy help\":1.5 OR \"appreciate time\":1.5 OR \"all the best\":1 OR \"hang up\":1 OR \"contact you back\":2 OR \"get in touch\":1.5 OR \"transfer now\":1 OR \"wonderful day\":1 OR \"nothing more say\":2.5 OR \"can't help you\":2 OR \"Don't know person\":2.5 OR \"stop the regarding|recording\":1.5 OR \"transfer you through|now\":2 OR \"order line\":1 OR CAT:[Call Drivers.Ghost Calls]){-60}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32211, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32211, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-15T09:55:48.647", "ComponentCount": 1}