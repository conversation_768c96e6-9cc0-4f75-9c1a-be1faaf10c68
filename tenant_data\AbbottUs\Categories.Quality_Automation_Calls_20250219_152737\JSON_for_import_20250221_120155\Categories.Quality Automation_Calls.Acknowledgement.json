{"BucketId": null, "BucketFullname": "", "BucketDescription": "To identify were the agent has acknowledge the information received or the issue of the customer in a positive way, showing that they have heard the customer.", "UserEditable": false, "BucketName": "Acknowledgement", "SectionName": "", "SectionNameDisplay": "", "SectionId": 2308, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T12:01:53", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T12:01:53", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": "2024-04-07T00:00:00", "EndDate": "2024-04-14T23:59:59", "LastNDays": 0, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Acknowledgement", "Description": null, "ActionString": "~silence:+10~!80%<%hold%>!80%$OR$[do+apologi%:1.2]!80%<cannot>!80%<%n`t>!80%<not>!80%$OR$$OR$[sorry+hear+that|this:1.6]!80%$NOTNEAR:0.5$[sorry+i+will:1.2]!80%[sorry+deal%+with:1.5]!80%[sorry+going|go%|went+through|hell:1.5]!80%<hallmark>!80%[sorry+you+have|had:1.4]!80%$NOTNEAR:2$[sorry|apologi%+let|give+me:2]!80%[sorry|apologi%+cause|causing|caused:1.5]!80%[%n`t|cannont+hear|heard:1.2]!80%<said>!80%<say%>!80%<break%>!80%<repeat%>!80%$OR$$OR$$OR$$OR$[i`m|am+sorry+sir|mister|mam|ma`am+but:1.8]!80%[we|i+apologi%+about|again|but|cause|for|have|however|if|i`m|it|it`s|it`ll|ma`am|sir|so|sorry|that|this|thought|unfortunately:1.6]!80%[i`m|am|i|we|are|we`re+really|truly|totally+sorry|apologi%:1.4]!80%$OR$$OR$$NOTNEAR:1$[no|%n`t+worry|worries:1.2]!80%[not+able:1.2]!80%$OR$<apologi%>!80%<sorry>!80%$OR$$NEAR:3$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTNEAR:3$~silence:+10~!80%<patient%>!80%<wait%>!80%<question%>!80%<%hold%>!80%$OR$$OR$$OR$$OR$[sorry+for|about|because+that|this|the|those:1.7]!80%$NOTNEAR:1.5${sir|mam|ma`am+understandable:1.5}!80%[understand+where+you%+com%+from:2.6]!80%[understand+however|perhaps:1.2]!80%[you`re|are+absolutely|completely+correct|right:1.5]!80%[wish|hope+you+didn`t|don`t:1.4]!80%[wish|hope+make|get|gets+better:2]!80%<upload>!80%<scan%>!80%<application>!80%<app>!80%$OR$$OR$$OR$[no+problem:1]!80%$NOTNEAR:2$[rest|be|stay+assured:1]!80%[count+on+me|us:1.5]!80%[okay+you+say|said|saying|told|mention%:1.2]!80%~silence:+10~!80%<patient%>!80%<wait%>!80%<question%>!80%<%hold%>!80%$OR$$OR$$OR$$OR$<hold>!80%<wait%>!80%<patient%>!80%$OR$$OR$$OR$[thank%+so|very+much:1.2]!80%[no|thank%|okay|yes+sir|ma`m|mam|madam|ma`am|mister:1.2]!80%$OR$$NOTNEAR:3$[not+a+problem:1.1]!80%[no|not|%n`t|nothing|don+worry%|worries:1.2]!80%<redact%>!80%<repeat%>!80%<back>!80%<delay>!80%<wait%>!80%$OR$$OR$$OR$$OR$[apolog%|sorry+incovenience:1.4]!80%[sincere%|wholeheart%+apolog%:1.2]!80%[my+apolog%:1.2]!80%[i+am+really+sorry:1.2]!80%[i+apologize:2]!80%$OR$$OR$$OR$$OR$$NOTNEAR:3$[just+to+confirm|reiterate:1.2]!80%[my+apolog%:1.2]!80%[i+am+really+sorry:1.2]!80%[sure|okay|right|thank%+got+it:1.6]!80%[i+apologise:1.4]!80%[thank%+for+confirming|informing|advising|that:1]!80%<doesn`t>!80%<couldn`t>!80%<can`t>!80%<don`t>!80%$OR$$OR$$OR$[thank|thanks+for+expla%|clarif%|bring%|letting:1.2]!80%[sure|okay+i+can|will+help|assist:1.2]!80%[i+get|got+it+now:1]!80%[thank|thanks+clearing|clear+up:1]!80%$OR$$OR$$OR$$NOTNEAR:0$[i+understand+now|situation%|frustration%|concern%|sir|ma`am:1.2]!80%[i+absolutely|complet%|definitely|totally|fully+understand:1.6]!80%[i+see+what+you+mean:1.2]!80%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"I see what you mean\":1.2 \nOR \"i absolutely|complet*|definitely|totally|fully understand\":1.6 \nOR \"I understand now|situation*|frustration*|concern*|sir|ma'am\":1.2 \nOR (\"thank|thanks clearing|clear up\":1 \nOR \"i get|got it now\":1 OR \"sure|okay i can|will help|assist\":1.2 OR \"thank|thanks for expla*|clarif*|bring*|letting\":1.2) NOT NEAR:0 (don't|can't|couldn't|doesn't)\nOR \"thank* for confirming|informing|advising|that\":1 \nOR \"i apologise\":1.4 OR \"sure|okay|right|thank* got it\":1.6\nOR \"I am really sorry\":1.2 \nOR \"my apolog*\":1.2 \nOR \"just to confirm|reiterate\":1.2 \nOR (\"i apologize\":2 \nOR \"I am really sorry\":1.2 \nOR \"my apolog*\":1.2 \nOR \"sincere*|wholeheart* apolog*\":1.2 \nOR \"apolog*|sorry incovenience\":1.4) NOT NEAR:3 (wait*|delay|back|repeat*|redact*) \nOR \"no|not|*n't|nothing|don worry*|worries\":1.2 \nOR \"not a problem\":1.1 OR (\"no|thank*|okay|yes sir|ma'm|mam|madam|ma'am|mister\":1.2 OR \"thank* so|very much\":1.2) NOT NEAR:3 ((patient*|wait*|hold) OR ((*hold*|question*|wait*|patient*) OR  SIL:[+10])) OR \"okay you say|said|saying|told|mention*\":1.2\nOR \"count on me|us\":1.5 \nOR \"rest|be|stay assured\":1 \nOR \"no problem\":1 NOT NEAR:2 (app|application|scan*|upload) \nOR \"wish|hope make|get|gets better\":2 \nOR \"wish|hope you didn't|don't\":1.4 \nOR \"you're|are absolutely|completely correct|right\":1.5 \nOR \"understand however|perhaps\":1.2 \nOR \"understand where you* com* from\":2.6 \nOR [sir|mam|ma'am understandable]:1.5 \nOR \"sorry for|about|because that|this|the|those\":1.7 NOT NEAR:1.5 ((*hold*|question*|wait*|patient*) OR  SIL:[+10]) OR ((sorry|apologi*) NEAR:3 (\"not able\":1.2 OR \"no|*n't worry|worries\":1.2) \nOR (\"i'm|am|i|we|are|we're really|truly|totally sorry|apologi*\":1.4 \nOR \"we|i apologi* about|again|but|cause|for|have|however|if|I'm|it|it's|it'll|ma'am|sir|so|sorry|that|this|thought|unfortunately\":1.6 \nOR \"i'm|am sorry sir|mister|mam|ma'am but\":1.8) NOT NEAR:1 ((repeat*|break*|say*|said) \nOR \"*n't|cannont hear|heard\":1.2)\nOR \"sorry|apologi* cause|causing|caused\":1.5 \nOR \"sorry|apologi* let|give me\":2 \nOR \"sorry you have|had\":1.4 NOT NEAR:2 (hallmark)\nOR \"sorry going|go*|went through|hell\":1.5\nOR \"sorry deal* with\":1.5 OR \"sorry I will\":1.2\nOR \"sorry hear that|this\":1.6 NOT NEAR:0.5 (not|*n't|cannot) \nOR \"do apologi*\":1.2) NOT NEAR:3 ((*hold*) OR SIL:[+10])){80%}", "SpeakerList": "1", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T12:01:53", "ComponentCount": 1}