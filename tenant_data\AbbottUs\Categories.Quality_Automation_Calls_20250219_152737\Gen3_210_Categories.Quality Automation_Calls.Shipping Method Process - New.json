{"BucketId": "210", "BucketFullname": "Categories.Quality Automation_Calls.Shipping Method Process - New", "BucketDescription": "Identifies calls where the agent has followed process for shipping, confirming shipping address, method, timeframe and asking if customer has backup way of checking blood sugar levels.", "UserEditable": false, "BucketName": "Shipping Method Process - New", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-03-28T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Shipping Method Process - New", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 2.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2024-05-16T09:24:30.847-04:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-03-28T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.5, "SearchComponents": [{"Id": -31996, "Name": "Address Confirmation", "Description": null, "ActionString": "(quality automation_calls.address confirmation)", "UserEntry": "CAT:[Quality Automation_Calls.Address Confirmation] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31996, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31996, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31995, "Name": "Shipping Method", "Description": null, "ActionString": "(quality automation_calls.shipping method)", "UserEntry": "CAT:[Quality Automation_Calls.Shipping Method] ", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31995, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31995, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31993, "Name": "Delivery Timeframe", "Description": null, "ActionString": "[business+day%:1.6]!-30%<day%>[it+should+arrive+within|in:1.2]$NEAR:3$[you+will+receive+replacement|package|shipment|order|deliver:1.4]<battery>[five|one%|two|three|redacted+business+day%:2]$NOTNEAR:4$<left><ago>$OR$[business+days+receiv%|deliver%|get:1.2][expect|receive|shiping|shipment|parcel|package|arrive|timeframe|next|getting|replacement|redacted|kit|address|standard|overnight+within|days|business:2.5]$OR$$NOTNEAR:2$$OR$$OR$$OR$$OR$", "UserEntry": "(\"expect|receive|shiping|shipment|parcel|package|arrive|receive|timeframe|next|getting|replacement|redacted|kit|address|standard|overnight within|days|business\":2.5 OR \"business days receiv*|deliver*|get\":1.2) NOT NEAR:2 (ago|left) OR \"five|one*|two|three|redacted business day*\":2 NOT NEAR:4 (battery) OR \"you will receive replacement|package|shipment|order|deliver\":1.4 OR \"it should arrive within|in\":1.2 NEAR:3 (day*) OR \"business day*\":1.6{-30%}", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31993, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31993, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-06-26T11:18:20.023", "ComponentCount": 3}