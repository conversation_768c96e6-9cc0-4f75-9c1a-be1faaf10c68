{"BucketId": "100", "BucketFullname": "Categories.Quality Automation_Calls.Complaint", "BucketDescription": "Identifies calls where a complaint (case) needs to be logged due to customers requesting a sensor/reader replacement, are reporting malfunction of Abbott products or are expressing concern about their health being affected by sensors/readers not being readily available.\nSpeakers - None SBB/Filters Included - Yes (Agent Group=Abbott_US_Eng_Voice)", "UserEditable": false, "BucketName": "<PERSON><PERSON><PERSON><PERSON>", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-04-07T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Complaint", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2023-12-14T04:18:03.637-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-04-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": "2023-12-10T00:00:00", "EndDate": "2023-12-15T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -32503, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Description": null, "ActionString": "<ups><carrier><courier><dhl><fedex>$OR$$OR$$OR$$OR$<sent><deliver%><send%>$OR$$OR$$NEAR:3${same+address+earlier|begin%|receiv%|deliver%|shift%|ship%:2.5}<%mail>!-40%[confirm+delivery|your|ship%+address:1.5]!-40%$NOTNEAR:3$<ups><carrier><courier><dhl><fedex>$OR$$OR$$OR$$OR$[work%+days:1]$NEAR:3$$OR$<checkout><website><order%>$OR$$OR$[%n`t|%not|unable+complete|put|fill+address|%code:2]$NEAR:15$$BEFORE:600${address+%adequate:1.3}{hold|what%+status:1.2}<deliver%><order%>$OR$$NEAR:5$<%mail>!-40%[confirm%|take|taking|concern%+delivery|your|ship%+address|urgent%:2]!-40%$NOTNEAR:3$[wrong|incorrect+order|product%|delivery:1.7][serial+number:1][sixty|ten+minute%:1.5][%n`t|%not+turn%+on:1.7][event+log|code:1][lost+data+app|application:1.4][investigation+form:1.4][no|any+news|information+dispatch:1.4]{%till|yet+not|%nt|%n`t+received:1.5}[no+longer+work%:1.1][order%+days|weeks|month+ago:1.4]<sensor%><%phone><device><application><app>$OR$$OR$$OR$$OR$[%not|%nt|%n`t+respond%|open|download|%install%|work:1.4]$NEAR:2${app|application+compatibility|problem%|issue%+with:1.5}[replacement+placed|shipped|arrange%:3][%not|%nt|%n`t+contain+return|biohazard+kit|bag:1.8][problem%|issue%+data+shar%:1.5][not|%nt|%n`t+available+country|region:1.6][follow%+problem%|issue%+with:1.5]<trial>[never+arrived:1.5]$NOTNEAR:1.6$[share|where%|connect+data+pc|computer|laptop|gone|lost|app:3]<replacement>[order+cancel%:1]$NEAR:10$[how+make|get|can+work%|function%|download:1.5][is+this|it|not+compatible:1.4][sensor+went+arit|dead:1.2][address+incomplete:2][disappear%+when|after+try%+to+connect:2][what+going+on+app|application:2.5]{app|application+currently+down:1.5}[%not|%nt|%n`t+provide|send|access+replacement|data|app:1.8][app|application+not|%nt|%n`t+available|exist:1.5][message+%device+not|%nt|%n`t+found:1.4][problem%|issue%|%not|%n`t|%nt+download%|upload%|lose+data:1.6][%not|%nt|%n`t|stop%+tak%|show%+glucose|blood%|sugar+reading%|result%:1.6][complet%|continuous|disc+fail%:1.5][lost|bad|poor|terrible|horrible+stickiness|adhesion:1.6]<qr><data><print>$OR$$OR$[%not|%nt|%n`t+stick%|hold|adher%|stay|charg%:1.4]$NOTNEAR:1$[fail%|unable+open%|download|replace|work:1][forced|have|has|had|must|need+turn|switch+off|of:1.5]{white|black|smashed|cracked|broken|flash%|blank|damage%|blanck|frozen+screen|display|page|app:1.5}[cgm+function|reading%:1.4][arrived|delivered+late:1.4]<trial><files><%mail%><note>$OR$$OR$$OR$[please+remove|change|replac%:1]$NOTNEAR:1.5$<return><happy><label>$OR$$OR$[needle%|sensor+stuck|applicator|bent|broken:1.6]$NOTNEAR:1$[unable+check+sugar|reading%|result|glucose|level%|blood:1.6]<us><serial><how>$OR$$OR$[chase|status|check|delay%|late|track+order%|replacement%|sensor%|reader:1.5]$NOTNEAR:1$[when+be|expect|order%|replacement%+deliver%|receiv%:2]<degrading><if><verification><%mail%><attached><trial><app>$OR$$OR$$OR$$OR$$OR$$OR$[request%|want%|get|need|obtain|send|ask+replacement%|another|new|reader:1.5]$NOTNEAR:2$<cache>[scan%|start|try+again:1]$NOTNEAR:2$[replace%|say%|remove|change+sensor:1.2]<consciousness><seizure><medical>$OR$$OR$[result%|data%|history|reading%+lost|disappear%|stop%:2.5]$NOTNEAR:2${faulty|defective|broken|lost|damage%|failure|failed+sensor%|reader%|device%|monitor%|machine%|case|service:1}{alarm%|signal|bluetooth+problem%|issue%|loss|%available|alert%|notification%|lack:1.2}[higher|lower+than|then+should%|showed:2][high|low|inaccurate|incorrect|erratic|critical|hypo|hyper+reading%|value%|result%:1.2]<postal>[message%|code|notification|exclamation+screen|phone|app|application|mobile|mark%:1.5]$NOTNEAR:2$<silent>[sensor|cent%|reader|patch%|strip%|test|app|application|libre|device|machine|product|freestyle|meter|it|it`s+%not|%nt|%n`t|stop%|refus%|fail%|declin%|quit+work%|scan%|charg%|correct%|read%|switch%|turn%|start%|power%|load%|show%|communicat%|effective:3]$NOTNEAR:1$[error%|display%|sensor+code%|message%|reading:1]{ahead|can|will|i|we|need+troubleshoot|%shoot%+issue%|problem%|avoid:2}<question%><stone><list><office><postbox><timeline>$OR$$OR$$OR$$OR$$OR$[fall|fell|fallen|came|fold|broke+off|down|out|away:1]$NOTNEAR:2$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"fall|fell|fallen|came|fold|broke off|down|out|away\":1 NOT NEAR:2 (timeline|postbox|office|list|stone|question*)\nOR [ahead|can|will|i|we|need troubleshoot|*shoot* issue*|problem*|avoid]:2\nOR \"error*|display*|sensor code*|message*|reading\":1\nOR \"sensor|cent*|reader|patch*|strip*|test|app|application|libre|device|machine|product|freestyle|meter|it|it's *not|*nt|*n't|stop*|refus*|fail*|declin*|quit work*|scan*|charg*|correct*|read*|switch*|turn*|start*|power*|load*|show*|communicat*|effective\":3 NOT NEAR:1 (silent)\nOR \"message*|code|notification|exclamation screen|phone|app|application|mobile|phone|mark*\":1.5 NOT NEAR:2 (postal)\nOR \"high|low|inaccurate|incorrect|erratic|critical|hypo|hyper reading*|value*|result*\":1.2\nOR \"higher|lower than|then should*|showed\":2\nOR [alarm*|signal|bluetooth problem*|issue*|loss|*available|alert*|notification*|lack]:1.2 \nOR [faulty|defective|broken|lost|damage*|failure|failed sensor*|reader*|device*|monitor*|machine*|case|service]:1\nOR \"result*|data*|history|reading* lost|disappear*|stop*\":2.5 NOT NEAR:2 (medical|seizure|consciousness)\nOR \"replace*|say*|remove|change sensor\":1.2\nOR \"scan*|start|try again\":1 NOT NEAR:2 (cache)\nOR \"request*|want*|get|need|obtain|send|ask replacement*|another|new|reader\":1.5 NOT NEAR:2 (app|trial|attached|*mail*|verification|if|degrading)\nOR \"when be|expect|order*|replacement* deliver*|receiv*\":2\nOR \"chase|status|check|delay*|late|track order*|replacement*|sensor*|reader\":1.5 NOT NEAR:1 (how|serial|us)\nOR \"unable check sugar|reading*|result|glucose|level*|blood\":1.6 \nOR \"needle*|sensor stuck|applicator|bent|broken\":1.6 NOT NEAR:1 (label|happy|return)\nOR \"please remove|change|replac*\":1 NOT NEAR:1.5 (note|*mail*|files|trial)\nOR \"arrived|delivered late\":1.4\nOR \"cgm function|reading*\":1.4\nOR [white|black|smashed|cracked|broken|flash*|blank|damage*|blanck|frozen screen|display|page|app]:1.5\nOR \"forced|have|has|had|must|need turn|switch off|of\":1.5 \nOR \"fail*|unable open*|download|replace|work\":1\nOR \"*not|*nt|*n't stick*|hold|adher*|stay|charg*\":1.4 NOT NEAR:1 (print|data|QR)\nOR \"lost|bad|poor|terrible|horrible stickiness|adhesion\":1.6\nOR \"complet*|continuous|disc fail*\":1.5\nOR \"*not|*nt|*n't|stop* tak*|show* glucose|blood*|sugar reading*|result*\":1.6 \nOR \"problem*|issue*|*not|*n't|*nt download*|upload*|lose data\":1.6 \nOR \"message *device not|*nt|*n't found\":1.4 \nOR \"app|application not|*nt|*n't available|exist\":1.5 \nOR \"*not|*nt|*n't provide|send|access replacement|data|app\":1.8 \nOR [app|application currently down]:1.5 \nOR \"what going on app|application\":2.5 \nOR \"disappear* when|after try* to connect\":2 \nOR \"address incomplete\":2 \nOR \"sensor went arit|dead\":1.2 \nOR \"is this|it|not compatible\":1.4 \nOR \"how make|get|can work*|function*|download\":1.5 \nOR \"order cancel*\":1 NEAR:10 (replacement) \nOR \"share|where*|connect data PC|computer|laptop|gone|lost|app\":3 \nOR \"never arrived\":1.5 NOT NEAR:1.6 (trial) \nOR \"follow* problem*|issue* with\":1.5  \nOR \"not|*nt|*n't available country|region\":1.6                                                                                                  \nOR \"problem*|issue* data shar*\":1.5 \nOR \"*not|*nt|*n't contain return|biohazard kit|bag\":1.8 \nOR \"replacement placed|shipped|arrange*\":3\nOR [app|application compatibility|problem*|issue* with]:1.5 \nOR \"*not|*nt|*n't respond*|open|download|*install*|work\":1.4 NEAR:2 (app|application|device|*phone|sensor*) \nOR \"order* days|weeks|month ago\":1.4 \nOR \"no longer work*\":1.1  \nOR [*till| yet not|*nt|*n't received]:1.5 \nOR \"no|any news|information dispatch\":1.4 \nOR \"investigation form\":1.4 \nOR \"lost data app|application\":1.4 \nOR \"event log|code\":1\nOR \"*n't|*not turn* on\":1.7\nOR \"sixty|ten minute*\":1.5\nOR \"serial number\":1\nOR \"wrong|incorrect order|product*|delivery\":1.7\nOR (\"confirm*|take|taking|concern* delivery|your|ship* address|urgent*\":2 NOT NEAR:3 (*mail)){-40%}\nOR (order*|deliver*) NEAR:5 [hold|what* status]:1.2\nOR [address *adequate]:1.3\nOR (\"*n't|*not|unable complete|put|fill address|*code\":2 NEAR:15 (order*|website|checkout)) BEFORE:600 (\"work* days\":1 NEAR:3 (fedex|dhl|courier|carrier|ups) OR (\"confirm delivery|your|ship* address\":1.5 NOT NEAR:3 (*mail)){-40%})\nOR [same address earlier|begin*|receiv*|deliver*|shift*|ship*]:2.5\nOR (send*|deliver*|sent) NEAR:3 (fedex|dhl|courier|carrier|ups)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32503, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32503, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-15T09:25:09.537", "ComponentCount": 1}