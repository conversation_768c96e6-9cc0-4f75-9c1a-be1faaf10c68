{"BucketId": "297", "BucketFullname": "Categories.C_Recording_3.Recording 3 - 4", "BucketDescription": "the licensed sales representative is either employed or contracted by a medicare plan and may be paid based on your enrollment in a plan", "UserEditable": false, "BucketName": "Recording 3 - 4", "SectionName": "C_Recording_3", "SectionNameDisplay": "C_Recording_3", "SectionId": 92, "RetroStartDate": "2024-10-21T00:00:00", "CategoryDisplay": "C_Recording_3.Recording 3 - 4", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-28T17:04:32.033-04:00", "Creator": "<EMAIL>", "Version": 2, "LanguageTag": "", "EffectiveDate": "2024-10-21T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31083, "Name": "_SCRIPT", "Description": null, "ActionString": "[the+licensed+sales+representative+is+either+employed+or+contracted+by+a+medicare+plan+and+may+be+paid+based+on+your+enrollment+in+a+plan:7.1]", "UserEntry": "\"the licensed sales representative is either employed or contracted by a medicare plan and may be paid based on your enrollment in a plan\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31008, "Name": "Recording 3 - 4", "Description": null, "ActionString": "[licensed|sales|representative+either|employed|contracted+medicare|plan+paid|based+enrollment|plan:10]", "UserEntry": "\"licensed|sales|representative either|employed|contracted medicare|plan paid|based enrollment|plan\":10", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-11-04T13:43:25.773", "ComponentCount": 2}