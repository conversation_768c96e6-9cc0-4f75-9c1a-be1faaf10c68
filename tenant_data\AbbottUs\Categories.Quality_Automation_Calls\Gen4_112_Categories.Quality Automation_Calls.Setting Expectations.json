{"BucketId": "112", "BucketFullname": "Categories.Quality Automation_Calls.Setting Expectations", "BucketDescription": "Sets appropriate expectations regarding our processes, response times,  transfer expectations, delivery times and escalation steps where applicable. NOTE Do not change category until Dec 9th due to improvement projects.", "UserEditable": false, "BucketName": "Setting Expectations", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": null, "CategoryDisplay": "Quality Automation_Calls.Setting Expectations", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 5.0, "HasPendingRetroRequest": false, "Created": "2023-12-26T06:05:10.83-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-19T06:19:08.643", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32454, "Name": "Setting Expectations", "Description": null, "ActionString": "{september+thirt%:2}<deliver%><providing><provide><going><gonna><will><also><getting><get><receiving><receive><send%>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[return+kit|box|envelope%:2]$AFTER:2$(quality automation_calls.troubleshooting)<check%>=1<resource%>=1<%solution%>=1$OR$$OR$[next|explain+step%:1]=1$NEAR:5$$NOTNEAR:10$[be+discontinued:2]=1[end+of+september:2]=1[return+kit|box|envelope:2]=1<%mail%>=1[five|two+ten|three+minute%:1.5]=1$NEAR:3$[we+gonna+send:1.5]=1[you+can|should+expect:1.4]=1[redacted|next|couple+business+days|hour%:1.8]=1[set%|proper|make+expectation%:1.2]=1[smart|type+phone:1.5]=1[that%|it`s+gonna|going+take+time:2]=1$NOTNEAR:5$[will|gonna|going|be|%`ll+send%|get%|receiv%+%mail%:1.6]=1[expect|receive|receiving+call+back:1.4]=1[we|they|some%|manager%|supervisor%|team|digital|department+will|gonna|going|be+get|getting+touch|back:1.2]=1<brief>=1<short>=1<quick>=1<put>=1$OR$$OR$$OR$[we|they|some%|manager%|supervisor%|i|team|digital|order%|am|i`m|department+will|gonna|going+give+back|call:1.5]=1$NOTNEAR:1$<brief>=1<short>=1<quick>=1<put>=1$OR$$OR$$OR$[we|they|some%|manager%|supervisor%|team|digital|order%|i|am|i`m|department+will|gonna|going|be+call%|contact%|ring%+you|back:2.1]=1$NOTNEAR:0$[by|at+end+call:1]=1!15%[how+can:0.8]=1[i|i`m|am|we|they|were+will|%`ll|gonna|going|are+help%|assist%+with|you|this|that|issue|problem:2]=1$NOTAFTER:1$[will|gonna|going+find|provid%+%solution%:2]=1<strip%>=1<test>=1<control>=1$OR$$OR$[%solution%+for+this|you|issue%|problem|call:1.8]=1$NOTNEAR:2$[parcel+force%:1]=1[work%|business+day|days:1.1]=1[within|in|from+hours|days:1.5]=1[carrier+be+delivering:1.7]=1[will|gonna|going|%`ll+be+delivered:1.2]=1[delivery+be|will+sent:1.2]=1{will|be|gonna|going|expect+receiv%|arriv%|come|coming+it|parcel|this|package|order|delivery:1.6}=1[it%|will|is|be+arrive|arriving|receive|receiving|come|coming+in|tomorrow:1.4]=1[expect%+order%|ship%|deliver%|parcel|package|%mail%:1.4]=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"expect* order*|ship*|deliver*|parcel|package|*mail*\":1.4 \nOR \"it*|will|is|be arrive|arriving|receive|receiving|come|coming in|tomorrow\":1.4 \nOR [will|be|gonna|going|expect receiv*|arriv*|come|coming it|parcel|this|package|order|delivery]:1.6 \nOR \"delivery be|will sent\":1.2 \nOR \"will|gonna|going|*'ll be delivered\":1.2\nOR \"carrier be delivering\":1.7\nOR \"within|in|from hours|days\":1.5 \nOR \"work*|business day|days\":1.1 \nOR \"parcel force*\":1 \nOR \"*solution* for this|you|issue*|problem|call\":1.8 NOT NEAR:2 (control|test|strip*) \nOR \"will|gonna|going find|provid* *solution*\":2\nOR \"i|i'm|am|we|they|were will|*'ll|gonna|going|are help*|assist* with|you|this|that|issue|problem\":2 NOT AFTER:1 \"how can\":0.8 \nOR \"by|at end call\":1{15%} \nOR \"we|they|some*|manager*|supervisor*|team|digital|order*|i|am|i'm|department will|gonna|going|be call*|contact*|ring* you|back\":2.1 NOT NEAR:0 (put|quick|short|brief) \nOR \"we|they|some*|manager*|supervisor*|i|team|digital|order*|am|i'm|department will|gonna|going give back|call\":1.5 NOT NEAR:1 (put|quick|short|brief)\nOR \"we|they|some*|manager*|supervisor*|team|digital|department will|gonna|going|be get|getting touch|back\":1.2 \nOR \"expect|receive|receiving call back\":1.4 \nOR \"will|gonna|going|be|*'ll send*|get*|receiv* *mail*\":1.6\nOR \"that*|it's gonna|going take time\":2 NOT NEAR:5 \"smart|type phone\":1.5\nOR \"set*|proper|make expectation*\":1.2\nOR \"redacted|next|couple business days|hour*\":1.8\nOR \"you can|should expect\":1.4\nOR \"we gonna send\":1.5\nOR \"five|two ten|three minute*\":1.5 NEAR:3 (*mail*) OR \"return kit|box|envelope\":2 OR \"end of september\":2 OR \"be discontinued\":2)=Agent\nOR (\"next|explain step*\":1 NEAR:5 (*solution*|resource*|check*))=Agent NOT NEAR:10 CAT:[Quality Automation_Calls.Troubleshooting]                                OR \"return kit|box|envelope*\":2 AFTER:2 (send*|receive|receiving|get|getting|also|will|gonna|going|provide|providing|deliver*)                                                  OR [september thirt*]:2", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32454, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32454, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32443, "Name": "Explain Next Steps", "Description": null, "ActionString": "[let|let`s+gather+information%:2]=1[special%|relevant+department%:1.3]=1[i`ll|will|am+pass|put+you+through:1.4]=1[can|will|gonna|going+pass|passing+through:1.4]=1[i`ll|will|am+connect+you+with|to:1.8]=1[transfer%+you|call|department:1.3]=1<over>=1<through>=1<transfer%>=1<pass>=1<put>=1<get>=1$OR$$OR$$OR$$OR$$OR$[correct|right+department:1.5]=1$NEAR:2$<this>=1<that>=1<it>=1$OR$$OR$[gonna+pass+you|over:1.2]=1$NOTNEAR:1$[going|gonna+to|do+is:1.8]=1[i+will+now:1]=1[i`ll+be+now:1]=1[what%+gonna|going+happen:1.7]=1[what+i|i`ll|we|can+do:1]=1{sensor%|serial+number:1.3}=1[i+gonna|going+to:1.4]=1[i`m|am+gonna|going:1.4]=1$OR$$NOTNEAR:3$[then+going+to+check:2]=1[i`ll+leave|make+notes:1]=1[confirm+delivery+address:1.7]=1[what+going|gonna+do+for+you:1.1]=1[case+no+need+send+back:1.9]=1[can|gonna|going|able|will|%`ll|i`m|am+give|giving+you+track%+number:1.8]=1[gonna|going|will|am|i`m|%`ll|i|be+send%+new|you|an|a|the+link|letter|%mail|detail%|password|reset|reserve|document%:1.8]=1[will|gonna|going|need|%`ll+check|checking|consult|ask|asking+manager%|colleague%|supervisor%|superior|department:1.7]=1[will|gonna|going|i`ll|we`ll+escalat%|report:1.4]=1[will|%`ll|going|gonna+pass+feedback|information:1.8]=1[what+we+do+here:1]=1[all|what|only+need|have|should+do|to:1.3]=1<%n`t>=1[we+need+to:1.2]=1$NOTNEAR:0$[next|now+will|let`s|please|should:0.9]=1<status>=1<system>=1<lines>=1<three>=1<redacted>=1<full>=1<version>=1<software>=1<about>=1<press>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[need|just|can+contact|call|give+us|again|back|call:1.6]=1$NOTNEAR:3$[will|need|i`ll+gather+information:1.2]=1[receive|process+refund:1.1]=1<account%>=1<office>=1<one>=1<call>=1$OR$$OR$$OR$[will|going|gonna|%`ll+document%|proceed:1.2]=1$NOTNEAR:3$[go+ahead+document|proceed|approve|help|find:1.7]=1<transfer>=1<contact>=1<call>=1<doctor>=1<information>=1$OR$$OR$$OR$$OR$[in+this|that+case+what|gonna|will|going|go|ahead|you:1.8]=1$NOTNEAR:3$<completed>=1<done>=1<finished>=1$OR$$OR$[will|am|gonna|going|start|proceed|ahead|try|need|through|complete+%shoot%:1.4]=1$NOTNEAR:0$<pay>=1<money>=1<price%>=1<address>=1$OR$$OR$$OR$[place|placing+order:1.1]=1$NOTNEAR:3$[will|gonna|going|%`ll|i`m|am+give|provide+case|reference+number%:1.8]=1[gonna|will|i`m|am|going|be|have|need|possible|wanted|like+creat%|open%|set|setting+case|account%|icon|number%|new|one:1.6]=1<back>=1<%valuat%>=1<send>=1<return>=1<post>=1$OR$$OR$$OR$$OR$[have|get|put|the+faulty|forty:1]=1$NEAR:3$[working|business+days:1]=1[gonna|going|will|am|i`m+send%+return:1.1]=1[send%|get|receive|along|which+return|prepaid+envelope|label:2.5]=1[will|able|need|going|gonna|ahead|%`ll|i`m|am+replac%+for+you:1.8]=1[dispatch%|deliver%+parcel%|force:1.3]=1{replacement+sent|send%+out:1.2}=1[send+pre-:1]=1[will|gonna|going|am|%`ll|i`m|be|able+replace|replacing+sens%|cen%|read%|libre|lever|leaver|livre|fever|that|this:1.6]=1[i`m|am|%`ll|gonna|going|will|able|be+send%|provid%+%new|replac%+read%|cen%|sens%|assessor%:2]=1[i`m|am|%`ll|gonna|going|will|able|can+send%|provid%|receiv%+replac%:1.8]=1[expect|will+be|to|this+deliver%|receive:1.6]=1[expect%+delivery|parcel|package:1.5]=1<server%>=1<fenc%>=1<cent%>=1<sens%>=1$OR$$OR$$OR$[make+sure+to|that|you|we:1]=1$NOTNEAR:5$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(\"make sure to|that|you|we\":1 NOT NEAR:5 (sens*|cent*|fenc*|server*) \nOR \"expect* delivery|parcel|package\":1.5 \nOR \"expect|will be|to|this deliver*|receive\":1.6 \nOR \"i'm|am|*'ll|gonna|going|will|able|can send*|provid*|receiv* replac*\":1.8 \nOR \"i'm|am|*'ll|gonna|going|will|able|be send*|provid* *new|replac* read*|cen*|sens*|assessor*\":2 \nOR \"will|gonna|going|am|*'ll|i'm|be|able replace|replacing sens*|cen*|read*|libre|lever|leaver|livre|fever|that|this\":1.6 \nOR \"send pre-\":1 \nOR [replacement sent|send* out]:1.2 \nOR \"dispatch*|deliver* parcel*|force\":1.3 \nOR \"will|able|need|going|gonna|ahead|*'ll|i'm|am replac* for you\":1.8 \nOR \"send*|get|receive|along|which return|prepaid envelope|label\":2.5 \nOR \"gonna|going|will|am|i'm send* return\":1.1 \nOR \"working|business days\":1 \nOR \"have|get|put|the faulty|forty\":1 NEAR:3 (post|return|send|*valuat*|back) \nOR \"gonna|will|i'm|am|going|be|have|need|possible|wanted|like creat*|open*|set|setting case|account*|icon|number*|new|one\":1.6 \nOR \"will|gonna|going|*'ll|i'm|am give|provide case|reference number*\":1.8\nOR \"place|placing order\":1.1 NOT NEAR:3 (address|price*|money|pay) \nOR \"will|am|gonna|going|start|proceed|ahead|try|need|through|complete *shoot*\":1.4 NOT NEAR:0 (finished|done|completed) \nOR \"in this|that case what|gonna|will|going|go|ahead|you\":1.8 NOT NEAR:3 (information|doctor|call|contact|transfer) \nOR \"go ahead document|proceed|approve|help|find\":1.7 \nOR \"will|going|gonna|*'ll document*|proceed\":1.2 NOT NEAR:3 (call|one|office|account*) \nOR \"receive|process refund\":1.1 OR \"will|need|I'll gather information\":1.2\nOR \"need|just|can contact|call|give us|again|back|call\":1.6 NOT NEAR:3 (press|about|software|version|full|redacted|three|lines|system|status) \nOR \"next|now will|let's|please|should\":0.9 \nOR \"we need to\":1.2 NOT NEAR:0 (*n't) \nOR \"all|what|only need|have|should do|to\":1.3 \nOR \"what we do here\":1 \nOR \"will|*'ll|going|gonna pass feedback|information\":1.8  \nOR \"will|gonna|going|i'll|we'll escalat*|report\":1.4 \nOR \"will|gonna|going|need|*'ll check|checking|consult|ask|asking manager*|colleague*|supervisor*|superior|department\":1.7 \nOR \"gonna|going|will|am|i'm|*'ll|i|be send* new|you|an|a|the link|letter|*mail|detail*|password|reset|reserve|document*\":1.8 \nOR \"can|gonna|going|able|will|*'ll|i'm|am give|giving you track* number\":1.8 \nOR \"case no need send back\":1.9 \nOR \"what going|gonna do for you\":1.1 \nOR \"confirm delivery address\":1.7\nOR \"i'll leave|make notes\":1 \nOR \"then going to check\":2\nOR (\"i'm|am gonna|going\":1.4 \nOR \"i gonna|going to\":1.4) NOT NEAR:3 [sensor*|serial number]:1.3\nOR \"what i|i'll|we|can do\":1\nOR \"what* gonna|going happen\":1.7\nOR \"i'll be now\":1\nOR \"i will now\":1\nOR \"going|gonna to|do is\":1.8\nOR \"gonna pass you|over\":1.2 NOT NEAR:1 (it|that|this)\nOR \"correct|right department\":1.5 NEAR:2 (get|put|pass|transfer*|through|over)\nOR \"transfer* you|call|department\":1.3\nOR \"i'll|will|am connect you with|to\":1.8\nOR \"can|will|gonna|going pass|passing through\":1.4\nOR \"i'll|will|am pass|put you through\":1.4\nOR \"special*|relevant department*\":1.3\nOR \"let|let's gather information*\":2)=Agent", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32443, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32443, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32442, "Name": "Set An Agenda", "Description": null, "ActionString": "{by|at|and+end+call|school+%solution%:2.5}=1!30%[gonna|going|will+go|guide+through:1.6]=1!30%[gonna|going|will|%`ll+and+%solution%:2]=1!30%$OR$$OR$", "UserEntry": "((\"gonna|going|will|*'ll  and *solution*\":2 \nOR \"gonna|going|will go|guide through\":1.6 \nOR [by|at|and end call|school *solution*]:2.5)=Agent){+30%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32442, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32442, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32063, "Name": "Hold", "Description": null, "ActionString": "(customer experience.can you hold)", "UserEntry": "CAT:[Customer Experience.Can you hold] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32063, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32063, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31922, "Name": "Answered between 5-7 Seconds", "Description": null, "ActionString": "<thank>=1<this>=1<today>=1<this>=1<calling>=1<hey>=1<help>=1<okay>=1<yes>=1<hello>=1<yeah>=1<hi>=1$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$~silence:5.3-7~!10$NOTAFTER:3$", "UserEntry": "SIL:[5.3-7]{10s} NOT AFTER:10w (hi|yeah|hello|yes|okay|help|hey|calling|this|today|this|thank)=agent", "SpeakerList": "", "Status": true, "Weight": -5.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31922, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31922, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31726, "Name": "Autopass - UCE", "Description": null, "ActionString": "(quality automation_calls.unnatural call endings)", "UserEntry": "CAT:[Quality Automation_Calls.Unnatural Call Endings] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31726, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31726, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31725, "Name": "Transfer Etiquette", "Description": null, "ActionString": "<survey%>=1!-70%[i|i`m|i`ll|let+attempt%|try|trying+connect|transfer%+you:1.5]=1!-70%[am|i%|going|gonna|will+transfer|put|connect+through|to|read%|depart%|relevant|team:3]=1!-70%[before+i%+transfer%:2]=1!-70%[called|through+wrong+line|number|department:2]=1!-70%[through|connect|over+right|correct|relevant+department|person:2]=1!-70%[patch|put|pop|get+you+through+to:1.2]=1!-70%[going|gonna|let+patch|put|pop|get+you+through:1.2]=1!-70%[provide|give|tell+number+call%:2.5]=1!-70%<policy>=1!-70%<delivery>=1!-70%<lead>=1!-70%<place>=1!-70%<back>=1!-70%<book>=1!-70%<ward>=1!-70%<reply>=1!-70%<hold>=1!-70%<replacement>=1!-70%<bill>=1!-70%<pharmacy>=1!-70%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$[connect|transfer%+correct|right|order|%other|sales|different|appropriate+relevant|agent|%partm%|place|people|rep|represent%|line|person:2.5]=1!-70%$NOTNEAR:4$$NOTNEAR:15$[try+make+transfer:1.7]=1!-70%<send>=1!-70%<issue>=1!-70%$OR$<was>=1!-70%[i|i`m|i`ll|let+attempt%|try|trying+connect|transfer%+you:1.7]=1!-70%$NOTNEAR:0$$NOTNEAR:2$[let|allow+me+connect|transfer+call|calls|her|him|over|you:1.7]=1!-70%[go|move+ahead|forward|head+connect|transfer+call|calls|her|him|over|you:1.6]=1!-70%[definitely+transfer+you:1.3]=1!-70%[patch|put|pop|get+you+through+to+right|team|department%|reads|reach|reed%:1.5]=1!-70%<address>=1!-70%<account>=1!-70%$OR$[get+you+transferred+into|to|over:1.7]=1!-70%$NOTNEAR:3$<affect%>=1!-70%<deliver%>=1!-70%<replac%>=1!-70%<number>=1!-70%$OR$$OR$$OR$[ahead|allow|before|going|gonna|lemme+connect|transfer+call|calls|her|him|over|you:2]=1!-70%$NOTNEAR:5$[we`ll+transfer+your+call:3]=1!-70%<perform%>=1!-70%<refund>=1!-70%<serial>=1!-70%<replac%>=1!-70%$OR$$OR$$OR$[i`m|will|i`ll+transferring|transfer+you|your|line:1.6]=1!-70%$NOTNEAR:3$[proceed%+transfer%:1.5]=1!-70%[when|while+i+transfer+you|call:1.5]=1!-70%[your+call+transfer%:1.7]=1!-70%[get|put+agent%|%partm%|group|representative%|specialist%|service%|team|colleag%+on+line:1.2]=1!-70%$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTNEAR:10$", "UserEntry": "((\"get|put agent*|*partm*|group|representative*|specialist*|service*|team|colleag* on line\":1.2 \nOR \"your call transfer*\":1.7\nOR \"when|while i transfer you|call\":1.5\nOR \"proceed* transfer*\":1.5\nOR \"i'm|will|i'll transferring|transfer you|your|line\":1.6 NOT NEAR:3 (replac*|serial|refund|perform*)\nOR \"we'll transfer your call\":3 \nOR \"ahead|allow|before|going|gonna|lemme connect|transfer call|calls|her|him|over|you\":2 NOT NEAR:5 (number|replac*|deliver*|affect*) \nOR \"get you transferred into|to|over\":1.7 NOT NEAR:3 (account|address)\nOR \"patch|put|pop|get you through to right|team|department*|reads|reach|reed*\":1.5 \nOR \"definitely transfer you\":1.3\nOR \"go|move ahead|forward|head connect|transfer call|calls|her|him|over|you\":1.6\nOR \"let|allow me connect|transfer call|calls|her|him|over|you\":1.7\nOR (\"i|i'm|i'll|let attempt*|try|trying connect|transfer* you\":1.7 NOT NEAR:0 (was)) NOT NEAR:2 (issue|send) \nOR \"try make transfer\":1.7\nOR (\"connect|transfer* correct|right|order|*other|sales|different|appropriate relevant|agent|*partm*|place|people|rep|represent*|line|person\":2.5 NOT NEAR:4 (pharmacy|bill|replacement|hold|reply|ward|book|back|place|lead|delivery|policy)) NOT NEAR:15 \"provide|give|tell number call*\":2.5\nOR \"going|gonna|let patch|put|pop|get you through\":1.2 \nOR \"patch|put|pop|get you through to\":1.2 \nOR \"through|connect|over right|correct|relevant department|person\":2 \nOR \"called|through wrong line|number|department\":2 \nOR \"before i* transfer*\":2\nOR \"am|i*|going|gonna|will transfer|put|connect through|to|read*|depart*|relevant|team\":3\nOR \"i|i'm|i'll|let attempt*|try|trying connect|transfer* you\":1.5) NOT NEAR:10 (survey*))=agent{-70%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31725, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31725, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31663, "Name": "Survey", "Description": null, "ActionString": "(quality automation_calls.call back exclusions)<survey%>!-85%$OR$", "UserEntry": "(survey*){-85%} OR CAT:[Quality Automation_Calls.Call Back Exclusions]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31663, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -31663, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-19T06:19:13.06", "ComponentCount": 8}