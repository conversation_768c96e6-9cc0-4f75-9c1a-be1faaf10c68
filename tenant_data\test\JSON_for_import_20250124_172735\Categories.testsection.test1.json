{"BucketId": null, "BucketFullname": "Categories.testsection.test1", "BucketDescription": "test category", "UserEditable": false, "BucketName": "test1", "SectionName": "testsection", "SectionNameDisplay": "testsection", "SectionId": 168, "RetroStartDate": "2025-01-24T17:27:34", "CategoryDisplay": "testsection.test1", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-01-24T17:27:34", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-01-24T17:27:34", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "testcomponent1", "Description": null, "ActionString": "test", "UserEntry": "test", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-01-24T17:27:34", "ComponentCount": 1}