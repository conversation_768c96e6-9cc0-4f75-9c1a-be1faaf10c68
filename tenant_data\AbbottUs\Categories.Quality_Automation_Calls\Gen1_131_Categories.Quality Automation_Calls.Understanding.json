{"BucketId": "131", "BucketFullname": "Categories.Quality Automation_Calls.Understanding", "BucketDescription": "", "UserEditable": false, "BucketName": "Understanding", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2023-10-25T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Understanding", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2024-01-23T09:30:02.61-05:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-10-25T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -32401, "Name": "Ownership", "Description": null, "ActionString": "(quality automation_calls.ownership)", "UserEntry": "CAT:[Quality Automation_Calls.Ownership] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32400, "Name": "Agent Confidence", "Description": null, "ActionString": "(quality automation_calls.agent confidence)", "UserEntry": "CAT:[Quality Automation_Calls.Agent Confidence] ", "SpeakerList": "", "Status": true, "Weight": -1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-04-24T05:38:11.923", "ComponentCount": 2}