{"BucketId": "3800", "BucketFullname": "Categories.KA Emotions.Emotions Index Happiness or Excitement", "BucketDescription": "Words and phrases that indicate customer is happy of exicted", "UserEditable": false, "BucketName": "Emotions Index Happiness or Excitement", "SectionName": "KA Emotions", "SectionNameDisplay": "KA Emotions", "SectionId": 2209, "RetroStartDate": null, "CategoryDisplay": "KA Emotions.Emotions Index Happiness or Excitement", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2022-02-14T16:40:39.547-05:00", "Creator": "<PERSON>.<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2023-02-28T09:25:36", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>.<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -25442, "Name": "Appealing", "Description": null, "ActionString": "<weren`t><can`t><won`t><aren`t><isn`t><wan`t><not>$OR$$OR$$OR$$OR$$OR$$OR$<interesting><attractive><appealing>$OR$$OR$$NOTAFTER:1$", "UserEntry": "(appealing|attractive|interesting) NOT AFTER:1 (not|wan't|isn't|aren't|won't|can't|weren't)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25441, "Name": "Great", "Description": null, "ActionString": "<isn`t><wasn`t><not>$OR$$OR$<thrilled><excited><ecstatic><greatness><great><good>$OR$$OR$$OR$$OR$$OR$$NOTAFTER:1$", "UserEntry": "(good|great|greatness|ecstatic|excited|thrilled) NOT AFTER:1 (not|wasn't|isn't)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25440, "Name": "Happy", "Description": null, "ActionString": "<couldn`t><aren`t><isn`t><won`t><weren`t><wasn`t><not>$OR$$OR$$OR$$OR$$OR$$OR$<excited><thrilled><glad><happy>$OR$$OR$$OR$$NOTAFTER:1$", "UserEntry": "(happy|glad|thrilled|excited) NOT AFTER:1 (not|wasn't|weren't|won't|isn't|aren't|couldn't)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25439, "Name": "Positive", "Description": null, "ActionString": "<weren`t><can`t><won`t><aren`t><isn`t><wan`t><not>$OR$$OR$$OR$$OR$$OR$$OR$<positive%>$NOTAFTER:1$", "UserEntry": "positive* NOT AFTER:1 (not|wan't|isn't|aren't|won't|can't|weren't)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -25438, "Name": "Really Good", "Description": null, "ActionString": "<weren`t><can`t><won`t><aren`t><isn`t><wan`t><not>$OR$$OR$$OR$$OR$$OR$$OR$[really|very|too|quite|real|always|lawyers+nice|clear|lucky|good|happy|glad:0.5]$NOTAFTER:1$", "UserEntry": "\"really|very|too|quite|real|always|lawyers nice|clear|lucky|good|happy|glad\" NOT AFTER:1 (not|wan't|isn't|aren't|won't|can't|weren't)", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2023-02-28T09:25:36", "ComponentCount": 5}