{"BucketId": null, "BucketFullname": "", "BucketDescription": "tuned for good news in script", "UserEditable": false, "BucketName": "^Customer Advocacy - Emotions Index", "SectionName": "", "SectionNameDisplay": "", "SectionId": 352, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T21:02:13", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T21:02:13", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Love Service", "Description": null, "ActionString": "<enjoying><loving><enjoy><love>$OR$$OR$$OR$<product><service>$OR$$NEAR:2$", "UserEntry": "(service|product) NEAR:2 (love|enjoy|loving|enjoying)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Use all the time", "Description": null, "ActionString": "[use+constantly:0.5][i+really+used+it:1.2][use+a+lot:1][use+every+day:1.2][use+all+time:1]$OR$$OR$$OR$$OR$", "UserEntry": "\"use all time\":1 OR \"use every day\":1.2 OR \"use a lot\":1 OR \"i really used it\":1.2 OR \"use constantly\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Fantastic Service-Product", "Description": null, "ActionString": "(emotions.extremely positive)<product><service>$OR$$NEAR:5$", "UserEntry": "(service|product) NEAR:5 (CAT:[Emotions.Extremely Positive Language])", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Appreciate-ThankYou-Not Near Closing", "Description": null, "ActionString": "(emotions.closinglanguage)<not>$BEFORE:30$(emotions.compliments)!0-50%$AND$", "UserEntry": "CAT:[Emotions.Compliments]{0-50%} Not BEFORE:30 (CAT:[Emotions.ClosingLanguage])", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "good news", "Description": null, "ActionString": "[good+news:0.5]", "UserEntry": "\"good news\"", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "great news", "Description": null, "ActionString": "[to+hear:0.5](emotions.extremely positive)$BEFORE:1$[good|great|happy|wonderful|rare|glad+to+hear:1]<news>(emotions.extremely positive)$BEFORE:1$$OR$$OR$", "UserEntry": "( (CAT:[Emotions.Extremely Positive Language]) BEFORE:1 (news) ) OR (\"good|great|happy|wonderful|rare|glad to hear\":1 ) OR ( (CAT:[Emotions.Extremely Positive Language]) BEFORE:1 (\"to hear\") )", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T21:02:13", "ComponentCount": 6}