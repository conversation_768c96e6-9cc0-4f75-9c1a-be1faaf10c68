{"BucketId": "208", "BucketFullname": "Categories.C_Disclaimer.PBI_Disclaimer_5", "BucketDescription": "your local state health insurance program also known as ship", "UserEditable": false, "BucketName": "PBI_Disclaimer_5", "SectionName": "C_Disclaimer", "SectionNameDisplay": "C_Disclaimer", "SectionId": 72, "RetroStartDate": "2024-10-07T00:00:00", "CategoryDisplay": "C_Disclaimer.PBI_Disclaimer_5", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-18T12:49:06.78-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-07T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-10-21T00:00:00", "EndDate": "2024-10-23T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -31337, "Name": "Disclaimer 6", "Description": null, "ActionString": "[also+known+as+s+h+i+p:2][also+known+as+ship|chip|shift:2]$OR$[your+local+state+health+insurance+program:2][your+local+state+health+insurance+program+also+known+as+shi%:2]$OR$$AND$", "UserEntry": "((\"your local state health insurance program also known as shi*\":2) OR (\"your local state health insurance program\":2)) AND ((\"also known as ship|chip|shift\":2 ) OR (\"also known as s h i p\":2))", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31337, "BucketID": 290, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31274, "Name": "Disclaimer 6.2", "Description": null, "ActionString": "[program+also+known+as:1.1][known+issue:1.5]<%shi%>[also+known+as:0.8]<chip%><shi%>$OR$$OR$$OR$$OR$[local|state|health+insurance:1.5][state|local|health+insurance+program:1.5]$OR$$BEFORE:2$$OR$", "UserEntry": "((\"state|local|health insurance program\":1.5 OR \"local|state|health insurance\":1.5) BEFORE:2 (((shi*|chip*)) OR (\"also known as\") OR (*shi*) OR (\"known issue\":1.5))) OR \"program also known as\"", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31274, "BucketID": 290, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31041, "Name": "1", "Description": null, "ActionString": "[local|state+health+insurance+program|plan:3]", "UserEntry": "\"local|state health insurance program|plan\":3", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31041, "BucketID": 290, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31040, "Name": "ship", "Description": null, "ActionString": "<chip%><shi%>$OR$[known+as:0.5]$BEFORE:1$", "UserEntry": "\"known as\" BEFORE:1 (shi*|chip*)", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -31040, "BucketID": 290, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": null, "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": null, "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-06T12:52:59.973", "ComponentCount": 4}