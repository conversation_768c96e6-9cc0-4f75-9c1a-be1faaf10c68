{"BucketId": "299", "BucketFullname": "Categories.C_Recording_3.Recording 3 - 6", "BucketDescription": "agreeing to this scope of appointment does not affect your current or future enrollment in a medicare plan enroll you in a medicare plan or obligate you to enroll in a medicare plan", "UserEditable": false, "BucketName": "Recording 3 - 6", "SectionName": "C_Recording_3", "SectionNameDisplay": "C_Recording_3", "SectionId": 92, "RetroStartDate": "2024-10-21T00:00:00", "CategoryDisplay": "C_Recording_3.Recording 3 - 6", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-28T17:06:11.14-04:00", "Creator": "<EMAIL>", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-10-21T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31081, "Name": "_SCRIPT", "Description": null, "ActionString": "[agreeing+to+this+scope+of+appointment+does+not+affect+your+current+or+future+enrollment+in+a+medicare+plan+enroll+you+in+a+medicare+plan+or+obligate+you+to+enroll+in+a+medicare+plan:9.8]", "UserEntry": "\"agreeing to this scope of appointment does not affect your current or future enrollment in a medicare plan enroll you in a medicare plan or obligate you to enroll in a medicare plan\"", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30901, "Name": "Recording 3 - 6", "Description": null, "ActionString": "[agreeing|scope|appointment+affect|current|future+enrollment|medicare|plan+enroll|medicare|plan+obligate|enroll+medicare|plan:10]", "UserEntry": "\"agreeing|scope|appointment affect|current|future enrollment|medicare|plan enroll|medicare|plan obligate|enroll medicare|plan\":10", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-11-04T13:53:27.403", "ComponentCount": 2}