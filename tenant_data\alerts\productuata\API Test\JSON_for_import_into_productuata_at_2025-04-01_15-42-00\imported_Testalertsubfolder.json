{"AlertID": -2147483545, "Name": "imported_Testalertsubfolder", "SearchComponents": [{"Id": null, "Name": "testalert_sc1", "Description": null, "ActionString": "<test>", "UserEntry": "test", "SpeakerList": "1", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -18836, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "Agent", "TableFilterTypes": null, "FriendlyName": null, "BaseTypeName": null, "FormatString": null, "Operator": "=", "Operand1": "<PERSON>", "Operand2": "", "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "CoolOffPeriodInSeconds": 10.0, "Threshold": 1.0, "FolderID": -32518, "Description": "For testing API import/export", "MessageType": {"MessageTypeID": 5, "Name": "Other", "Color": "#64899c", "Priority": 0, "SlnInstallationId": null}, "Message": {"Message": "<p>test alert</p>", "LastUser": "<PERSON>@callminer.com", "LastModified": "2025-03-28T14:58:00"}, "Version": 1, "Status": 1, "SlnInstallationID": null, "Creator": "<PERSON>@callminer.com", "Created": "2025-03-28T14:58:00", "AlertFullname": "API Test/API Test Subfolder.Testalertsubfolder"}