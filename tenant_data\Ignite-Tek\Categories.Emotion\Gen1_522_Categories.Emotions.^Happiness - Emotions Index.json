{"BucketId": "522", "BucketFullname": "Categories.Emotions.^Happiness - Emotions Index", "BucketDescription": "Tuned for over hitting on good in closing script phrases", "UserEditable": false, "BucketName": "^Happiness - Emotions Index", "SectionName": "Emotions", "SectionNameDisplay": "Emotions", "SectionId": 154, "RetroStartDate": "2024-11-10T00:00:00", "CategoryDisplay": "Emotions.^Happiness - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-12-03T12:24:48.327-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-11-10T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": 14, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -30153, "Name": "Thrilled", "Description": null, "ActionString": "<never><lack%><without><isn`t><wasn`t><not>$OR$$OR$$OR$$OR$$OR$<triumphantly><triumph><triumphant><radiance><radiant><energetic><energizing><energized><vibrancy><vibrant><passionately><passion><passionate><delightfully><delightful><delighted><delight><enthusiastically><enthusiasm><enthusiastic><exhilaration><exhilarated><elated><joyously><joyfulness><joyful><excitement><exciting><excited><greatest><thrill%><excited%><ecstatic%><greatness>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$NOTAFTER:1$", "UserEntry": "(greatness|ecstatic*|excited*|thrill*|Greatest|Excited|Exciting|Excitement|Joyful|Joyfulness|Joyously|Elated|Exhilarated|Exhilaration|Enthusiastic|Enthusiasm|Enthusiastically|Delight|Delighted|Delightful|Delightfully|Passionate|Passion|Passionately|Vibrant|Vibrancy|Energized|Energizing|Energetic|Radiant|Radiance|Triumphant|Triumph|Triumphantly) NOT AFTER:1 (not|wasn't|isn't|Without|Lack*|Never)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30152, "Name": "Happy", "Description": null, "ActionString": "<thanksgiving><holiday%>$OR$<couldn`t><aren`t><isn`t><won`t><weren`t><wasn`t><not>$OR$$OR$$OR$$OR$$OR$$OR$<content><glad><happy>$OR$$OR$$NOTAFTER:1$$NOTNEAR:1$", "UserEntry": "(happy|glad|content) NOT AFTER:1 (not|wasn't|weren't|won't|isn't|aren't|couldn't) NOT NEAR:1 (holiday*|thanksgiving)", "SpeakerList": "", "Status": false, "Weight": 0.5, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30151, "Name": "Pleasant_Delightful", "Description": null, "ActionString": "[short|simple:0.2]<be><have><weren`t><can`t><won`t><aren`t><isn`t><wasn`t><not>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$<delightful><pleasant><sweet>$OR$$OR$$NOTAFTER:1$$NOTNEAR:1$", "UserEntry": "(sweet|pleasant|delightful) NOT AFTER:1 (not|wasn't|isn't|aren't|won't|can't|weren't|have|be) NOT NEAR:1 (\"Short|simple\")", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30150, "Name": "Extremely Good", "Description": null, "ActionString": "<weren`t><can`t><won`t><aren`t><isn`t><wan`t><not>$OR$$OR$$OR$$OR$$OR$$OR$[too|quite|extremely|fairly|so+good:0.5]$NOTAFTER:1$", "UserEntry": "\"Too|Quite|Extremely|Fairly|so good\" NOT AFTER:1 (not|wan't|isn't|aren't|won't|can't|weren't)", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30149, "Name": "Appealing", "Description": null, "ActionString": "<weren`t><can`t><won`t><aren`t><isn`t><wan`t><not>$OR$$OR$$OR$$OR$$OR$$OR$<attractive><appealing>$OR$$NOTAFTER:1$", "UserEntry": "(appealing|attractive) NOT AFTER:1 (not|wan't|isn't|aren't|won't|can't|weren't)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30148, "Name": "Positive", "Description": null, "ActionString": "[not+worried|stressed|concerned:0.5]<weren`t><can`t><won`t><aren`t><isn`t><wan`t><not>$OR$$OR$$OR$$OR$$OR$$OR$<positive%>$NOTAFTER:1$$OR$", "UserEntry": "positive* NOT AFTER:1 (not|wan't|isn't|aren't|won't|can't|weren't) OR (\"not worried|stressed|concerned\")", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30019, "Name": "Interesting_minusScript", "Description": null, "ActionString": "<weren`t>!60-max<can`t>!60-max<won`t>!60-max<aren`t>!60-max<isn`t>!60-max<wan`t>!60-max<not>!60-max$OR$$OR$$OR$$OR$$OR$$OR$[very|really|too|quite|extremely|fairly|so+interesting:0.5]!60-max$NOTAFTER:1$", "UserEntry": "((\"Very|Really|Too|Quite|Extremely|Fairly|so interesting\") NOT AFTER:1 (not|wan't|isn't|aren't|won't|can't|weren't)){60-MAX}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30018, "Name": "GreatGood-minusClosing", "Description": null, "ActionString": "[not|wasn`t|isn`t|ok|okay|have|very|really+great:0.5]!0-90%<great>!0-90%$NOTNEAR:1$", "UserEntry": "((great) NOT NEAR:1 (\"not|wasn't|isn't|ok|okay|have|very|really great\")){0%-90%}", "SpeakerList": "", "Status": true, "Weight": 0.5, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30017, "Name": "Very happy", "Description": null, "ActionString": "<weren`t><can`t><won`t><aren`t><isn`t><wasn`t><not>$OR$$OR$$OR$$OR$$OR$$OR$[very|really|too|quite|extremely|fairly+nice|clear|lucky|happy|glad:0.5]$NOTAFTER:1$", "UserEntry": "\"Very|Really|Too|Quite|Extremely|Fairly nice|clear|lucky|happy|glad\" NOT AFTER:1 (not|wasn't|isn't|aren't|won't|can't|weren't)", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -30016, "Name": "Can`t wait", "Description": null, "ActionString": "[i+can`t|just+wait+till|for:1.1]", "UserEntry": "(\"i can't|just wait till|for\")", "SpeakerList": "", "Status": true, "Weight": 2.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29941, "Name": "Terms of endearment-female", "Description": null, "ActionString": "<babydoll><sunshine><sugar><buttercup><cupcake><pumpkin><darling><honey><sweetheart><sweetie><dearest>$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "(dearest|sweetie|sweetheart|honey|darling|pumpkin|cupcake|buttercup|sugar|sunshine|babydoll )", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29940, "Name": "Endearment", "Description": null, "ActionString": "[you`re|are+angel:0.5]<spell><name>$OR$<sunshine><angel><darling><honey>$OR$$OR$$OR$$NOTNEAR:2$$OR$", "UserEntry": "( (<PERSON>|Darling|Angel|Sunshine) NOT NEAR:2 (name|spell) ) OR (\"you're|are angel\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -29825, "Name": "Extremely Positive Language", "Description": null, "ActionString": "[totally+awesome:0.5][love+it:0.5][wonderful|great+idea:0.5][you+rock:0.5](emotions.extremely positive language)(emotions.extremely positive language)$BEFORE:1$$OR$$OR$$OR$$OR$", "UserEntry": "CAT:[Emotions.Extremely Positive Language]  BEFORE:1 CAT:[Emotions.Extremely Positive Language] OR (\"you rock\") OR (\"Wonderful|great idea\") OR (\"Love it\") OR (\"Totally awesome\")", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-22T17:17:05.717", "ComponentCount": 13}