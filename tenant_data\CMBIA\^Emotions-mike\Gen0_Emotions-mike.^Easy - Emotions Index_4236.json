{"BucketId": "4236", "BucketFullname": "Categories.Emotions-mike.^Easy - Emotions Index", "BucketDescription": "tuned for quick and easy in scripts", "UserEditable": false, "BucketName": "^Easy - Emotions Index", "SectionName": "Emotions-mike", "SectionNameDisplay": "Emotions-mike", "SectionId": 2326, "RetroStartDate": null, "CategoryDisplay": "Emotions-mike.^Easy - Emotions Index", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T20:45:36.697-05:00", "Creator": "<PERSON>@callminer.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T20:44:41", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<PERSON>@callminer.com", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": -22276, "Name": "Easy", "Description": null, "ActionString": "[can`t|cannot+believe+how+easy|simple:2.5][thank+you:0.8]<thanks>$OR$[really|pretty|very|real|so+simple|easy:1]$NEAR:5$[easier|simpler+than+thought|expecting|expected|thinking:1.8]$OR$$OR$", "UserEntry": "\"easier|simpler than thought|expecting|expected|thinking\":1.8 OR (\"really|pretty|very|real|so simple|easy\":1 NEAR:5 (thanks OR \"thank you\":.8)) OR \"can't|cannot believe how easy|simple\":2.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22275, "Name": "Not as difficult", "Description": null, "ActionString": "[thought+would+harder|difficult:1.8][not|wasn`t|isn`t+as+difficult|hard:1]$OR$", "UserEntry": "\"not|wasn't|isn't as difficult|hard\":1 OR \"thought would harder|difficult\":1.8", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -22274, "Name": "Quick and easy", "Description": null, "ActionString": "{fast|quick|quickly+and+easy|easily|simple|simply|brief|effortless:0.5}", "UserEntry": "[fast|quick|quickly and easy|easily|simple|simply|brief|effortless]:.5", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": true, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T20:45:38.02", "ComponentCount": 3}