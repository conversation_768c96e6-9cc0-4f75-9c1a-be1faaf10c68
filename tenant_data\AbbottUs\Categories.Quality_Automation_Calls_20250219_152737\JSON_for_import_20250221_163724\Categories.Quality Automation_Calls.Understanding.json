{"BucketId": null, "BucketFullname": "", "BucketDescription": "", "UserEditable": false, "BucketName": "Understanding", "SectionName": "", "SectionNameDisplay": "", "SectionId": -99, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 1.0, "HasPendingRetroRequest": false, "Created": "2025-02-21T16:37:22", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-21T16:37:22", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": 0, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "Ownership", "Description": null, "ActionString": "(quality automation_calls.ownership)", "UserEntry": "CAT:[Quality Automation_Calls.Ownership] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "Agent Confidence", "Description": null, "ActionString": "(quality automation_calls.agent confidence)", "UserEntry": "CAT:[Quality Automation_Calls.Agent Confidence] ", "SpeakerList": "", "Status": true, "Weight": -1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-21T16:37:22", "ComponentCount": 2}