FEAPI_Helpers.py - Core Helper Library for CallMiner FEAPI Integration
===========================================================================

OVERVIEW:
---------
FEAPI_Helpers.py is the core helper library that provides a comprehensive interface for interacting with the CallMiner FEAPI (Front End API). This module serves as the foundation for all other scripts in the project, providing authentication, data retrieval, manipulation, and management capabilities for CallMiner categories, scores, alerts, and other entities.

MAIN CLASS:
-----------
FEAPI_Helper - The primary class that encapsulates all API interaction functionality.

KEY FEATURES:
-------------

1. AUTHENTICATION & TOKEN MANAGEMENT:
   - JWT token-based authentication with CallMiner's security API
   - Automatic token refresh and persistence to file
   - Secure credential management using environment variables
   - Support for multiple tenant configurations

2. API COMMUNICATION:
   - Standardized HTTP methods (GET, POST, PUT) with consistent error handling
   - Automatic token updates from API responses
   - Configurable retry logic and timeout handling
   - JSON payload processing and validation

3. DATA RETRIEVAL & MANAGEMENT:
   - Categories: Full CRUD operations, hierarchy management, dependency tracking
   - Scores: Creation, updating, renaming, and folder organization
   - Alerts: Retrieval and management of alert configurations
   - Folders: Organization and structure management
   - Sections: Category section management

4. EXCEL INTEGRATION:
   - Export data to Excel with multiple worksheets
   - Custom formatting and styling
   - Dependency analysis and visualization
   - Filter and search result exports

5. LOGGING & ERROR HANDLING:
   - Comprehensive logging with multiple handlers (console, file, error, warning)
   - Structured error reporting and debugging information
   - Performance monitoring and API call tracking

CORE METHODS:
-------------

Authentication Methods:
- _get_token_API10(): Obtains JWT token from security API
- UpdateTokenFromResponse(): Updates token from API response headers
- write_token_to_file(): Persists token to file for reuse

API Communication Methods:
- ApiGetJson(): Standardized GET requests with token management
- ApiPostJson(): Standardized POST requests with token management  
- ApiPutJson(): Standardized PUT requests with token management

Category Management:
- API_GetAllCategories(): Retrieves all categories from the system
- API_GetCategory(): Gets category ID by full name
- API_GetCategoryJSON(): Retrieves detailed category data
- API_RenameCategory(): Renames an existing category
- API_UpdateCategory(): Updates category configuration
- API_CreateCategory(): Creates new categories

Score Management:
- API_GetAllScores(): Retrieves all scores with folder information
- API_GetScore(): Gets score ID by name
- API_GetScoreJSON(): Retrieves detailed score data
- API_RenameScore(): Renames an existing score
- API_UpdateScore(): Updates score configuration
- API_CreateScore(): Creates new scores

Alert Management:
- API_GetAllAlertsJSON(): Retrieves all alert configurations
- GetAlertName(): Gets alert name by ID

Data Processing:
- GetFilterDependencyTables(): Analyzes dependencies between entities
- ParseFilterJson(): Processes filter configurations
- show_all_attributes_and_values(): Debugging utility for object inspection

Excel Export:
- ExportToExcel(): Creates formatted Excel files with multiple worksheets
- Various formatting and styling methods for professional output

CONFIGURATION:
--------------
The class requires a configuration object with the following properties:
- API: CallMiner API endpoint (e.g., "feapi")
- Tenant: Tenant identifier for multi-tenant environments
- username: API username (typically from environment variable)
- password: API password (typically from environment variable)
- security_api: Security API endpoint for authentication
- LogRoot: Directory for log file storage

DEPENDENCIES:
-------------
- requests: HTTP client for API communication
- pandas: Data manipulation and analysis
- openpyxl: Excel file creation and formatting
- json: JSON data processing
- logging: Comprehensive logging framework
- os, sys: System and environment interaction
- datetime: Timestamp generation

USAGE PATTERNS:
---------------

1. Basic Initialization:
   helper = FEAPI_Helper("helper_name", logger, config)

2. Category Operations:
   catid = helper.API_GetCategory("Categories.MyCategory")
   helper.API_RenameCategory("Categories.OldName", "NewName")

3. Score Operations:
   scoreid = helper.API_GetScore("MyScore")
   helper.API_RenameScore("OldScore", "NewScore")

4. Data Export:
   helper.ExportToExcel(data, "output.xlsx")

ERROR HANDLING:
---------------
The module implements comprehensive error handling:
- API errors are logged with detailed status codes and responses
- Network timeouts and connection issues are handled gracefully
- Invalid data formats trigger appropriate exceptions
- Missing dependencies are detected and reported

SECURITY CONSIDERATIONS:
------------------------
- Credentials are never stored in code or configuration files
- JWT tokens are securely managed and automatically refreshed
- API calls use proper authentication headers
- Sensitive data is logged at appropriate levels only

PERFORMANCE FEATURES:
---------------------
- Efficient data caching to minimize API calls
- Batch operations where supported by the API
- Optimized pandas operations for large datasets
- Memory-efficient processing of large result sets

EXTENSIBILITY:
--------------
The class is designed for easy extension:
- New API endpoints can be added following existing patterns
- Additional data types can be integrated using the same framework
- Custom export formats can be implemented using the base methods
- New authentication methods can be plugged in as needed

This module serves as the backbone for all CallMiner FEAPI operations in the project, providing a robust, secure, and efficient interface for managing CallMiner configurations and data.
