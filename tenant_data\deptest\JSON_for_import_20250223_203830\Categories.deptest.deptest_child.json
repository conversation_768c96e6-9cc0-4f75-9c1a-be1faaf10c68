{"BucketId": null, "BucketFullname": "", "BucketDescription": "test how dependencies are stored", "UserEditable": false, "BucketName": "deptest_child", "SectionName": "", "SectionNameDisplay": "", "SectionId": 2325, "RetroStartDate": null, "CategoryDisplay": "", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2025-02-23T20:38:27", "Creator": "null", "Version": 1, "LanguageTag": "", "EffectiveDate": "2025-02-23T20:38:27", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "null", "StartDate": null, "EndDate": null, "LastNDays": null, "TimeFrame": "Yesterday", "Threshold": 1.0, "SearchComponents": [{"Id": null, "Name": "catEQL deptest3", "Description": null, "ActionString": "(deptest.cat_doesnt_exist)(deptest.deptest3a)$AND$", "UserEntry": "CAT:[deptest.deptest3a] AND CAT:[deptest.cat_doesnt_exist]", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "catFilter deptest2a", "Description": null, "ActionString": "<deptest2a><test>$AND$", "UserEntry": "test deptest2a", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "catFilter <PERSON>", "Description": null, "ActionString": "<test>", "UserEntry": "test", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": null, "Name": "EQLFilter Empathy", "Description": null, "ActionString": "(behaviors.empathy)", "UserEntry": "CAT:[Behaviors.Empathy] ", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2025-02-23T20:38:27", "ComponentCount": 4}