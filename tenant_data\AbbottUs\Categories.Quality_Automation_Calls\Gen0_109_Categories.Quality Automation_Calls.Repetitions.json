{"BucketId": "109", "BucketFullname": "Categories.Quality Automation_Calls.Repetitions", "BucketDescription": "To identify where the agent is asking for something to be repeated at least one more time.", "UserEditable": false, "BucketName": "Repetitions", "SectionName": "Quality Automation_Calls", "SectionNameDisplay": "Quality Automation_Calls", "SectionId": 107, "RetroStartDate": "2024-08-30T00:00:00", "CategoryDisplay": "Quality Automation_Calls.Repetitions", "VisibilityMode": "All", "Groups": [], "HitMinWeight": 1.0, "HitMaxWeight": 2.0, "HasPendingRetroRequest": false, "Created": "2023-12-22T11:03:26.18-05:00", "Creator": "srdjan.<PERSON><PERSON><PERSON>@foundever.com", "Version": 1, "LanguageTag": "", "EffectiveDate": "2024-08-30T00:00:00", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": "2024-08-01T00:00:00", "EndDate": "2024-11-27T23:59:59", "LastNDays": null, "TimeFrame": null, "Threshold": 1.0, "SearchComponents": [{"Id": -32463, "Name": "Agent - Asking for Repetition", "Description": null, "ActionString": "<number><back>$OR$[can|could|would|will|please+you+repeat+again|first|it|last|once|part|question|that|what|your:1.5]$NOTBEFORE:1.5$[i+will:1]<pay><back><oursel%><this>$OR$$OR$$OR$$OR$<repeat%>[for|to+me:0.4][can|could|would+you+please:1.2][be+so+kind:1]$OR$$OR$$NEAR:1$$NOTNEAR:1$[would|can+you+explain+to+me:1.5][would+you+be+able+explain:1.5][can|could|would|will|please+you+repeat:1.4][appreciate|keep|keeps|kept|mind+repeat%:1.2][ask|asking+repeat|repeating:1.2]<back><i>$OR$[apolog%|can|could|sorry|would|will|able+repeat+that:1.5]$NOTNEAR:0.5$[i+didn`t|not|couldn`t+catch|hear:1.4]$AND$[what+did+you+say:1.2]<notification><error><message%>$OR$$OR$[what+was+that:1.2]$NOTNEAR:3$[come+again:1.2][can|could|would+you+say+that+again:1.4]$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"can|could|would you say that again\":1.4 \nOR \"come again\":1.2 \nOR \"what was that\":1.2 NOT NEAR:3 (message*|error|notification)\nOR \"what did you say\":1.2 \nOR \"I didn't|not|couldn't catch|hear\":1.4\n\"apolog*|can|could|sorry|would|will|able repeat that\":1.5 NOT NEAR:0.5 (i|back) \nOR \"ask|asking repeat|repeating\":1.2 \nOR \"appreciate|keep|keeps|kept|mind repeat*\":1.2 \nOR \"can|could|would|will|please you repeat\":1.4 \nOR \"would you be able explain\":1.5 \nOR \"would|can you explain to me\":1.5 \nOR ((\"be so kind\":1 OR \"can|could|would you please\":1.2 OR \"for|to me\":0.4) NEAR:1 (repeat*)) NOT NEAR:1 ((this|oursel*|back|pay) OR \"I will\":1)\nOR (\"can|could|would|will|please you repeat again|first|it|last|once|part|question|that|what|your\":1.5) NOT BEFORE:1.5 (back|number)", "SpeakerList": "1", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32463, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32463, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -32462, "Name": "Customer Repeating", "Description": null, "ActionString": "[need+to+say+it+again:1.3][you+are+not+listening:1.2][do+i+need+to+repeat:1.2][are+you+listening:1.2][+already+said|told|provided:1.2][i+will+repeat:1.2]<correct%>[if+i+got+it:1]$OR$[let+me+repeat:1.2]$NOTNEAR:2$$OR$$OR$$OR$$OR$$OR$$OR$", "UserEntry": "\"let me repeat\":1.2 NOT NEAR:2 (\"if I got it\":1 OR (correct*))\nOR \"i will repeat\":1.2 \nOR \" already said|told|provided\":1.2 \nOR \"are you listening\":1.2 \nOR \"do I need to repeat\":1.2 \nOR \"you are not listening\":1.2 OR \"need to say it again\":1.3", "SpeakerList": "2", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [{"SearchComponentID": -32462, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice_Azure", "Operand2": null, "Display": null, "SlnInstallationID": null}, {"SearchComponentID": -32462, "BucketID": null, "SectionID": null, "ScoreID": null, "AlertID": null, "ColumnName": "AgentGroup", "TableFilterTypes": null, "FriendlyName": "Agent Group", "BaseTypeName": "String", "FormatString": null, "Operator": "=", "Operand1": "Abbott_US_Eng_Voice", "Operand2": null, "Display": null, "SlnInstallationID": null}], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-11-28T07:31:32.58", "ComponentCount": 2}