{"BucketId": "236", "BucketFullname": "Categories.C_Global.PBI_Medicare_AB", "BucketDescription": "medicare parts A and B", "UserEditable": false, "BucketName": "PBI_Medicare_AB", "SectionName": "C_Global", "SectionNameDisplay": "C_Global", "SectionId": 69, "RetroStartDate": null, "CategoryDisplay": "C_Global.PBI_Medicare_AB", "VisibilityMode": "All", "Groups": [], "HitMinWeight": null, "HitMaxWeight": null, "HasPendingRetroRequest": false, "Created": "2024-10-22T16:27:08.59-04:00", "Creator": "<EMAIL>", "Version": 2, "LanguageTag": "", "EffectiveDate": "2024-11-01T19:11:34.2", "SlnInstallationID": null, "SemanticType": "EQL", "Username": "<EMAIL>", "StartDate": null, "EndDate": null, "LastNDays": 7, "TimeFrame": "", "Threshold": 1.0, "SearchComponents": [{"Id": -31136, "Name": "_SCRIPT", "Description": null, "ActionString": "<b><a>$AND$<parts><medicare>$AND$$AND$", "UserEntry": "medicare parts A and B", "SpeakerList": "", "Status": false, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31135, "Name": "Medicare Parts A B", "Description": null, "ActionString": "[medicare+part%+amb|b|sandy:2]!50%[medicare+part%+amb|b|sandy:2]!90$OR$[medicare+part%+a|and|be|e|say+b|be|d|e|p|we:2]!50%[medicare+part%+a|and|be|e|say+b|be|d|e|p|we:2]!90$OR$$OR$", "UserEntry": "(\"medicare part* A|and|be|E|say B|be|D|E|P|we\":2{90} OR \"medicare part* A|and|be|E|say B|be|D|E|P|we\":2{50%}) OR (\"medicare part* amb|B|sandy\":2{90} OR \"medicare part* amb|B|sandy\":2{50%})", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}, {"Id": -31032, "Name": "Medicare A B", "Description": null, "ActionString": "[medicare+a+b:2]!50%[medicare+a+b:2]!90$OR$", "UserEntry": "\"medicare A B\":2{90} OR \"medicare A B\":2{50%}", "SpeakerList": "", "Status": true, "Weight": 1.0, "CountMultiple": false, "DisplayOrder": 0, "Error": null, "Filters": [], "SlnInstallationID": null, "SemanticType": "EQL", "Threshold": null, "MoveTo": null, "MoveToForce": null, "MoveAway": null, "MoveAwayForce": null}], "LastModified": "2024-12-06T12:55:29.95", "ComponentCount": 3}